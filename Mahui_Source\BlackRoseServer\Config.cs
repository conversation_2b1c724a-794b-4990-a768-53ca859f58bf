﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlackRoseServer
{
    public class Config
    {
        public static Config Instance { get; set; } = new Config();

        [Description("开启回合结束时开友伤")]
        public bool EnableFriendlyFire { get; set; } = true;

        [Description("名字结构")]
        public string NickStructure { get; set; } = "[Lv.%lvl%]%name%";

        [Description("MySQL数据库连接字符串")]
        public string DatabaseConnectionString { get; set; } = "server=localhost;database=mahuiserver;uid=mahuiserver;pwd=****************;charset=utf8mb4;";

        [Description("启用数据库功能")]
        public bool EnableDatabaseFeatures { get; set; } = true;

        [Description("启用音效功能")]
        public bool EnableAudioEffects { get; set; } = false;

        [Description("击杀音效文件路径")]
        public string KillSoundPath { get; set; } = @"C:\Users\<USER>\AppData\Roaming\SCP Secret Laboratory\LabAPI\configs\yx.ogg";

        [Description("获取经验音效文件路径")]
        public string ExperienceGainSoundPath { get; set; } = @"C:\Users\<USER>\AppData\Roaming\SCP Secret Laboratory\LabAPI\configs\yx.ogg";

        [Description("音效音量 (0-100)")]
        public float AudioVolume { get; set; } = 80f;

        [Description("启用击杀音效")]
        public bool EnableKillSound { get; set; } = true;

        [Description("启用获取经验音效")]
        public bool EnableExperienceGainSound { get; set; } = true;



        // ===== 计时器显示功能配置 =====

        [Description("是否启用重生计时器显示")]
        public bool EnableRespawnTimers { get; set; } = true;

        [Description("是否显示回合进行时长")]
        public bool EnableRoundTimer { get; set; } = true;

        [Description("计时器之间的间距（em单位）")]
        public int TimerSpacing { get; set; } = 16;

        // ===== 游戏平衡配置 =====

        [Description("是否启用站立回血功能")]
        public bool EnableStandingRegeneration { get; set; } = false;

        [Description("站立回血速度（每秒回血量）")]
        public float StandingRegenerationRate { get; set; } = 1.0f;

        [Description("站立回血最大血量百分比（0-1）")]
        public float StandingRegenerationMaxPercent { get; set; } = 0.8f;

        [Description("是否启用SCP血量调整")]
        public bool EnableSCPHealthAdjustment { get; set; } = false;

        [Description("SCP血量倍率")]
        public float SCPHealthMultiplier { get; set; } = 1.2f;

        // ===== 游戏功能开关 =====

        [Description("是否启用无限子弹")]
        public bool EnableInfiniteAmmo { get; set; } = false;

        [Description("是否启用保安下班功能")]
        public bool EnableGuardOffDuty { get; set; } = true;

        [Description("是否启用出生保护")]
        public bool EnableSpawnProtection { get; set; } = true;

        [Description("出生保护时间（秒）")]
        public float SpawnProtectionDuration { get; set; } = 10f;

        [Description("出生保护移动距离阈值（米，超过此距离将取消保护）")]
        public float SpawnProtectionMoveThreshold { get; set; } = 3f;
    }
}
