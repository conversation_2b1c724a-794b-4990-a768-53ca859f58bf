using System;
using LabApi.Features.Console;

namespace BlackRoseServer.API
{
    /// <summary>
    /// 经验和等级计算器
    /// 实现原MySQL存储过程UpdateXP的逻辑
    /// </summary>
    public static class ExperienceCalculator
    {
        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        /// <param name="experience">经验值</param>
        /// <returns>对应的等级</returns>
        public static int CalculateLevel(int experience)
        {
            if (experience <= 0)
                return 1;

            int level = 1;
            int totalExp = 0;

            // 逐级计算，直到经验值不足升级
            while (true)
            {
                int requiredExp = GetRequiredExperienceForLevel(level);
                if (totalExp + requiredExp > experience)
                    break;

                totalExp += requiredExp;
                level++;

                // 防止无限循环
                if (level > 10000)
                    break;
            }

            return level;
        }

        /// <summary>
        /// 获取指定等级升级所需的经验值
        /// </summary>
        /// <param name="level">当前等级</param>
        /// <returns>升级所需经验值</returns>
        public static int GetRequiredExperienceForLevel(int level)
        {
            if (level < 10)
                return 10;
            else if (level < 50)
                return 30;
            else if (level < 100)
                return 50;
            else if (level < 300)
                return 100;
            else if (level < 500)
                return 200;
            else if (level < 1000)
                return 500;
            else
                return 1000;
        }

        /// <summary>
        /// 处理玩家升级逻辑
        /// 基于原MySQL存储过程UpdateXP的逻辑
        /// </summary>
        /// <param name="playerData">玩家数据</param>
        /// <returns>是否发生了升级</returns>
        public static bool ProcessLevelUp(PlayerData playerData)
        {
            if (playerData == null)
            {
                Logger.Error("ProcessLevelUp: playerData为null");
                return false;
            }

            bool leveledUp = false;
            int originalLevel = playerData.Level;
            
            try
            {
                // 持续检查是否可以升级，直到经验不足为止
                while (true)
                {
                    int requiredExp = GetRequiredExperienceForLevel(playerData.Level);
                    
                    if (playerData.Experience >= requiredExp)
                    {
                        // 升级
                        playerData.Level++;
                        playerData.Experience -= requiredExp;
                        leveledUp = true;
                        
                        Logger.Debug($"玩家升级: Level {playerData.Level - 1} -> {playerData.Level}, 剩余经验: {playerData.Experience}");
                    }
                    else
                    {
                        // 经验不足，停止升级
                        break;
                    }
                    
                    // 防止无限循环的安全检查
                    if (playerData.Level > 10000)
                    {
                        Logger.Warn($"玩家等级异常过高: {playerData.Level}，停止升级处理");
                        break;
                    }
                }

                if (leveledUp)
                {
                    playerData.LastUpdated = DateTime.Now;
                    Logger.Info($"玩家 {playerData.NickName} 从 {originalLevel} 级升级到 {playerData.Level} 级");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"ProcessLevelUp处理失败: {ex.Message}");
                Logger.Debug($"ProcessLevelUp详细错误: {ex}");
            }

            return leveledUp;
        }

        /// <summary>
        /// 计算从当前等级到目标等级需要的总经验
        /// </summary>
        /// <param name="fromLevel">起始等级</param>
        /// <param name="toLevel">目标等级</param>
        /// <returns>需要的总经验值</returns>
        public static int CalculateTotalExperienceRequired(int fromLevel, int toLevel)
        {
            if (toLevel <= fromLevel)
                return 0;

            int totalExp = 0;
            for (int level = fromLevel; level < toLevel; level++)
            {
                totalExp += GetRequiredExperienceForLevel(level);
            }
            return totalExp;
        }

        /// <summary>
        /// 获取玩家到下一级所需的经验值
        /// </summary>
        /// <param name="playerData">玩家数据</param>
        /// <returns>到下一级所需经验值</returns>
        public static int GetExperienceToNextLevel(PlayerData playerData)
        {
            if (playerData == null)
                return 0;

            int requiredExp = GetRequiredExperienceForLevel(playerData.Level);
            return Math.Max(0, requiredExp - playerData.Experience);
        }

        /// <summary>
        /// 获取玩家当前等级的升级进度百分比
        /// </summary>
        /// <param name="playerData">玩家数据</param>
        /// <returns>进度百分比 (0-100)</returns>
        public static float GetLevelProgress(PlayerData playerData)
        {
            if (playerData == null)
                return 0f;

            int requiredExp = GetRequiredExperienceForLevel(playerData.Level);
            if (requiredExp <= 0)
                return 100f;

            return Math.Min(100f, (float)playerData.Experience / requiredExp * 100f);
        }

        /// <summary>
        /// 验证玩家数据的合理性
        /// </summary>
        /// <param name="playerData">玩家数据</param>
        /// <returns>数据是否合理</returns>
        public static bool ValidatePlayerData(PlayerData playerData)
        {
            if (playerData == null)
                return false;

            // 检查基本数据范围
            if (playerData.Level < 1 || playerData.Level > 10000)
            {
                Logger.Warn($"玩家 {playerData.NickName} 等级异常: {playerData.Level}");
                return false;
            }

            if (playerData.Experience < 0)
            {
                Logger.Warn($"玩家 {playerData.NickName} 经验值异常: {playerData.Experience}");
                return false;
            }

            return true;
        }
    }
}
