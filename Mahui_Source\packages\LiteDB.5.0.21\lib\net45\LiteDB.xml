<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LiteDB</name>
    </assembly>
    <members>
        <member name="M:LiteDB.LiteCollection`1.Count">
            <summary>
            Get document count in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Count(LiteDB.BsonExpression)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Count(System.String,LiteDB.BsonDocument)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Count(System.String,LiteDB.BsonValue[])">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Count(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Count(LiteDB.Query)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount">
            <summary>
            Get document count in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount(LiteDB.BsonExpression)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount(System.String,LiteDB.BsonDocument)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount(System.String,LiteDB.BsonValue[])">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.LongCount(LiteDB.Query)">
            <summary>
            Get document count in collection using predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Exists(LiteDB.BsonExpression)">
            <summary>
            Get true if collection contains at least 1 document that satisfies the predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Exists(System.String,LiteDB.BsonDocument)">
            <summary>
            Get true if collection contains at least 1 document that satisfies the predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Exists(System.String,LiteDB.BsonValue[])">
            <summary>
            Get true if collection contains at least 1 document that satisfies the predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Exists(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Get true if collection contains at least 1 document that satisfies the predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Exists(LiteDB.Query)">
            <summary>
            Get true if collection contains at least 1 document that satisfies the predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Min(LiteDB.BsonExpression)">
            <summary>
            Returns the min value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Min">
            <summary>
            Returns the min value of _id index
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Min``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Returns the min value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Max(LiteDB.BsonExpression)">
            <summary>
            Returns the max value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Max">
            <summary>
            Returns the max _id index key value
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Max``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Returns the last/max field using a linq expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Delete(LiteDB.BsonValue)">
            <summary>
            Delete a single document on collection based on _id index. Returns true if document was deleted
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DeleteAll">
            <summary>
            Delete all documents inside collection. Returns how many documents was deleted. Run inside current transaction
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DeleteMany(LiteDB.BsonExpression)">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DeleteMany(System.String,LiteDB.BsonDocument)">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DeleteMany(System.String,LiteDB.BsonValue[])">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Query">
            <summary>
            Return a new LiteQueryable to build more complex queries
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Find(LiteDB.BsonExpression,System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Find(LiteDB.Query,System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using query definition.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Find(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindById(LiteDB.BsonValue)">
            <summary>
            Find a document using Document Id. Returns null if not found.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindOne(LiteDB.BsonExpression)">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindOne(System.String,LiteDB.BsonDocument)">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindOne(LiteDB.BsonExpression,LiteDB.BsonValue[])">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindOne(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindOne(LiteDB.Query)">
            <summary>
            Find the first document using defined query structure. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.FindAll">
            <summary>
            Returns all documents inside collection order by _id index.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Include``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Run an include action in each document returned by Find(), FindById(), FindOne() and All() methods to load DbRef documents
            Returns a new Collection with this action included
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Include(LiteDB.BsonExpression)">
            <summary>
            Run an include action in each document returned by Find(), FindById(), FindOne() and All() methods to load DbRef documents
            Returns a new Collection with this action included
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.EnsureIndex(System.String,LiteDB.BsonExpression,System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
        </member>
        <member name="M:LiteDB.LiteCollection`1.EnsureIndex(LiteDB.BsonExpression,System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="expression">Document field/expression</param>
            <param name="unique">If is a unique index</param>
        </member>
        <member name="M:LiteDB.LiteCollection`1.EnsureIndex``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
        </member>
        <member name="M:LiteDB.LiteCollection`1.EnsureIndex``1(System.String,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
        </member>
        <member name="M:LiteDB.LiteCollection`1.GetIndexExpression``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Get index expression based on LINQ expression. Convert IEnumerable in MultiKey indexes
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.DropIndex(System.String)">
            <summary>
            Drop index and release slot for another index
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Insert(`0)">
            <summary>
            Insert a new entity to this collection. Document Id must be a new value in collection - Returns document Id
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Insert(LiteDB.BsonValue,`0)">
            <summary>
            Insert a new document to this collection using passed id value.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Insert(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Insert an array of new documents to this collection. Document Id must be a new value in collection. Can be set buffer size to commit at each N documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.InsertBulk(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Implements bulk insert documents in a collection. Usefull when need lots of documents.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.GetBsonDocs(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Convert each T document in a BsonDocument, setting autoId for each one
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.RemoveDocId(LiteDB.BsonDocument)">
            <summary>
            Remove document _id if contains a "empty" value (checks for autoId bson type)
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Update(`0)">
            <summary>
            Update a document in this collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Update(LiteDB.BsonValue,`0)">
            <summary>
            Update a document in this collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Update(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.UpdateMany(LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Update many documents based on transform expression. This expression must return a new document that will be replaced over current document (according with predicate).
            Eg: col.UpdateMany("{ Name: UPPER($.Name), Age }", "_id > 0")
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.UpdateMany(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Update many document based on merge current document with extend expression. Use your class with initializers. 
            Eg: col.UpdateMany(x => new Customer { Name = x.Name.ToUpper(), Salary: 100 }, x => x.Name == "John")
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Upsert(`0)">
            <summary>
            Insert or Update a document in this collection.
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Upsert(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Insert or Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteCollection`1.Upsert(LiteDB.BsonValue,`0)">
            <summary>
            Insert or Update a document in this collection.
            </summary>
        </member>
        <member name="P:LiteDB.LiteCollection`1.Name">
            <summary>
            Get collection name
            </summary>
        </member>
        <member name="P:LiteDB.LiteCollection`1.AutoId">
            <summary>
            Get collection auto id type
            </summary>
        </member>
        <member name="P:LiteDB.LiteCollection`1.EntityMapper">
            <summary>
            Getting entity mapper from current collection. Returns null if collection are BsonDocument type
            </summary>
        </member>
        <member name="P:LiteDB.ILiteCollection`1.Name">
            <summary>
            Get collection name
            </summary>
        </member>
        <member name="P:LiteDB.ILiteCollection`1.AutoId">
            <summary>
            Get collection auto id type
            </summary>
        </member>
        <member name="P:LiteDB.ILiteCollection`1.EntityMapper">
            <summary>
            Getting entity mapper from current collection. Returns null if collection are BsonDocument type
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Include``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Run an include action in each document returned by Find(), FindById(), FindOne() and All() methods to load DbRef documents
            Returns a new Collection with this action included
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Include(LiteDB.BsonExpression)">
            <summary>
            Run an include action in each document returned by Find(), FindById(), FindOne() and All() methods to load DbRef documents
            Returns a new Collection with this action included
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Upsert(`0)">
            <summary>
            Insert or Update a document in this collection.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Upsert(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Insert or Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Upsert(LiteDB.BsonValue,`0)">
            <summary>
            Insert or Update a document in this collection.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Update(`0)">
            <summary>
            Update a document in this collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Update(LiteDB.BsonValue,`0)">
            <summary>
            Update a document in this collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Update(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.UpdateMany(LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Update many documents based on transform expression. This expression must return a new document that will be replaced over current document (according with predicate).
            Eg: col.UpdateMany("{ Name: UPPER($.Name), Age }", "_id > 0")
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.UpdateMany(System.Linq.Expressions.Expression{System.Func{`0,`0}},System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Update many document based on merge current document with extend expression. Use your class with initializers. 
            Eg: col.UpdateMany(x => new Customer { Name = x.Name.ToUpper(), Salary: 100 }, x => x.Name == "John")
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Insert(`0)">
            <summary>
            Insert a new entity to this collection. Document Id must be a new value in collection - Returns document Id
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Insert(LiteDB.BsonValue,`0)">
            <summary>
            Insert a new document to this collection using passed id value.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Insert(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Insert an array of new documents to this collection. Document Id must be a new value in collection. Can be set buffer size to commit at each N documents
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.InsertBulk(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Implements bulk insert documents in a collection. Usefull when need lots of documents.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.EnsureIndex(System.String,LiteDB.BsonExpression,System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.EnsureIndex(LiteDB.BsonExpression,System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="expression">Document field/expression</param>
            <param name="unique">If is a unique index</param>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.EnsureIndex``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.EnsureIndex``1(System.String,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DropIndex(System.String)">
            <summary>
            Drop index and release slot for another index
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Query">
            <summary>
            Return a new LiteQueryable to build more complex queries
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Find(LiteDB.BsonExpression,System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Find(LiteDB.Query,System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using query definition.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Find(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32,System.Int32)">
            <summary>
            Find documents inside a collection using predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindById(LiteDB.BsonValue)">
            <summary>
            Find a document using Document Id. Returns null if not found.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindOne(LiteDB.BsonExpression)">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindOne(System.String,LiteDB.BsonDocument)">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindOne(LiteDB.BsonExpression,LiteDB.BsonValue[])">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindOne(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Find the first document using predicate expression. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindOne(LiteDB.Query)">
            <summary>
            Find the first document using defined query structure. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.FindAll">
            <summary>
            Returns all documents inside collection order by _id index.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Delete(LiteDB.BsonValue)">
            <summary>
            Delete a single document on collection based on _id index. Returns true if document was deleted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DeleteAll">
            <summary>
            Delete all documents inside collection. Returns how many documents was deleted. Run inside current transaction
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DeleteMany(LiteDB.BsonExpression)">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DeleteMany(System.String,LiteDB.BsonDocument)">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DeleteMany(System.String,LiteDB.BsonValue[])">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.DeleteMany(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Delete all documents based on predicate expression. Returns how many documents was deleted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count">
            <summary>
            Get document count using property on collection.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count(LiteDB.BsonExpression)">
            <summary>
            Count documents matching a query. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count(System.String,LiteDB.BsonDocument)">
            <summary>
            Count documents matching a query. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count(System.String,LiteDB.BsonValue[])">
            <summary>
            Count documents matching a query. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Count(LiteDB.Query)">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount">
            <summary>
            Get document count using property on collection.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount(LiteDB.BsonExpression)">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount(System.String,LiteDB.BsonDocument)">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount(System.String,LiteDB.BsonValue[])">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.LongCount(LiteDB.Query)">
            <summary>
            Count documents matching a query. This method does not deserialize any documents. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Exists(LiteDB.BsonExpression)">
            <summary>
            Returns true if query returns any document. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Exists(System.String,LiteDB.BsonDocument)">
            <summary>
            Returns true if query returns any document. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Exists(System.String,LiteDB.BsonValue[])">
            <summary>
            Returns true if query returns any document. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Exists(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Returns true if query returns any document. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Exists(LiteDB.Query)">
            <summary>
            Returns true if query returns any document. This method does not deserialize any document. Needs indexes on query expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Min(LiteDB.BsonExpression)">
            <summary>
            Returns the min value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Min">
            <summary>
            Returns the min value of _id index
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Min``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Returns the min value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Max(LiteDB.BsonExpression)">
            <summary>
            Returns the max value from specified key value in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Max">
            <summary>
            Returns the max _id index key value
            </summary>
        </member>
        <member name="M:LiteDB.ILiteCollection`1.Max``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Returns the last/max field using a linq expression
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.Mapper">
            <summary>
            Get current instance of BsonMapper used in this database instance (can be BsonMapper.Global)
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.FileStorage">
            <summary>
            Returns a special collection for storage files/stream inside datafile. Use _files and _chunks collection names. FileId is implemented as string. Use "GetStorage" for custom options
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetCollection``1(System.String,LiteDB.BsonAutoId)">
            <summary>
            Get a collection using a entity class as strong typed document. If collection does not exits, create a new one.
            </summary>
            <param name="name">Collection name (case insensitive)</param>
            <param name="autoId">Define autoId data type (when object contains no id field)</param>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetCollection``1">
            <summary>
            Get a collection using a name based on typeof(T).Name (BsonMapper.ResolveCollectionName function)
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetCollection``1(LiteDB.BsonAutoId)">
            <summary>
            Get a collection using a name based on typeof(T).Name (BsonMapper.ResolveCollectionName function)
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetCollection(System.String,LiteDB.BsonAutoId)">
            <summary>
            Get a collection using a generic BsonDocument. If collection does not exits, create a new one.
            </summary>
            <param name="name">Collection name (case insensitive)</param>
            <param name="autoId">Define autoId data type (when document contains no _id field)</param>
        </member>
        <member name="M:LiteDB.ILiteDatabase.BeginTrans">
            <summary>
            Initialize a new transaction. Transaction are created "per-thread". There is only one single transaction per thread.
            Return true if transaction was created or false if current thread already in a transaction.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Commit">
            <summary>
            Commit current transaction
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Rollback">
            <summary>
            Rollback current transaction
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetStorage``1(System.String,System.String)">
            <summary>
            Get new instance of Storage using custom FileId type, custom "_files" collection name and custom "_chunks" collection. LiteDB support multiples file storages (using different files/chunks collection names)
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.GetCollectionNames">
            <summary>
            Get all collections name inside this database.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.CollectionExists(System.String)">
            <summary>
            Checks if a collection exists on database. Collection name is case insensitive
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.DropCollection(System.String)">
            <summary>
            Drop a collection and all data + indexes
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.RenameCollection(System.String,System.String)">
            <summary>
            Rename a collection. Returns false if oldName does not exists or newName already exists
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Execute(System.IO.TextReader,LiteDB.BsonDocument)">
            <summary>
            Execute SQL commands and return as data reader.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Execute(System.String,LiteDB.BsonDocument)">
            <summary>
            Execute SQL commands and return as data reader
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Execute(System.String,LiteDB.BsonValue[])">
            <summary>
            Execute SQL commands and return as data reader
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Checkpoint">
            <summary>
            Do database checkpoint. Copy all commited transaction from log file into datafile.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Rebuild(LiteDB.Engine.RebuildOptions)">
            <summary>
            Rebuild all database to remove unused pages - reduce data file
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Pragma(System.String)">
            <summary>
            Get value from internal engine variables
            </summary>
        </member>
        <member name="M:LiteDB.ILiteDatabase.Pragma(System.String,LiteDB.BsonValue)">
            <summary>
            Set new value to internal engine variables
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.UserVersion">
            <summary>
            Get/Set database user version - use this version number to control database change model
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.Timeout">
            <summary>
            Get/Set database timeout - this timeout is used to wait for unlock using transactions
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.UtcDate">
            <summary>
            Get/Set if database will deserialize dates in UTC timezone or Local timezone (default: Local)
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.LimitSize">
            <summary>
            Get/Set database limit size (in bytes). New value must be equals or larger than current database size
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.CheckpointSize">
            <summary>
            Get/Set in how many pages (8 Kb each page) log file will auto checkpoint (copy from log file to data file). Use 0 to manual-only checkpoint (and no checkpoint on dispose)
            Default: 1000 pages
            </summary>
        </member>
        <member name="P:LiteDB.ILiteDatabase.Collation">
            <summary>
            Get database collection (this options can be changed only in rebuild proces)
            </summary>
        </member>
        <member name="P:LiteDB.ILiteRepository.Database">
            <summary>
            Get database instance
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Insert``1(``0,System.String)">
            <summary>
            Insert a new document into collection. Document Id must be a new value in collection - Returns document Id
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Insert``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Insert an array of new documents into collection. Document Id must be a new value in collection. Can be set buffer size to commit at each N documents
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Update``1(``0,System.String)">
            <summary>
            Update a document into collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Update``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Upsert``1(``0,System.String)">
            <summary>
            Insert or Update a document based on _id key. Returns true if insert entity or false if update entity
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Upsert``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Insert or Update all documents based on _id key. Returns entity count that was inserted
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Delete``1(LiteDB.BsonValue,System.String)">
            <summary>
            Delete entity based on _id key
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.DeleteMany``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Delete entity based on Query
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.DeleteMany``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Delete entity based on predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Query``1(System.String)">
            <summary>
            Returns new instance of LiteQueryable that provides all method to query any entity inside collection. Use fluent API to apply filter/includes an than run any execute command, like ToList() or First()
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.EnsureIndex``1(System.String,LiteDB.BsonExpression,System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.ILiteRepository.EnsureIndex``1(LiteDB.BsonExpression,System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.ILiteRepository.EnsureIndex``2(System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.ILiteRepository.EnsureIndex``2(System.String,System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.ILiteRepository.SingleById``1(LiteDB.BsonValue,System.String)">
            <summary>
            Search for a single instance of T by Id. Shortcut from Query.SingleById
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Fetch``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).ToList();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Fetch``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).ToList();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.First``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).First();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.First``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).First();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.FirstOrDefault``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).FirstOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.FirstOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).FirstOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Single``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).Single();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.Single``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).Single();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.SingleOrDefault``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).SingleOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.ILiteRepository.SingleOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).SingleOrDefault();
            </summary>
        </member>
        <member name="T:LiteDB.LiteDatabase">
            <summary>
            The LiteDB database. Used for create a LiteDB instance and use all storage resources. It's the database connection
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.Mapper">
            <summary>
            Get current instance of BsonMapper used in this database instance (can be BsonMapper.Global)
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.#ctor(System.String,LiteDB.BsonMapper)">
            <summary>
            Starts LiteDB database using a connection string for file system database
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.#ctor(LiteDB.ConnectionString,LiteDB.BsonMapper)">
            <summary>
            Starts LiteDB database using a connection string for file system database
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.#ctor(System.IO.Stream,LiteDB.BsonMapper,System.IO.Stream)">
            <summary>
            Starts LiteDB database using a generic Stream implementation (mostly MemoryStream).
            </summary>
            <param name="stream">DataStream reference </param>
            <param name="mapper">BsonMapper mapper reference</param>
            <param name="logStream">LogStream reference </param>
        </member>
        <member name="M:LiteDB.LiteDatabase.#ctor(LiteDB.Engine.ILiteEngine,LiteDB.BsonMapper,System.Boolean)">
            <summary>
            Start LiteDB database using a pre-exiting engine. When LiteDatabase instance dispose engine instance will be disposed too
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetCollection``1(System.String,LiteDB.BsonAutoId)">
            <summary>
            Get a collection using an entity class as strong typed document. If collection does not exist, create a new one.
            </summary>
            <param name="name">Collection name (case insensitive)</param>
            <param name="autoId">Define autoId data type (when object contains no id field)</param>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetCollection``1">
            <summary>
            Get a collection using a name based on typeof(T).Name (BsonMapper.ResolveCollectionName function)
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetCollection``1(LiteDB.BsonAutoId)">
            <summary>
            Get a collection using a name based on typeof(T).Name (BsonMapper.ResolveCollectionName function)
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetCollection(System.String,LiteDB.BsonAutoId)">
            <summary>
            Get a collection using a generic BsonDocument. If collection does not exist, create a new one.
            </summary>
            <param name="name">Collection name (case insensitive)</param>
            <param name="autoId">Define autoId data type (when document contains no _id field)</param>
        </member>
        <member name="M:LiteDB.LiteDatabase.BeginTrans">
            <summary>
            Initialize a new transaction. Transaction are created "per-thread". There is only one single transaction per thread.
            Return true if transaction was created or false if current thread already in a transaction.
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Commit">
            <summary>
            Commit current transaction
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Rollback">
            <summary>
            Rollback current transaction
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.FileStorage">
            <summary>
            Returns a special collection for storage files/stream inside datafile. Use _files and _chunks collection names. FileId is implemented as string. Use "GetStorage" for custom options
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetStorage``1(System.String,System.String)">
            <summary>
            Get new instance of Storage using custom FileId type, custom "_files" collection name and custom "_chunks" collection. LiteDB support multiples file storages (using different files/chunks collection names)
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.GetCollectionNames">
            <summary>
            Get all collections name inside this database.
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.CollectionExists(System.String)">
            <summary>
            Checks if a collection exists on database. Collection name is case insensitive
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.DropCollection(System.String)">
            <summary>
            Drop a collection and all data + indexes
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.RenameCollection(System.String,System.String)">
            <summary>
            Rename a collection. Returns false if oldName does not exists or newName already exists
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Execute(System.IO.TextReader,LiteDB.BsonDocument)">
            <summary>
            Execute SQL commands and return as data reader.
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Execute(System.String,LiteDB.BsonDocument)">
            <summary>
            Execute SQL commands and return as data reader
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Execute(System.String,LiteDB.BsonValue[])">
            <summary>
            Execute SQL commands and return as data reader
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Checkpoint">
            <summary>
            Do database checkpoint. Copy all commited transaction from log file into datafile.
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Rebuild(LiteDB.Engine.RebuildOptions)">
            <summary>
            Rebuild all database to remove unused pages - reduce data file
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Pragma(System.String)">
            <summary>
            Get value from internal engine variables
            </summary>
        </member>
        <member name="M:LiteDB.LiteDatabase.Pragma(System.String,LiteDB.BsonValue)">
            <summary>
            Set new value to internal engine variables
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.UserVersion">
            <summary>
            Get/Set database user version - use this version number to control database change model
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.Timeout">
            <summary>
            Get/Set database timeout - this timeout is used to wait for unlock using transactions
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.UtcDate">
            <summary>
            Get/Set if database will deserialize dates in UTC timezone or Local timezone (default: Local)
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.LimitSize">
            <summary>
            Get/Set database limit size (in bytes). New value must be equals or larger than current database size
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.CheckpointSize">
            <summary>
            Get/Set in how many pages (8 Kb each page) log file will auto checkpoint (copy from log file to data file). Use 0 to manual-only checkpoint (and no checkpoint on dispose)
            Default: 1000 pages
            </summary>
        </member>
        <member name="P:LiteDB.LiteDatabase.Collation">
            <summary>
            Get database collection (this options can be changed only in rebuild proces)
            </summary>
        </member>
        <member name="T:LiteDB.LiteQueryable`1">
            <summary>
            An IQueryable-like class to write fluent query in documents in collection.
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Include``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Load cross reference documents from path expression (DbRef reference)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Include(LiteDB.BsonExpression)">
            <summary>
            Load cross reference documents from path expression (DbRef reference)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Include(System.Collections.Generic.List{LiteDB.BsonExpression})">
            <summary>
            Load cross reference documents from path expression (DbRef reference)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Where(LiteDB.BsonExpression)">
            <summary>
            Filters a sequence of documents based on a predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Where(System.String,LiteDB.BsonDocument)">
            <summary>
            Filters a sequence of documents based on a predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Where(System.String,LiteDB.BsonValue[])">
            <summary>
            Filters a sequence of documents based on a predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Where(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Filters a sequence of documents based on a predicate expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.OrderBy(LiteDB.BsonExpression,System.Int32)">
            <summary>
            Sort the documents of resultset in ascending (or descending) order according to a key (support only one OrderBy)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.OrderBy``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Int32)">
            <summary>
            Sort the documents of resultset in ascending (or descending) order according to a key (support only one OrderBy)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.OrderByDescending(LiteDB.BsonExpression)">
            <summary>
            Sort the documents of resultset in descending order according to a key (support only one OrderBy)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.OrderByDescending``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Sort the documents of resultset in descending order according to a key (support only one OrderBy)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.GroupBy(LiteDB.BsonExpression)">
            <summary>
            Groups the documents of resultset according to a specified key selector expression (support only one GroupBy)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Having(LiteDB.BsonExpression)">
            <summary>
            Filter documents after group by pipe according to predicate expression (requires GroupBy and support only one Having)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Select(LiteDB.BsonExpression)">
            <summary>
            Transform input document into a new output document. Can be used with each document, group by or all source
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Select``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Project each document of resultset into a new document/value based on selector expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ForUpdate">
            <summary>
            Execute query locking collection in write mode. This is avoid any other thread change results after read document and before transaction ends
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Offset(System.Int32)">
            <summary>
            Bypasses a specified number of documents in resultset and retun the remaining documents (same as Skip)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Skip(System.Int32)">
            <summary>
            Bypasses a specified number of documents in resultset and retun the remaining documents (same as Offset)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Limit(System.Int32)">
            <summary>
            Return a specified number of contiguous documents from start of resultset
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ExecuteReader">
            <summary>
            Execute query and returns resultset as generic BsonDataReader
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ToDocuments">
            <summary>
            Execute query and return resultset as IEnumerable of documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ToEnumerable">
            <summary>
            Execute query and return resultset as IEnumerable of T. If T is a ValueType or String, return values only (not documents)
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ToList">
            <summary>
            Execute query and return results as a List
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.ToArray">
            <summary>
            Execute query and return results as an Array
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.GetPlan">
            <summary>
            Get execution plan over current query definition to see how engine will execute query
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Single">
            <summary>
            Returns the only document of resultset, and throw an exception if there not exactly one document in the sequence
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.SingleOrDefault">
            <summary>
            Returns the only document of resultset, or null if resultset are empty; this method throw an exception if there not exactly one document in the sequence
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.First">
            <summary>
            Returns first document of resultset
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.FirstOrDefault">
            <summary>
            Returns first document of resultset or null if resultset are empty
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Count">
            <summary>
            Execute Count methos in filter query
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.LongCount">
            <summary>
            Execute Count methos in filter query
            </summary>
        </member>
        <member name="M:LiteDB.LiteQueryable`1.Exists">
            <summary>
            Returns true/false if query returns any result
            </summary>
        </member>
        <member name="T:LiteDB.LiteRepository">
            <summary>
            The LiteDB repository pattern. A simple way to access your documents in a single class with fluent query api
            </summary>
        </member>
        <member name="P:LiteDB.LiteRepository.Database">
            <summary>
            Get database instance
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.#ctor(LiteDB.ILiteDatabase)">
            <summary>
            Starts LiteDB database an existing Database instance
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.#ctor(System.String,LiteDB.BsonMapper)">
            <summary>
            Starts LiteDB database using a connection string for file system database
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.#ctor(LiteDB.ConnectionString,LiteDB.BsonMapper)">
            <summary>
            Starts LiteDB database using a connection string for file system database
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.#ctor(System.IO.Stream,LiteDB.BsonMapper,System.IO.Stream)">
            <summary>
            Starts LiteDB database using a Stream disk
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Insert``1(``0,System.String)">
            <summary>
            Insert a new document into collection. Document Id must be a new value in collection - Returns document Id
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Insert``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Insert an array of new documents into collection. Document Id must be a new value in collection. Can be set buffer size to commit at each N documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Update``1(``0,System.String)">
            <summary>
            Update a document into collection. Returns false if not found document in collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Update``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Update all documents
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Upsert``1(``0,System.String)">
            <summary>
            Insert or Update a document based on _id key. Returns true if insert entity or false if update entity
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Upsert``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Insert or Update all documents based on _id key. Returns entity count that was inserted
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Delete``1(LiteDB.BsonValue,System.String)">
            <summary>
            Delete entity based on _id key
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.DeleteMany``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Delete entity based on Query
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.DeleteMany``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Delete entity based on predicate filter expression
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Query``1(System.String)">
            <summary>
            Returns new instance of LiteQueryable that provides all method to query any entity inside collection. Use fluent API to apply filter/includes an than run any execute command, like ToList() or First()
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.EnsureIndex``1(System.String,LiteDB.BsonExpression,System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.LiteRepository.EnsureIndex``1(LiteDB.BsonExpression,System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already. Returns true if index was created or false if already exits
            </summary>
            <param name="expression">Create a custom expression function to be indexed</param>
            <param name="unique">If is a unique index</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.LiteRepository.EnsureIndex``2(System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.LiteRepository.EnsureIndex``2(System.String,System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Boolean,System.String)">
            <summary>
            Create a new permanent index in all documents inside this collections if index not exists already.
            </summary>
            <param name="name">Index name - unique name for this collection</param>
            <param name="keySelector">LinqExpression to be converted into BsonExpression to be indexed</param>
            <param name="unique">Create a unique keys index?</param>
            <param name="collectionName">Collection Name</param>
        </member>
        <member name="M:LiteDB.LiteRepository.SingleById``1(LiteDB.BsonValue,System.String)">
            <summary>
            Search for a single instance of T by Id. Shortcut from Query.SingleById
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Fetch``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).ToList();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Fetch``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).ToList();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.First``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).First();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.First``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).First();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.FirstOrDefault``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).FirstOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.FirstOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).FirstOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Single``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).Single();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.Single``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).Single();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.SingleOrDefault``1(LiteDB.BsonExpression,System.String)">
            <summary>
            Execute Query[T].Where(predicate).SingleOrDefault();
            </summary>
        </member>
        <member name="M:LiteDB.LiteRepository.SingleOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.String)">
            <summary>
            Execute Query[T].Where(predicate).SingleOrDefault();
            </summary>
        </member>
        <member name="T:LiteDB.BsonCtorAttribute">
            <summary>
            Indicate which constructor method will be used in this entity
            </summary>
        </member>
        <member name="T:LiteDB.BsonFieldAttribute">
            <summary>
            Set a name to this property in BsonDocument
            </summary>
        </member>
        <member name="T:LiteDB.BsonIdAttribute">
            <summary>
            Indicate that property will be used as BsonDocument Id
            </summary>
        </member>
        <member name="T:LiteDB.BsonIgnoreAttribute">
            <summary>
            Indicate that property will not be persist in Bson serialization
            </summary>
        </member>
        <member name="T:LiteDB.BsonRefAttribute">
            <summary>
            Indicate that field are not persisted inside this document but it's a reference for another document (DbRef)
            </summary>
        </member>
        <member name="T:LiteDB.BsonMapper">
            <summary>
            Class that converts your entity class to/from BsonDocument
            If you prefer use a new instance of BsonMapper (not Global), be sure cache this instance for better performance
            Serialization rules:
                - Classes must be "public" with a public constructor (without parameters)
                - Properties must have public getter (can be read-only)
                - Entity class must have Id property, [ClassName]Id property or [BsonId] attribute
                - No circular references
                - Fields are not valid
                - IList, Array supports
                - IDictionary supports (Key must be a simple datatype - converted by ChangeType)
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper._entities">
            <summary>
            Mapping cache between Class/BsonDocument
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper._customSerializer">
            <summary>
            Map serializer/deserialize for custom types
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper._typeInstantiator">
            <summary>
            Type instantiator function to support IoC
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper._typeNameBinder">
            <summary>
            Type name binder to control how type names are serialized to BSON documents
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper.Global">
            <summary>
            Global instance used when no BsonMapper are passed in LiteDatabase ctor
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper.ResolveFieldName">
            <summary>
            A resolver name for field
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.SerializeNullValues">
            <summary>
            Indicate that mapper do not serialize null values (default false)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.TrimWhitespace">
            <summary>
            Apply .Trim() in strings when serialize (default true)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.EmptyStringToNull">
            <summary>
            Convert EmptyString to Null (default true)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.EnumAsInteger">
            <summary>
            Get/Set if enum must be converted into Integer value. If false, enum will be converted into String value.
            MUST BE "true" to support LINQ expressions (default false)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.IncludeFields">
            <summary>
            Get/Set that mapper must include fields (default: false)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.IncludeNonPublic">
            <summary>
            Get/Set that mapper must include non public (private, protected and internal) (default: false)
            </summary>
        </member>
        <member name="P:LiteDB.BsonMapper.MaxDepth">
            <summary>
            Get/Set maximum depth for nested object (default 20)
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper.ResolveMember">
            <summary>
            A custom callback to change MemberInfo behavior when converting to MemberMapper.
            Use mapper.ResolveMember(Type entity, MemberInfo property, MemberMapper documentMappedField)
            Set FieldName to null if you want remove from mapped document
            </summary>
        </member>
        <member name="F:LiteDB.BsonMapper.ResolveCollectionName">
            <summary>
            Custom resolve name collection based on Type 
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.RegisterType``1(System.Func{``0,LiteDB.BsonValue},System.Func{LiteDB.BsonValue,``0})">
            <summary>
            Register a custom type serializer/deserialize function
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.RegisterType(System.Type,System.Func{System.Object,LiteDB.BsonValue},System.Func{LiteDB.BsonValue,System.Object})">
            <summary>
            Register a custom type serializer/deserialize function
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.Entity``1">
            <summary>
            Map your entity class to BsonDocument using fluent API
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetExpression``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Resolve LINQ expression into BsonExpression
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetIndexExpression``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Resolve LINQ expression into BsonExpression (for index only)
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.UseCamelCase">
            <summary>
            Use lower camel case resolution for convert property names to field names
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.UseLowerCaseDelimiter(System.Char)">
            <summary>
            Uses lower camel case with delimiter to convert property names to field names
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetEntityMapper(System.Type)">
            <summary>
            Get property mapper between typed .NET class and BsonDocument - Cache results
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.BuildAddEntityMapper(System.Type)">
            <summary>
            Use this method to override how your class can be, by default, mapped from entity to Bson document.
            Returns an EntityMapper from each requested Type
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetIdMember(System.Collections.Generic.IEnumerable{System.Reflection.MemberInfo})">
            <summary>
            Gets MemberInfo that refers to Id from a document object.
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetTypeMembers(System.Type)">
            <summary>
            Returns all member that will be have mapper between POCO class to document
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.GetTypeCtor(LiteDB.EntityMapper)">
            <summary>
            Get best construtor to use to initialize this entity.
            - Look if contains [BsonCtor] attribute
            - Look for parameterless ctor
            - Look for first contructor with parameter and use BsonDocument to send RawValue
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.RegisterDbRef(LiteDB.BsonMapper,LiteDB.MemberMapper,LiteDB.ITypeNameBinder,System.String)">
            <summary>
            Register a property mapper as DbRef to serialize/deserialize only document reference _id
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.RegisterDbRefItem(LiteDB.BsonMapper,LiteDB.MemberMapper,LiteDB.ITypeNameBinder,System.String)">
            <summary>
            Register a property as a DbRef - implement a custom Serialize/Deserialize actions to convert entity to $id, $ref only
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.RegisterDbRefList(LiteDB.BsonMapper,LiteDB.MemberMapper,LiteDB.ITypeNameBinder,System.String)">
            <summary>
            Register a property as a DbRefList - implement a custom Serialize/Deserialize actions to convert entity to $id, $ref only
            </summary>
        </member>
        <member name="T:LiteDB.BsonMapper.DeserializationCallback">
            <summary>
            Delegate for deserialization callback.
            </summary>
            <param name="sender">The BsonMapper instance that triggered the deserialization.</param>
            <param name="target">The target type for deserialization.</param>
            <param name="value">The BsonValue to be deserialized.</param>
            <returns>The deserialized BsonValue.</returns>
        </member>
        <member name="P:LiteDB.BsonMapper.OnDeserialization">
            <summary>
            Gets called before deserialization of a value
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.ToObject(System.Type,LiteDB.BsonDocument)">
            <summary>
            Deserialize a BsonDocument to entity class
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.ToObject``1(LiteDB.BsonDocument)">
            <summary>
            Deserialize a BsonDocument to entity class
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.Deserialize``1(LiteDB.BsonValue)">
            <summary>
            Deserialize a BsonValue to .NET object typed in T
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.Deserialize(System.Type,LiteDB.BsonValue)">
            <summary>
            Deserilize a BsonValue to .NET object based on type parameter
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.ToDocument(System.Type,System.Object)">
            <summary>
            Serialize a entity class to BsonDocument
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.ToDocument``1(``0)">
            <summary>
            Serialize a entity class to BsonDocument
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.Serialize``1(``0)">
            <summary>
            Serialize to BsonValue any .NET object based on T type (using mapping rules)
            </summary>
        </member>
        <member name="M:LiteDB.BsonMapper.Serialize(System.Type,System.Object)">
            <summary>
            Serialize to BsonValue any .NET object based on type parameter (using mapping rules)
            </summary>
        </member>
        <member name="T:LiteDB.EntityBuilder`1">
            <summary>
            Helper class to modify your entity mapping to document. Can be used instead attribute decorates
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.Ignore``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Define which property will not be mapped to document
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.Field``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
            <summary>
            Define a custom name for a property when mapping to document
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.Id``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean)">
            <summary>
            Define which property is your document id (primary key). Define if this property supports auto-id
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.Ctor(System.Func{LiteDB.BsonDocument,`0})">
            <summary>
            Define which property is your document id (primary key). Define if this property supports auto-id
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.DbRef``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
            <summary>
            Define a subdocument (or a list of) as a reference
            </summary>
        </member>
        <member name="M:LiteDB.EntityBuilder`1.GetMember``2(System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Action{LiteDB.MemberMapper})">
            <summary>
            Get a property based on a expression. Eg.: 'x => x.UserId' return string "UserId"
            </summary>
        </member>
        <member name="T:LiteDB.EntityMapper">
            <summary>
            Class to map entity class to BsonDocument
            </summary>
        </member>
        <member name="P:LiteDB.EntityMapper.ForType">
            <summary>
            Indicate which Type this entity mapper is
            </summary>
        </member>
        <member name="P:LiteDB.EntityMapper.Members">
            <summary>
            List all type members that will be mapped to/from BsonDocument
            </summary>
        </member>
        <member name="P:LiteDB.EntityMapper.Id">
            <summary>
            Indicate which member is _id
            </summary>
        </member>
        <member name="P:LiteDB.EntityMapper.CreateInstance">
            <summary>
            Get/Set a custom ctor function to create new entity instance
            </summary>
        </member>
        <member name="M:LiteDB.EntityMapper.GetMember(System.Linq.Expressions.Expression)">
            <summary>
            Resolve expression to get member mapped
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitLambda``1(System.Linq.Expressions.Expression{``0})">
            <summary>
            Visit :: `x => x.Customer.Name`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitInvocation(System.Linq.Expressions.InvocationExpression)">
            <summary>
            Visit lambda invocation
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitParameter(System.Linq.Expressions.ParameterExpression)">
            <summary>
            Visit :: x => `x`.Customer.Name
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitMember(System.Linq.Expressions.MemberExpression)">
            <summary>
            Visit :: x => x.`Customer.Name`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)">
            <summary>
            Visit :: x => x.Customer.Name.`ToUpper()`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitConstant(System.Linq.Expressions.ConstantExpression)">
            <summary>
            Visit :: x => x.Age + `10` (will create parameter:  `p0`, `p1`, ...)
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitUnary(System.Linq.Expressions.UnaryExpression)">
            <summary>
            Visit :: x => `!x.Active`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitNew(System.Linq.Expressions.NewExpression)">
            <summary>
            Visit :: x => `new { x.Id, x.Name }`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitMemberInit(System.Linq.Expressions.MemberInitExpression)">
            <summary>
            Visit :: x => `new MyClass { Id = 10 }`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitNewArray(System.Linq.Expressions.NewArrayExpression)">
            <summary>
            Visit :: x => `new int[] { 1, 2, 3 }`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitBinary(System.Linq.Expressions.BinaryExpression)">
            <summary>
            Visit :: x => x.Id `+` 10
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitConditional(System.Linq.Expressions.ConditionalExpression)">
            <summary>
            Visit :: x => `x.Id > 0 ? "ok" : "not-ok"`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitCoalesce(System.Linq.Expressions.BinaryExpression)">
            <summary>
            Visit :: x => `x.FirstName ?? x.LastName`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitArrayIndex(System.Linq.Expressions.BinaryExpression)">
            <summary>
            Visit :: x => `x.Items[5]`
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.ResolvePattern(System.String,System.Linq.Expressions.Expression,System.Collections.Generic.IList{System.Linq.Expressions.Expression})">
            <summary>
            Resolve string pattern using an object + N arguments. Will write over _builder
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitEnumerablePredicate(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Resolve Enumerable predicate when using Any/All enumerable extensions
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.GetOperator(System.Linq.Expressions.ExpressionType)">
            <summary>
            Get string operator from an Binary expression
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.ResolveMember(System.Reflection.MemberInfo)">
            <summary>
            Returns document field name for some type member
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.IsMethodIndexEval(System.Linq.Expressions.MethodCallExpression,System.Linq.Expressions.Expression@,System.Linq.Expressions.Expression@)">
            <summary>
            Define if this method is index access and must eval index value (do not use parameter)
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.VisitAsPredicate(System.Linq.Expressions.Expression,System.Boolean)">
            <summary>
            Visit expression but, if ensurePredicate = true, force expression be a predicate (appending ` = true`)
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.Evaluate(System.Linq.Expressions.Expression,System.Type[])">
            <summary>
            Compile and execute expression (can be cached)
            </summary>
        </member>
        <member name="M:LiteDB.LinqExpressionVisitor.TryGetResolver(System.Type,LiteDB.ITypeResolver@)">
            <summary>
            Try find a Type Resolver for declaring type
            </summary>
        </member>
        <member name="T:LiteDB.ParameterExpressionVisitor">
            <summary>
            Class used to test in an Expression member expression is based on parameter `x => x.Name` or variable `x => externalVar`
            </summary>
        </member>
        <member name="T:LiteDB.MemberMapper">
            <summary>
            Internal representation for a .NET member mapped to BsonDocument
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.AutoId">
            <summary>
            If member is Id, indicate that are AutoId
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.MemberName">
            <summary>
            Member name
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.DataType">
            <summary>
            Member returns data type
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.FieldName">
            <summary>
            Converted document field name
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.Getter">
            <summary>
            Delegate method to get value from entity instance
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.Setter">
            <summary>
            Delegate method to set value to entity instance
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.Serialize">
            <summary>
            When used, can be define a serialization function from entity class to bson value
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.Deserialize">
            <summary>
            When used, can define a deserialization function from bson value
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.IsDbRef">
            <summary>
            Is this property an DbRef? Must implement Serialize/Deserialize delegates
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.IsEnumerable">
            <summary>
            Indicate that this property contains an list of elements (IEnumerable)
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.UnderlyingType">
            <summary>
            When property is an array of items, gets underlying type (otherwise is same type of PropertyType)
            </summary>
        </member>
        <member name="P:LiteDB.MemberMapper.IsIgnore">
            <summary>
            Is this property ignore
            </summary>
        </member>
        <member name="T:LiteDB.Reflection">
            <summary>
            Helper class to get entity properties and map as BsonValue
            </summary>
            <summary>
            Using Expressions is the easy and fast way to create classes, structs, get/set fields/properties. But it not works in NET35
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.CreateInstance(System.Type)">
            <summary>
            Create a new instance from a Type
            </summary>
        </member>
        <member name="F:LiteDB.Reflection.ConvertType">
            <summary>
            Get a list from all acepted data type to property converter BsonValue
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.UnderlyingTypeOf(System.Type)">
            <summary>
            Get underlying get - using to get inner Type from Nullable type
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.GetListItemType(System.Type)">
            <summary>
            Get item type from a generic List or Array
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.IsEnumerable(System.Type)">
            <summary>
            Returns true if Type is any kind of Array/IList/ICollection/....
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.IsSimpleType(System.Type)">
            <summary>
            Return if type is simple value
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.IsCollection(System.Type)">
            <summary>
            Returns true if Type implement ICollection (like List, HashSet)
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.IsDictionary(System.Type)">
            <summary>
            Returns if Type is a generic Dictionary
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.SelectMember(System.Collections.Generic.IEnumerable{System.Reflection.MemberInfo},System.Func{System.Reflection.MemberInfo,System.Boolean}[])">
            <summary>
            Select member from a list of member using predicate order function to select
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.MethodName(System.Reflection.MethodInfo,System.Int32)">
            <summary>
            Get a friendly method name with parameter types
            </summary>
        </member>
        <member name="M:LiteDB.Reflection.FriendlyTypeName(System.Type)">
            <summary>
            Get C# friendly primitive type names
            </summary>
        </member>
        <member name="F:LiteDB.DefaultTypeNameBinder._disallowedTypeNames">
            <summary>
            Contains all well known vulnerable types according to ysoserial.net
            </summary>
        </member>
        <member name="M:LiteDB.SharedEngine.OpenDatabase">
            <summary>
            Open database in safe mode
            </summary>
        </member>
        <member name="M:LiteDB.SharedEngine.CloseDatabase">
            <summary>
            Dequeue stack and dispose database on empty stack
            </summary>
        </member>
        <member name="T:LiteDB.SqlParser">
            <summary>
            Internal class to parse and execute sql-like commands
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseBegin">
            <summary>
            BEGIN [ TRANS | TRANSACTION ]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseCheckpoint">
            <summary>
            CHECKPOINT
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseCommit">
            <summary>
            COMMIT [ TRANS | TRANSACTION ]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseCreate">
            <summary>
            CREATE [ UNIQUE ] INDEX {indexName} ON {collection} ({indexExpr})
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseDelete">
            <summary>
            DELETE {collection} WHERE {whereExpr}
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseDrop">
            <summary>
            DROP INDEX {collection}.{indexName}
            DROP COLLECTION {collection}
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseInsert">
            <summary>
            INSERT INTO {collection} VALUES {doc0} [, {docN}] [ WITH ID={type} ] ]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseWithAutoId">
            <summary>
            Parse :[type] for AutoId (just after collection name)
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseListOfExpressions">
            <summary>
            {expr0}, {expr1}, ..., {exprN}
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseListOfDocuments">
            <summary>
            {doc0}, {doc1}, ..., {docN} {EOF|;}
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParsePragma">
            <summary>
            PRAGMA [DB_PARAM] = VALUE
            PRAGMA [DB_PARAM]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseRebuild">
            <summary>
            SHRINK
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseRename">
            <summary>
            RENAME COLLECTION {collection} TO {newName}
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseRollback">
            <summary>
            ROLLBACK [ TRANS | TRANSACTION ]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseSelect">
            <summary>
            [ EXPLAIN ]
               SELECT {selectExpr}
               [ INTO {newcollection|$function} [ : {autoId} ] ]
               [ FROM {collection|$function} ]
            [ INCLUDE {pathExpr0} [, {pathExprN} ]
              [ WHERE {filterExpr} ]
              [ GROUP BY {groupByExpr} ]
             [ HAVING {filterExpr} ]
              [ ORDER BY {orderByExpr} [ ASC | DESC ] ]
              [ LIMIT {number} ]
             [ OFFSET {number} ]
                [ FOR UPDATE ]
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseCollection(LiteDB.Tokenizer)">
            <summary>
            Read collection name and parameter (in case of system collections)
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseCollection(LiteDB.Tokenizer,System.String@,LiteDB.BsonValue@)">
            <summary>
            Read collection name and parameter (in case of system collections)
            </summary>
        </member>
        <member name="M:LiteDB.SqlParser.ParseUpdate">
            <summary>
            UPDATE - update documents - if used with {key} = {exprValue} will merge current document with this fields
                     if used with { key: value } will replace current document with new document
             UPDATE {collection}
                SET [{key} = {exprValue}, {key} = {exprValue} | { newDoc }]
            [ WHERE {whereExpr} ]
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.FindById(`0)">
            <summary>
            Find a file inside datafile and returns LiteFileInfo instance. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Find(LiteDB.BsonExpression)">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Find(System.String,LiteDB.BsonDocument)">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Find(System.String,LiteDB.BsonValue[])">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Find(System.Linq.Expressions.Expression{System.Func{LiteDB.LiteFileInfo{`0},System.Boolean}})">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.FindAll">
            <summary>
            Find all files inside file collections
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Exists(`0)">
            <summary>
            Returns if a file exisits in database
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.OpenWrite(`0,System.String,LiteDB.BsonDocument)">
            <summary>
            Open/Create new file storage and returns linked Stream to write operations.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Upload(`0,System.String,System.IO.Stream,LiteDB.BsonDocument)">
            <summary>
            Upload a file based on stream data
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Upload(`0,System.String)">
            <summary>
            Upload a file based on file system data
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.SetMetadata(`0,LiteDB.BsonDocument)">
            <summary>
            Update metadata on a file. File must exist.
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.OpenRead(`0)">
            <summary>
            Load data inside storage and returns as Stream
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Download(`0,System.IO.Stream)">
            <summary>
            Copy all file content to a steam
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Download(`0,System.String,System.Boolean)">
            <summary>
            Copy all file content to a file
            </summary>
        </member>
        <member name="M:LiteDB.ILiteStorage`1.Delete(`0)">
            <summary>
            Delete a file inside datafile and all metadata related
            </summary>
        </member>
        <member name="T:LiteDB.LiteFileInfo`1">
            <summary>
            Represents a file inside storage collection
            </summary>
        </member>
        <member name="M:LiteDB.LiteFileInfo`1.OpenRead">
            <summary>
            Open file stream to read from database
            </summary>
        </member>
        <member name="M:LiteDB.LiteFileInfo`1.OpenWrite">
            <summary>
            Open file stream to write to database
            </summary>
        </member>
        <member name="M:LiteDB.LiteFileInfo`1.CopyTo(System.IO.Stream)">
            <summary>
            Copy file content to another stream
            </summary>
        </member>
        <member name="M:LiteDB.LiteFileInfo`1.SaveAs(System.String,System.Boolean)">
            <summary>
            Save file content to a external file
            </summary>
        </member>
        <member name="F:LiteDB.LiteFileStream`1.MAX_CHUNK_SIZE">
            <summary>
            Number of bytes on each chunk document to store
            </summary>
        </member>
        <member name="P:LiteDB.LiteFileStream`1.FileInfo">
            <summary>
            Get file information
            </summary>
        </member>
        <member name="M:LiteDB.LiteFileStream`1.WriteChunks(System.Boolean)">
            <summary>
            Consume all _buffer bytes and write to chunk collection
            </summary>
        </member>
        <member name="T:LiteDB.LiteStorage`1">
            <summary>
            Storage is a special collection to store files and streams.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.FindById(`0)">
            <summary>
            Find a file inside datafile and returns LiteFileInfo instance. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Find(LiteDB.BsonExpression)">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Find(System.String,LiteDB.BsonDocument)">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Find(System.String,LiteDB.BsonValue[])">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Find(System.Linq.Expressions.Expression{System.Func{LiteDB.LiteFileInfo{`0},System.Boolean}})">
            <summary>
            Find all files that match with predicate expression.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.FindAll">
            <summary>
            Find all files inside file collections
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Exists(`0)">
            <summary>
            Returns if a file exisits in database
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.OpenWrite(`0,System.String,LiteDB.BsonDocument)">
            <summary>
            Open/Create new file storage and returns linked Stream to write operations.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Upload(`0,System.String,System.IO.Stream,LiteDB.BsonDocument)">
            <summary>
            Upload a file based on stream data
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Upload(`0,System.String)">
            <summary>
            Upload a file based on file system data
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.SetMetadata(`0,LiteDB.BsonDocument)">
            <summary>
            Update metadata on a file. File must exist.
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.OpenRead(`0)">
            <summary>
            Load data inside storage and returns as Stream
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Download(`0,System.IO.Stream)">
            <summary>
            Copy all file content to a steam
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Download(`0,System.String,System.Boolean)">
            <summary>
            Copy all file content to a file
            </summary>
        </member>
        <member name="M:LiteDB.LiteStorage`1.Delete(`0)">
            <summary>
            Delete a file inside datafile and all metadata related
            </summary>
        </member>
        <member name="T:LiteDB.ConnectionString">
            <summary>
            Manage ConnectionString to connect and create databases. Connection string are NameValue using Name1=Value1; Name2=Value2
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Connection">
            <summary>
            "connection": Return how engine will be open (default: Direct)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Filename">
            <summary>
            "filename": Full path or relative path from DLL directory
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Password">
            <summary>
            "password": Database password used to encrypt/decypted data pages
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.InitialSize">
            <summary>
            "initial size": If database is new, initialize with allocated space - support KB, MB, GB (default: 0)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.ReadOnly">
            <summary>
            "readonly": Open datafile in readonly mode (default: false)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Upgrade">
            <summary>
            "upgrade": Check if data file is an old version and convert before open (default: false)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.AutoRebuild">
            <summary>
            "auto-rebuild": If last close database exception result a invalid data state, rebuild datafile on next open (default: false)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Collation">
            <summary>
            "collation": Set default collaction when database creation (default: "[CurrentCulture]/IgnoreCase")
            </summary>
        </member>
        <member name="M:LiteDB.ConnectionString.#ctor">
            <summary>
            Initialize empty connection string
            </summary>
        </member>
        <member name="M:LiteDB.ConnectionString.#ctor(System.String)">
            <summary>
            Initialize connection string parsing string in "key1=value1;key2=value2;...." format or only "filename" as default (when no ; char found)
            </summary>
        </member>
        <member name="P:LiteDB.ConnectionString.Item(System.String)">
            <summary>
            Get value from parsed connection string. Returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.ConnectionString.CreateEngine(System.Action{LiteDB.Engine.EngineSettings})">
            <summary>
            Create ILiteEngine instance according string connection parameters. For now, only Local/Shared are supported
            </summary>
        </member>
        <member name="T:LiteDB.Query">
            <summary>
            Class is a result from optimized QueryBuild. Indicate how engine must run query - there is no more decisions to engine made, must only execute as query was defined
            </summary>
            <summary>
            Represent full query options
            </summary>
        </member>
        <member name="F:LiteDB.Query.Ascending">
            <summary>
            Indicate when a query must execute in ascending order
            </summary>
        </member>
        <member name="F:LiteDB.Query.Descending">
            <summary>
            Indicate when a query must execute in descending order
            </summary>
        </member>
        <member name="M:LiteDB.Query.All">
            <summary>
            Returns all documents
            </summary>
        </member>
        <member name="M:LiteDB.Query.All(System.Int32)">
            <summary>
            Returns all documents
            </summary>
        </member>
        <member name="M:LiteDB.Query.All(System.String,System.Int32)">
            <summary>
            Returns all documents
            </summary>
        </member>
        <member name="M:LiteDB.Query.EQ(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents that value are equals to value (=)
            </summary>
        </member>
        <member name="M:LiteDB.Query.LT(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents that value are less than value (&lt;)
            </summary>
        </member>
        <member name="M:LiteDB.Query.LTE(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents that value are less than or equals value (&lt;=)
            </summary>
        </member>
        <member name="M:LiteDB.Query.GT(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all document that value are greater than value (&gt;)
            </summary>
        </member>
        <member name="M:LiteDB.Query.GTE(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents that value are greater than or equals value (&gt;=)
            </summary>
        </member>
        <member name="M:LiteDB.Query.Between(System.String,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns all document that values are between "start" and "end" values (BETWEEN)
            </summary>
        </member>
        <member name="M:LiteDB.Query.StartsWith(System.String,System.String)">
            <summary>
            Returns all documents that starts with value (LIKE)
            </summary>
        </member>
        <member name="M:LiteDB.Query.Contains(System.String,System.String)">
            <summary>
            Returns all documents that contains value (CONTAINS) - string Contains
            </summary>
        </member>
        <member name="M:LiteDB.Query.Not(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents that are not equals to value (not equals)
            </summary>
        </member>
        <member name="M:LiteDB.Query.In(System.String,LiteDB.BsonArray)">
            <summary>
            Returns all documents that has value in values list (IN)
            </summary>
        </member>
        <member name="M:LiteDB.Query.In(System.String,LiteDB.BsonValue[])">
            <summary>
            Returns all documents that has value in values list (IN)
            </summary>
        </member>
        <member name="M:LiteDB.Query.In(System.String,System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns all documents that has value in values list (IN)
            </summary>
        </member>
        <member name="M:LiteDB.Query.Any">
            <summary>
            Get all operands to works with array or enumerable values
            </summary>
        </member>
        <member name="M:LiteDB.Query.And(LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Returns document that exists in BOTH queries results. If both queries has indexes, left query has index preference (other side will be run in full scan)
            </summary>
        </member>
        <member name="M:LiteDB.Query.And(LiteDB.BsonExpression[])">
            <summary>
            Returns document that exists in ALL queries results.
            </summary>
        </member>
        <member name="M:LiteDB.Query.Or(LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Returns documents that exists in ANY queries results (Union).
            </summary>
        </member>
        <member name="M:LiteDB.Query.Or(LiteDB.BsonExpression[])">
            <summary>
            Returns document that exists in ANY queries results (Union).
            </summary>
        </member>
        <member name="M:LiteDB.Query.ToSQL(System.String)">
            <summary>
            [ EXPLAIN ]
               SELECT {selectExpr}
               [ INTO {newcollection|$function} [ : {autoId} ] ]
               [ FROM {collection|$function} ]
            [ INCLUDE {pathExpr0} [, {pathExprN} ]
              [ WHERE {filterExpr} ]
              [ GROUP BY {groupByExpr} ]
             [ HAVING {filterExpr} ]
              [ ORDER BY {orderByExpr} [ ASC | DESC ] ]
              [ LIMIT {number} ]
             [ OFFSET {number} ]
                [ FOR UPDATE ]
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.EQ(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields is equal to value
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.LT(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are less tha to value (&lt;)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.LTE(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are less than or equals value (&lt;=)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.GT(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are greater than value (&gt;)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.GTE(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are greater than or equals value (&gt;=)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.Between(System.String,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are between "start" and "end" values (BETWEEN)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.StartsWith(System.String,System.String)">
            <summary>
            Returns all documents for which at least one value in arrayFields starts with value (LIKE)
            </summary>
        </member>
        <member name="M:LiteDB.QueryAny.Not(System.String,LiteDB.BsonValue)">
            <summary>
            Returns all documents for which at least one value in arrayFields are not equals to value (not equals)
            </summary>
        </member>
        <member name="T:LiteDB.BsonAutoId">
            <summary>
            All supported BsonTypes supported in AutoId insert operation
            </summary>
        </member>
        <member name="P:LiteDB.BsonDocument.RawId">
            <summary>
            Get/Set position of this document inside database. It's filled when used in Find operation.
            </summary>
        </member>
        <member name="P:LiteDB.BsonDocument.Item(System.String)">
            <summary>
            Get/Set a field for document. Fields are case sensitive
            </summary>
        </member>
        <member name="M:LiteDB.BsonDocument.GetElements">
            <summary>
            Get all document elements - Return "_id" as first of all (if exists)
            </summary>
        </member>
        <member name="T:LiteDB.BsonType">
            <summary>
            All supported BsonTypes in sort order
            </summary>
        </member>
        <member name="T:LiteDB.BsonValue">
            <summary>
            Represent a Bson Value used in BsonDocument
            </summary>
        </member>
        <member name="F:LiteDB.BsonValue.Null">
            <summary>
            Represent a Null bson type
            </summary>
        </member>
        <member name="F:LiteDB.BsonValue.MinValue">
            <summary>
            Represent a MinValue bson type
            </summary>
        </member>
        <member name="F:LiteDB.BsonValue.MaxValue">
            <summary>
            Represent a MaxValue bson type
            </summary>
        </member>
        <member name="M:LiteDB.BsonValue.DbRef(LiteDB.BsonValue,System.String)">
            <summary>
            Create a new document used in DbRef => { $id: id, $ref: collection }
            </summary>
        </member>
        <member name="P:LiteDB.BsonValue.Type">
            <summary>
            Indicate BsonType of this BsonValue
            </summary>
        </member>
        <member name="P:LiteDB.BsonValue.RawValue">
            <summary>
            Get internal .NET value object
            </summary>
        </member>
        <member name="P:LiteDB.BsonValue.Item(System.String)">
            <summary>
            Get/Set a field for document. Fields are case sensitive - Works only when value are document
            </summary>
        </member>
        <member name="P:LiteDB.BsonValue.Item(System.Int32)">
            <summary>
            Get/Set value in array position. Works only when value are array
            </summary>
        </member>
        <member name="M:LiteDB.BsonValue.GetBytesCount(System.Boolean)">
            <summary>
            Returns how many bytes this BsonValue will consume when converted into binary BSON
            If recalc = false, use cached length value (from Array/Document only)
            </summary>
        </member>
        <member name="M:LiteDB.BsonValue.GetBytesCountElement(System.String,LiteDB.BsonValue)">
            <summary>
            Get how many bytes one single element will used in BSON format
            </summary>
        </member>
        <member name="T:LiteDB.BsonSerializer">
            <summary>
            Class to call method for convert BsonDocument to/from byte[] - based on http://bsonspec.org/spec.html
            In v5 this class use new BufferRead/Writer to work with byte[] segments. This class are just a shortchut
            </summary>
        </member>
        <member name="M:LiteDB.BsonSerializer.Serialize(LiteDB.BsonDocument)">
            <summary>
            Serialize BsonDocument into a binary array
            </summary>
        </member>
        <member name="M:LiteDB.BsonSerializer.Deserialize(System.Byte[],System.Boolean,System.Collections.Generic.HashSet{System.String})">
            <summary>
            Deserialize binary data into BsonDocument
            </summary>
        </member>
        <member name="T:LiteDB.BsonDataReader">
            <summary>
            Class to read void, one or a collection of BsonValues. Used in SQL execution commands and query returns. Use local data source (IEnumerable[BsonDocument])
            </summary>
        </member>
        <member name="M:LiteDB.BsonDataReader.#ctor">
            <summary>
            Initialize with no value
            </summary>
        </member>
        <member name="M:LiteDB.BsonDataReader.#ctor(LiteDB.BsonValue,System.String)">
            <summary>
            Initialize with a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonDataReader.#ctor(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},System.String,LiteDB.Engine.EngineState)">
            <summary>
            Initialize with an IEnumerable data source
            </summary>
        </member>
        <member name="P:LiteDB.BsonDataReader.HasValues">
            <summary>
            Return if has any value in result
            </summary>
        </member>
        <member name="P:LiteDB.BsonDataReader.Current">
            <summary>
            Return current value
            </summary>
        </member>
        <member name="P:LiteDB.BsonDataReader.Collection">
            <summary>
            Return collection name
            </summary>
        </member>
        <member name="M:LiteDB.BsonDataReader.Read">
            <summary>
            Move cursor to next result. Returns true if read was possible
            </summary>
        </member>
        <member name="T:LiteDB.BsonDataReaderExtensions">
            <summary>
            Implement some Enumerable methods to IBsonDataReader
            </summary>
        </member>
        <member name="T:LiteDB.BsonExpressionEnumerableDelegate">
            <summary>
            Delegate function to get compiled enumerable expression
            </summary>
        </member>
        <member name="T:LiteDB.BsonExpressionScalarDelegate">
            <summary>
            Delegate function to get compiled scalar expression
            </summary>
        </member>
        <member name="T:LiteDB.BsonExpression">
            <summary>
            Compile and execute string expressions using BsonDocuments. Used in all document manipulation (transform, filter, indexes, updates). See https://github.com/mbdavid/LiteDB/wiki/Expressions
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Source">
            <summary>
            Get formatted expression
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Type">
            <summary>
            Indicate expression type
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsImmutable">
            <summary>
            If true, this expression do not change if same document/paramter are passed (only few methods change - like NOW() - or parameters)
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Parameters">
            <summary>
            Get/Set parameter values that will be used on expression execution
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Left">
            <summary>
            In predicate expressions, indicate Left side
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Right">
            <summary>
            In predicate expressions, indicate Rigth side
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.UseSource">
            <summary>
            Get/Set this expression (or any inner expression) use global Source (*)
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Expression">
            <summary>
            Get transformed LINQ expression
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Fields">
            <summary>
            Fill this hashset with all fields used in root level of document (be used to partial deserialize) - "$" means all fields
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsScalar">
            <summary>
            Indicate if this expressions returns a single value or IEnumerable value
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsPredicate">
            <summary>
            Indicate that expression evaluate to TRUE or FALSE (=, >, ...). OR and AND are not considered Predicate expressions
            Predicate expressions must have Left/Right expressions
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsIndexable">
            <summary>
            This expression can be indexed? To index some expression must contains fields (at least 1) and
            must use only immutable methods and no parameters
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsValue">
            <summary>
            This expression has no dependency of BsonDocument so can be used as user value (when select index)
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.IsANY">
            <summary>
            Indicate when predicate expression uses ANY keywork for filter array items
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpression._funcEnumerable">
            <summary>
            Compiled Expression into a function to be executed: func(source[], root, current, parameters)[]
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpression._funcScalar">
            <summary>
            Compiled Expression into a scalar function to be executed: func(source[], root, current, parameters)1
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.DefaultFieldName">
            <summary>
            Get default field name when need convert simple BsonValue into BsonDocument
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.#ctor">
            <summary>
            Only internal ctor (from BsonParserExpression)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.op_Implicit(LiteDB.BsonExpression)~System.String">
            <summary>
            Implicit string converter
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.op_Implicit(System.String)~LiteDB.BsonExpression">
            <summary>
            Implicit string converter
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Execute(LiteDB.Collation)">
            <summary>
            Execute expression with an empty document (used only for resolve math/functions).
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Execute(LiteDB.BsonDocument,LiteDB.Collation)">
            <summary>
            Execute expression and returns IEnumerable values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Execute(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.Collation)">
            <summary>
            Execute expression and returns IEnumerable values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Execute(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonDocument,LiteDB.BsonValue,LiteDB.Collation)">
            <summary>
            Execute expression and returns IEnumerable values - returns NULL if no elements
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.GetIndexKeys(LiteDB.BsonDocument,LiteDB.Collation)">
            <summary>
            Execute expression over document to get all index keys. 
            Return distinct value (no duplicate key to same document)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.ExecuteScalar(LiteDB.Collation)">
            <summary>
            Execute scalar expression with an blank document and empty source (used only for resolve math/functions).
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.ExecuteScalar(LiteDB.BsonDocument,LiteDB.Collation)">
            <summary>
            Execute scalar expression over single document and return a single value (or BsonNull when empty). Throws exception if expression are not scalar expression
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.ExecuteScalar(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.Collation)">
            <summary>
            Execute scalar expression over multiple documents and return a single value (or BsonNull when empty). Throws exception if expression are not scalar expression
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.ExecuteScalar(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonDocument,LiteDB.BsonValue,LiteDB.Collation)">
            <summary>
            Execute expression and returns IEnumerable values - returns NULL if no elements
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Create(System.String)">
            <summary>
            Parse string and create new instance of BsonExpression - can be cached
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Create(System.String,LiteDB.BsonValue[])">
            <summary>
            Parse string and create new instance of BsonExpression - can be cached
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Create(System.String,LiteDB.BsonDocument)">
            <summary>
            Parse string and create new instance of BsonExpression - can be cached
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.Create(LiteDB.Tokenizer,LiteDB.BsonExpressionParserMode,LiteDB.BsonDocument)">
            <summary>
            Parse tokenizer and create new instance of BsonExpression - for now, do not use cache
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.ParseAndCompile(LiteDB.Tokenizer,LiteDB.BsonExpressionParserMode,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Parse and compile string expression and return BsonExpression
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.SetParameters(LiteDB.BsonExpression,LiteDB.BsonDocument)">
            <summary>
            Set same parameter referente to all expression child (left, right)
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpression.Root">
            <summary>
            Get root document $ expression
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Methods">
            <summary>
            Get all registered methods for BsonExpressions
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpression._methods">
            <summary>
            Load all static methods from BsonExpressionMethods class. Use a dictionary using name + parameter count
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.GetMethod(System.String,System.Int32)">
            <summary>
            Get expression method with same name and same parameter - return null if not found
            </summary>
        </member>
        <member name="P:LiteDB.BsonExpression.Functions">
            <summary>
            Get all registered functions for BsonExpressions
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpression._functions">
            <summary>
            Load all static methods from BsonExpressionFunctions class. Use a dictionary using name + parameter count
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpression.GetFunction(System.String,System.Int32)">
            <summary>
            Get expression function with same name and same parameter - return null if not found
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.COUNT(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Count all values. Return a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MIN(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Find minimal value from all values (number values only). Return a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MAX(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Find max value from all values (number values only). Return a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.FIRST(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns first value from an list of values (scan all source)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LAST(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns last value from an list of values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.AVG(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Find average value from all values (number values only). Return a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SUM(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Sum all values (number values only). Return a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.ANY(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Return "true" if inner collection contains any result
            ANY($.items[*])
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MINVALUE">
            <summary>
            Return a new instance of MINVALUE
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.OBJECTID">
            <summary>
            Create a new OBJECTID value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.GUID">
            <summary>
            Create a new GUID value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.NOW">
            <summary>
            Return a new DATETIME (Now)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.NOW_UTC">
            <summary>
            Return a new DATETIME (UtcNow)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.TODAY">
            <summary>
            Return a new DATETIME (Today)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MAXVALUE">
            <summary>
            Return a new instance of MAXVALUE
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.INT32(LiteDB.BsonValue)">
            <summary>
            Convert values into INT32. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.INT64(LiteDB.BsonValue)">
            <summary>
            Convert values into INT64. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DOUBLE(LiteDB.Collation,LiteDB.BsonValue)">
            <summary>
            Convert values into DOUBLE. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DOUBLE(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Convert values into DOUBLE. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DECIMAL(LiteDB.Collation,LiteDB.BsonValue)">
            <summary>
            Convert values into DECIMAL. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DECIMAL(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Convert values into DECIMAL. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.STRING(LiteDB.BsonValue)">
            <summary>
            Convert value into STRING
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.ARRAY(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Return an array from list of values. Support multiple values but returns a single value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.BINARY(LiteDB.BsonValue)">
            <summary>
            Return an binary from string (base64) values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.OBJECTID(LiteDB.BsonValue)">
            <summary>
            Convert values into OBJECTID. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.GUID(LiteDB.BsonValue)">
            <summary>
            Convert values into GUID. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.BOOLEAN(LiteDB.BsonValue)">
            <summary>
            Return converted value into BOOLEAN value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME(LiteDB.Collation,LiteDB.BsonValue)">
            <summary>
            Convert values into DATETIME. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Convert values into DATETIME. Returns empty if not possible to convert. Support custom culture info
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME_UTC(LiteDB.Collation,LiteDB.BsonValue)">
            <summary>
            Convert values into DATETIME. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME_UTC(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Convert values into DATETIME. Returns empty if not possible to convert
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Create a new instance of DATETIME based on year, month, day (local time)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATETIME_UTC(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Create a new instance of DATETIME based on year, month, day (UTC)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_MINVALUE(LiteDB.BsonValue)">
            <summary>
            Return true if value is MINVALUE
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_NULL(LiteDB.BsonValue)">
            <summary>
            Return true if value is NULL
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_INT32(LiteDB.BsonValue)">
            <summary>
            Return true if value is INT32
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_INT64(LiteDB.BsonValue)">
            <summary>
            Return true if value is INT64
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_DOUBLE(LiteDB.BsonValue)">
            <summary>
            Return true if value is DOUBLE
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_DECIMAL(LiteDB.BsonValue)">
            <summary>
            Return true if value is DECIMAL
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_NUMBER(LiteDB.BsonValue)">
            <summary>
            Return true if value is NUMBER (int, double, decimal)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_STRING(LiteDB.BsonValue)">
            <summary>
            Return true if value is STRING
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_DOCUMENT(LiteDB.BsonValue)">
            <summary>
            Return true if value is DOCUMENT
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_ARRAY(LiteDB.BsonValue)">
            <summary>
            Return true if value is ARRAY
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_BINARY(LiteDB.BsonValue)">
            <summary>
            Return true if value is BINARY
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_OBJECTID(LiteDB.BsonValue)">
            <summary>
            Return true if value is OBJECTID
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_GUID(LiteDB.BsonValue)">
            <summary>
            Return true if value is GUID
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_BOOLEAN(LiteDB.BsonValue)">
            <summary>
            Return true if value is BOOLEAN
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_DATETIME(LiteDB.BsonValue)">
            <summary>
            Return true if value is DATETIME
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_MAXVALUE(LiteDB.BsonValue)">
            <summary>
            Return true if value is DATE (alias to DATETIME)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.INT(LiteDB.BsonValue)">
            <summary>
            Alias to INT32(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LONG(LiteDB.BsonValue)">
            <summary>
            Alias to INT64(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.BOOL(LiteDB.BsonValue)">
            <summary>
            Alias to BOOLEAN(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATE(LiteDB.Collation,LiteDB.BsonValue)">
            <summary>
            Alias to DATETIME(values) and DATETIME_UTC(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_INT(LiteDB.BsonValue)">
            <summary>
            Alias to IS_INT32(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_LONG(LiteDB.BsonValue)">
            <summary>
            Alias to IS_INT64(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_BOOL(LiteDB.BsonValue)">
            <summary>
            Alias to IS_BOOLEAN(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_DATE(LiteDB.BsonValue)">
            <summary>
            Alias to IS_DATE(values)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.YEAR(LiteDB.BsonValue)">
            <summary>
            Get year from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MONTH(LiteDB.BsonValue)">
            <summary>
            Get month from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DAY(LiteDB.BsonValue)">
            <summary>
            Get day from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.HOUR(LiteDB.BsonValue)">
            <summary>
            Get hour from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MINUTE(LiteDB.BsonValue)">
            <summary>
            Get minute from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SECOND(LiteDB.BsonValue)">
            <summary>
            Get seconds from date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATEADD(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Add an interval to date. Use dateInterval: "y" (or "year"), "M" (or "month"), "d" (or "day"), "h" (or "hour"), "m" (or "minute"), "s" or ("second")
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DATEDIFF(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns an interval about 2 dates. Use dateInterval: "y|year", "M|month", "d|day", "h|hour", "m|minute", "s|second"
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.TO_LOCAL(LiteDB.BsonValue)">
            <summary>
            Convert UTC date into LOCAL date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.TO_UTC(LiteDB.BsonValue)">
            <summary>
            Convert LOCAL date into UTC date
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.ABS(LiteDB.BsonValue)">
            <summary>
            Apply absolute value (ABS) method in all number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.ROUND(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Round number method in all number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.POW(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Implement POWER (x and y)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.JSON(LiteDB.BsonValue)">
            <summary>
            Parse a JSON string into a new BsonValue
            JSON('{a:1}') = {a:1}
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.EXTEND(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Create a new document and copy all properties from source document. Then copy properties (overritting if need) extend document
            Always returns a new document!
            EXTEND($, {a: 2}) = {_id:1, a: 2}
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.ITEMS(LiteDB.BsonValue)">
            <summary>
            Convert an array into IEnuemrable of values - If not array, returns as single yield value
            ITEMS([1, 2, null]) = 1, 2, null
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.CONCAT(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Concatenates 2 sequences into a new single sequence
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.KEYS(LiteDB.BsonValue)">
            <summary>
            Get all KEYS names from a document
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.VALUES(LiteDB.BsonValue)">
            <summary>
            Get all values from a document
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.OID_CREATIONTIME(LiteDB.BsonValue)">
            <summary>
            Return CreationTime from ObjectId value - returns null if not an ObjectId
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IIF(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Conditional IF statment. If condition are true, returns TRUE value, otherwise, FALSE value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.COALESCE(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Return first values if not null. If null, returns second value.
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LENGTH(LiteDB.BsonValue)">
            <summary>
            Return length of variant value (valid only for String, Binary, Array or Document [keys])
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.TOP(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},LiteDB.BsonValue)">
            <summary>
            Returns the first num elements of values.
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.UNION(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns the union of the two enumerables.
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.EXCEPT(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns the set difference between the two enumerables.
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.DISTINCT(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Returns a unique list of items
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.RANDOM">
            <summary>
            Return a random int value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.RANDOM(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Return a ranom int value inside this min/max values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LOWER(LiteDB.BsonValue)">
            <summary>
            Return lower case from string value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.UPPER(LiteDB.BsonValue)">
            <summary>
            Return UPPER case from string value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LTRIM(LiteDB.BsonValue)">
            <summary>
            Apply Left TRIM (start) from string value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.RTRIM(LiteDB.BsonValue)">
            <summary>
            Apply Right TRIM (end) from string value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.TRIM(LiteDB.BsonValue)">
            <summary>
            Apply TRIM from string value
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.INDEXOF(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Reports the zero-based index of the first occurrence of the specified string in this instance
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.INDEXOF(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Reports the zero-based index of the first occurrence of the specified string in this instance
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SUBSTRING(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns substring from string value using index and length (zero-based)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SUBSTRING(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns substring from string value using index and length (zero-based)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.REPLACE(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Returns replaced string changing oldValue with newValue
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.LPAD(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Return value string with left padding
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.RPAD(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Return value string with right padding
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SPLIT(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Slit value string based on separator 
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.SPLIT(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Slit value string based on regular expression pattern
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.FORMAT(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Return format value string using format definition (same as String.Format("{0:~}", values)).
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.JOIN(System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Join all values into a single string with ',' separator.
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.JOIN(System.Collections.Generic.IEnumerable{LiteDB.BsonValue},LiteDB.BsonValue)">
            <summary>
            Join all values into a single string with a string separator
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.IS_MATCH(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if value is match with regular expression pattern
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionMethods.MATCH(LiteDB.BsonValue,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Apply regular expression pattern over value to get group data. Return null if not found
            </summary>
        </member>
        <member name="T:LiteDB.VolatileAttribute">
            <summary>
            When a method are decorated with this attribute means that this method are not immutable
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.ADD(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Add two number values. If any side are string, concat left+right as string
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.MINUS(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Minus two number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.MULTIPLY(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Multiply two number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.DIVIDE(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Divide two number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.MOD(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Mod two number values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.EQ(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left and right are same value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.GT(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is greater than right value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.GTE(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is greater or equals than right value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.LT(LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is less than right value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.LTE(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is less or equals than right value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.NEQ(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left and right are not same value. Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.LIKE(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is "SQL LIKE" with right. Returns true or false. Works only when left and right are string
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.BETWEEN(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left is between right-array. Returns true or false. Right value must be an array. Support multiple values
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.IN(LiteDB.Collation,LiteDB.BsonValue,LiteDB.BsonValue)">
            <summary>
            Test if left are in any value in right side (when right side is an array). If right side is not an array, just implement a simple Equals (=). Returns true or false
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.PARAMETER_PATH(LiteDB.BsonDocument,System.String)">
            <summary>
            Returns value from root document (used in parameter). Returns same document if name are empty
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.MEMBER_PATH(LiteDB.BsonValue,System.String)">
            <summary>
            Return a value from a value as document. If has no name, just return values ($). If value are not a document, do not return anything
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.ARRAY_INDEX(LiteDB.BsonValue,System.Int32,LiteDB.BsonExpression,LiteDB.BsonDocument,LiteDB.Collation,LiteDB.BsonDocument)">
            <summary>
            Returns a single value from array according index or expression parameter
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.ARRAY_FILTER(LiteDB.BsonValue,System.Int32,LiteDB.BsonExpression,LiteDB.BsonDocument,LiteDB.Collation,LiteDB.BsonDocument)">
            <summary>
            Returns all values from array according filter expression or all values (index = MaxValue)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.DOCUMENT_INIT(System.String[],LiteDB.BsonValue[])">
            <summary>
            Create multi documents based on key-value pairs on parameters. DOCUMENT('_id', 1)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionOperators.ARRAY_INIT(LiteDB.BsonValue[])">
            <summary>
            Return an array from list of values. Support multiple values but returns a single value
            </summary>
        </member>
        <member name="T:LiteDB.BsonExpressionParser">
            <summary>
            Compile and execute simple expressions using BsonDocuments. Used in indexes and updates operations. See https://github.com/mbdavid/LiteDB/wiki/Expressions
            </summary>
        </member>
        <member name="F:LiteDB.BsonExpressionParser._operators">
            <summary>
            Operation definition by methods with defined expression type (operators are in precedence order)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParseFullExpression(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Start parse string into linq expression. Read path, function or base type bson values (int, double, bool, string)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParseSingleExpression(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Start parse string into linq expression. Read path, function or base type bson values (int, double, bool, string)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParseSelectDocumentBuilder(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument)">
            <summary>
            Parse a document builder syntax used in SELECT statment: {expr0} [AS] [{alias}], {expr1} [AS] [{alias}], ...
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParseUpdateDocumentBuilder(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument)">
            <summary>
            Parse a document builder syntax used in UPDATE statment: 
            {key0} = {expr0}, .... will be converted into { key: [expr], ... }
            {key: value} ... return return a new document
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseDouble(LiteDB.Tokenizer,LiteDB.BsonDocument)">
            <summary>
            Try parse double number - return null if not double token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseInt(LiteDB.Tokenizer,LiteDB.BsonDocument)">
            <summary>
            Try parse int number - return null if not int token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseBool(LiteDB.Tokenizer,LiteDB.BsonDocument)">
            <summary>
            Try parse bool - return null if not bool token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseNull(LiteDB.Tokenizer,LiteDB.BsonDocument)">
            <summary>
            Try parse null constant - return null if not null token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseString(LiteDB.Tokenizer,LiteDB.BsonDocument)">
            <summary>
            Try parse string with both single/double quote - return null if not string
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseDocument(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse json document - return null if not document token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseSource(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse source documents (when passed) * - return null if not source token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseArray(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse array - return null if not array token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseParameter(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse parameter - return null if not parameter token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseInnerExpression(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse inner expression - return null if not bracket token
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseMethodCall(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse method call - return null if not method call
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParsePath(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Parse JSON-Path - return null if not method call
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParsePath(LiteDB.Tokenizer,System.Linq.Expressions.Expression,LiteDB.ExpressionContext,LiteDB.BsonDocument,System.Collections.Generic.HashSet{System.String},System.Boolean@,System.Boolean@,System.Boolean@,System.Text.StringBuilder)">
            <summary>
            Implement a JSON-Path like navigation on BsonDocument. Support a simple range of paths
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.TryParseFunction(LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Try parse FUNCTION methods: MAP, FILTER, SORT, ...
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ParseFunction(System.String,LiteDB.BsonExpressionType,LiteDB.Tokenizer,LiteDB.ExpressionContext,LiteDB.BsonDocument,LiteDB.DocumentScope)">
            <summary>
            Parse expression functions, like MAP, FILTER or SORT.
            MAP(items[*] => @.Name)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.NewArray(LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Create an array expression with 2 values (used only in BETWEEN statement)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ReadField(LiteDB.Tokenizer,System.Text.StringBuilder)">
            <summary>
            Get field from simple \w regex or ['comp-lex'] - also, add into source. Can read empty field (root)
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ReadKey(LiteDB.Tokenizer,System.Text.StringBuilder)">
            <summary>
            Read key in document definition with single word or "comp-lex"
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ReadOperant(LiteDB.Tokenizer)">
            <summary>
            Read next token as Operant with ANY|ALL keyword before - returns null if next token are not an operant
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ConvertToEnumerable(LiteDB.BsonExpression)">
            <summary>
            Convert scalar expression into enumerable expression using ITEMS(...) method
            Append [*] to path or ITEMS(..) in all others
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.ConvertToArray(LiteDB.BsonExpression)">
            <summary>
            Convert enumerable expression into array using ARRAY(...) method
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.CreateLogicExpression(LiteDB.BsonExpressionType,LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Create new logic (AND/OR) expression based in 2 expressions
            </summary>
        </member>
        <member name="M:LiteDB.BsonExpressionParser.CreateConditionalExpression(LiteDB.BsonExpression,LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Create new conditional (IIF) expression. Execute expression only if True or False value
            </summary>
        </member>
        <member name="T:LiteDB.JsonReader">
            <summary>
            A class that read a json string using a tokenizer (without regex)
            </summary>
        </member>
        <member name="T:LiteDB.JsonSerializer">
            <summary>
            Static class for serialize/deserialize BsonDocuments into json extended format
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.Serialize(LiteDB.BsonValue,System.Boolean)">
            <summary>
            Json serialize a BsonValue into a String
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.Serialize(LiteDB.BsonValue,System.IO.TextWriter,System.Boolean)">
            <summary>
            Json serialize a BsonValue into a TextWriter
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.Serialize(LiteDB.BsonValue,System.Text.StringBuilder,System.Boolean)">
            <summary>
            Json serialize a BsonValue into a StringBuilder
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.Deserialize(System.String)">
            <summary>
            Deserialize a Json string into a BsonValue
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.Deserialize(System.IO.TextReader)">
            <summary>
            Deserialize a Json TextReader into a BsonValue
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.DeserializeArray(System.String)">
            <summary>
            Deserialize a json array as an IEnumerable of BsonValue
            </summary>
        </member>
        <member name="M:LiteDB.JsonSerializer.DeserializeArray(System.IO.TextReader)">
            <summary>
            Deserialize a json array as an IEnumerable of BsonValue reading on demand TextReader
            </summary>
        </member>
        <member name="P:LiteDB.JsonWriter.Indent">
            <summary>
            Get/Set indent size
            </summary>
        </member>
        <member name="P:LiteDB.JsonWriter.Pretty">
            <summary>
            Get/Set if writer must print pretty (with new line/indent)
            </summary>
        </member>
        <member name="M:LiteDB.JsonWriter.Serialize(LiteDB.BsonValue)">
            <summary>
            Serialize value into text writer
            </summary>
        </member>
        <member name="T:LiteDB.ObjectId">
            <summary>
            Represent a 12-bytes BSON type used in document Id
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.Empty">
            <summary>
            A zero 12-bytes ObjectId
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.Timestamp">
            <summary>
            Get timestamp
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.Machine">
            <summary>
            Get machine number
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.Pid">
            <summary>
            Get pid number
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.Increment">
            <summary>
            Get increment
            </summary>
        </member>
        <member name="P:LiteDB.ObjectId.CreationTime">
            <summary>
            Get creation time
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.#ctor">
            <summary>
            Initializes a new empty instance of the ObjectId class.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.#ctor(System.Int32,System.Int32,System.Int16,System.Int32)">
            <summary>
            Initializes a new instance of the ObjectId class from ObjectId vars.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.#ctor(LiteDB.ObjectId)">
            <summary>
            Initializes a new instance of ObjectId class from another ObjectId.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ObjectId class from hex string.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.#ctor(System.Byte[],System.Int32)">
            <summary>
            Initializes a new instance of the ObjectId class from byte array.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.FromHex(System.String)">
            <summary>
            Convert hex value string in byte array
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.Equals(LiteDB.ObjectId)">
            <summary>
            Checks if this ObjectId is equal to the given object. Returns true
            if the given object is equal to the value of this instance. 
            Returns false otherwise.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to this instance.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.CompareTo(LiteDB.ObjectId)">
            <summary>
            Compares two instances of ObjectId
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.ToByteArray(System.Byte[],System.Int32)">
            <summary>
            Represent ObjectId as 12 bytes array
            </summary>
        </member>
        <member name="M:LiteDB.ObjectId.NewObjectId">
            <summary>
            Creates a new ObjectId.
            </summary>
        </member>
        <member name="T:LiteDB.Engine.DiskReader">
            <summary>
            Memory file reader - must call Dipose after use to return reader into pool
            This class is not ThreadSafe - must have 1 instance per thread (get instance from DiskService)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskReader.ReadStream(System.IO.Stream,System.Int64,LiteDB.BufferSlice)">
            <summary>
            Read bytes from stream into buffer slice
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskReader.NewPage">
            <summary>
            Request for a empty, writable non-linked page (same as DiskService.NewPage)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskReader.Dispose">
            <summary>
            When dispose, return stream to pool
            </summary>
        </member>
        <member name="T:LiteDB.Engine.DiskService">
            <summary>
            Implement custom fast/in memory mapped disk access
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DiskService.Cache">
            <summary>
            Get memory cache instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.Initialize(System.IO.Stream,LiteDB.Collation,System.Int64)">
            <summary>
            Create a new empty database (use synced mode)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.GetReader">
            <summary>
            Get a new instance for read data/log pages. This instance are not thread-safe - must request 1 per thread (used in Transaction)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DiskService.MAX_ITEMS_COUNT">
            <summary>
            This method calculates the maximum number of items (documents or IndexNodes) that this database can have.
            The result is used to prevent infinite loops in case of problems with pointers
            Each page support max of 255 items. Use 10 pages offset (avoid empty disk)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.DiscardDirtyPages(System.Collections.Generic.IEnumerable{LiteDB.Engine.PageBuffer})">
            <summary>
            When a page are requested as Writable but not saved in disk, must be discard before release
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.DiscardCleanPages(System.Collections.Generic.IEnumerable{LiteDB.Engine.PageBuffer})">
            <summary>
            Discard pages that contains valid data and was not modified
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.NewPage">
            <summary>
            Request for a empty, writable non-linked page.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.WriteLogDisk(System.Collections.Generic.IEnumerable{LiteDB.Engine.PageBuffer})">
            <summary>
            Write all pages inside log file in a thread safe operation
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.GetFileLength(LiteDB.Engine.FileOrigin)">
            <summary>
            Get file length based on data/log length variables (no direct on disk)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.MarkAsInvalidState">
            <summary>
            Mark a file with a single signal to next open do auto-rebuild. Used only when closing database (after close files)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.ReadFull(LiteDB.Engine.FileOrigin)">
            <summary>
            Read all database pages inside file with no cache using. PageBuffers dont need to be Released
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.WriteDataDisk(System.Collections.Generic.IEnumerable{LiteDB.Engine.PageBuffer})">
            <summary>
            Write pages DIRECT in disk. This pages are not cached and are not shared - WORKS FOR DATA FILE ONLY
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.SetLength(System.Int64,LiteDB.Engine.FileOrigin)">
            <summary>
            Set new length for file in sync mode. Queue must be empty before set length
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DiskService.GetName(LiteDB.Engine.FileOrigin)">
            <summary>
            Get file name (or Stream name)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.MemoryCache">
            <summary>
            Manage linear memory segments to avoid re-creating array buffer in heap memory
            Do not share same memory store with different files
            [ThreadSafe]
            </summary>
        </member>
        <member name="F:LiteDB.Engine.MemoryCache._free">
            <summary>
            Contains free ready-to-use pages in memory
            - All pages here MUST have ShareCounter = 0
            - All pages here MUST have Position = MaxValue
            </summary>
        </member>
        <member name="F:LiteDB.Engine.MemoryCache._readable">
            <summary>
            Contains only clean pages (from both data/log file) - support page concurrency use
            - MUST have defined Origin and Position
            - Contains only 1 instance per Position/Origin
            - Contains only pages with ShareCounter >= 0
            *  = 0 - Page is available but is not in use by anyone (can be moved into _free list on next Extend())
            * >= 1 - Page is in use by 1 or more threads. Page must run "Release" when finished using
            </summary>
        </member>
        <member name="F:LiteDB.Engine.MemoryCache._extends">
            <summary>
            Get how many extends were made in this store
            </summary>
        </member>
        <member name="F:LiteDB.Engine.MemoryCache._segmentSizes">
            <summary>
            Get memory segment sizes
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.GetReadablePage(System.Int64,LiteDB.Engine.FileOrigin,System.Action{System.Int64,LiteDB.BufferSlice})">
            <summary>
            Get page from clean cache (readable). If page doesn't exist, create this new page and load data using factory fn
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.GetReadableKey(System.Int64,LiteDB.Engine.FileOrigin)">
            <summary>
            Get unique position in dictionary according with origin. Use positive/negative values
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.GetWritablePage(System.Int64,LiteDB.Engine.FileOrigin,System.Action{System.Int64,LiteDB.BufferSlice})">
            <summary>
            Request for a writable page - no other can read this page and this page has no reference
            Writable pages can be MoveToReadable() or DiscardWritable() - but never Released()
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.NewPage">
            <summary>
            Create new page using an empty buffer block. Mark this page as writable.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.NewPage(System.Int64,LiteDB.Engine.FileOrigin)">
            <summary>
            Create new page using an empty buffer block. Mark this page as writable.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.TryMoveToReadable(LiteDB.Engine.PageBuffer)">
            <summary>
            Try to move this page to readable list (if not already in readable list)
            Returns true if it was moved
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.MoveToReadable(LiteDB.Engine.PageBuffer)">
            <summary>
            Move a writable page to readable list - if already exists, override content
            Used after write operation that must mark page as readable because page content was changed
            This method runs BEFORE send to write disk queue - but new page request must read this new content
            Returns readable page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.DiscardPage(LiteDB.Engine.PageBuffer)">
            <summary>
            Completely discard a writable page - clean content and move to free list
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.GetFreePage">
            <summary>
            Get a clean, re-usable page from store. Can extend buffer segments if store is empty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.Extend">
            <summary>
            Check if it's possible move readable pages to free list - if not possible, extend memory
            </summary>
        </member>
        <member name="P:LiteDB.Engine.MemoryCache.PagesInUse">
            <summary>
            Return how many pages are in use when call this method (ShareCounter != 0).
            </summary>
        </member>
        <member name="P:LiteDB.Engine.MemoryCache.FreePages">
            <summary>
            Return how many pages are available (completely free)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.MemoryCache.ExtendSegments">
            <summary>
            Return how many segments are already loaded in memory
            </summary>
        </member>
        <member name="P:LiteDB.Engine.MemoryCache.ExtendPages">
            <summary>
            Get how many pages this cache extends in memory
            </summary>
        </member>
        <member name="P:LiteDB.Engine.MemoryCache.WritablePages">
            <summary>
            Get how many pages are used as Writable at this moment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.GetPages">
            <summary>
            Get all readable pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.MemoryCache.Clear">
            <summary>
            Clean all cache memory - moving back all readable pages into free list
            This command must be called inside an exclusive lock
            </summary>
        </member>
        <member name="T:LiteDB.Engine.BufferReader">
            <summary>
            Read multiple array segment as a single linear segment - Forward Only
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BufferReader.Position">
            <summary>
            Current global cursor position
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BufferReader.IsEOF">
            <summary>
            Indicate position are at end of last source array segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.MoveForward(System.Int32)">
            <summary>
            Move forward in current segment. If array segment finishes, open next segment
            Returns true if moved to another segment - returns false if continues in the same segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read bytes from source and copy into buffer. Return how many bytes was read
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.Skip(System.Int32)">
            <summary>
            Skip bytes (same as Read but with no array copy)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.Consume">
            <summary>
            Consume all data source until finish
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadString(System.Int32)">
            <summary>
            Read string with fixed size
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadCString">
            <summary>
            Reading string until find \0 at end
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.TryReadCStringCurrentSegment(System.String@)">
            <summary>	
            Try read CString in current segment avoind read byte-to-byte over segments	
            </summary>	
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadDateTime">
            <summary>
            Read DateTime as UTC ticks (not BSON format)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadGuid">
            <summary>
            Read Guid as 16 bytes array
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadObjectId">
            <summary>
            Write ObjectId as 12 bytes array
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadBoolean">
            <summary>
            Write a boolean as 1 byte (0 or 1)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadByte">
            <summary>
            Write single byte
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadPageAddress">
            <summary>
            Write PageAddress as PageID, Index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadBytes(System.Int32)">
            <summary>
            Read byte array - not great because need create new array instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadIndexKey">
            <summary>
            Read single IndexKey (BsonValue) from buffer. Use +1 length only for string/binary
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadDocument(System.Collections.Generic.HashSet{System.String})">
            <summary>
            Read a BsonDocument from reader
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadArray">
            <summary>
            Read an BsonArray from reader
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferReader.ReadElement(System.Collections.Generic.HashSet{System.String},System.String@)">
            <summary>
            Reads an element (key-value) from an reader
            </summary>
        </member>
        <member name="T:LiteDB.Engine.BufferWriter">
            <summary>
            Write data types/BSON data into byte[]. It's forward only and support multi buffer slice as source
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BufferWriter.Position">
            <summary>
            Current global cursor position
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BufferWriter.IsEOF">
            <summary>
            Indicate position are at end of last source array segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.MoveForward(System.Int32)">
            <summary>
            Move forward in current segment. If array segment finish, open next segment
            Returns true if move to another segment - returns false if continue in same segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write bytes from buffer into segmentsr. Return how many bytes was write
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.Byte[])">
            <summary>
            Write bytes from buffer into segmentsr. Return how many bytes was write
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Skip(System.Int32)">
            <summary>
            Skip bytes (same as Write but with no array copy)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Consume">
            <summary>
            Consume all data source until finish
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.WriteCString(System.String)">
            <summary>
            Write String with \0 at end
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.WriteString(System.String,System.Boolean)">
            <summary>
            Write string into output buffer. 
            Support direct string (with no length information) or BSON specs: with (legnth + 1) [4 bytes] before and '\0' at end = 5 extra bytes
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.DateTime)">
            <summary>
            Write DateTime as UTC ticks (not BSON format)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.Guid)">
            <summary>
            Write Guid as 16 bytes array
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(LiteDB.ObjectId)">
            <summary>
            Write ObjectId as 12 bytes array
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.Boolean)">
            <summary>
            Write a boolean as 1 byte (0 or 1)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(System.Byte)">
            <summary>
            Write single byte
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.Write(LiteDB.Engine.PageAddress)">
            <summary>
            Write PageAddress as PageID, Index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.WriteArray(LiteDB.BsonArray,System.Boolean)">
            <summary>
            Write BsonArray as BSON specs. Returns array bytes count
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BufferWriter.WriteDocument(LiteDB.BsonDocument,System.Boolean)">
            <summary>
            Write BsonDocument as BSON specs. Returns document bytes count
            </summary>
        </member>
        <member name="T:LiteDB.Engine.FileStreamFactory">
            <summary>
            FileStream disk implementation of disk factory
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.FileStreamFactory.Name">
            <summary>
            Get data filename
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileStreamFactory.GetStream(System.Boolean,System.Boolean)">
            <summary>
            Create new data file FileStream instance based on filename
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileStreamFactory.GetLength">
            <summary>
            Get file length using FileInfo. Crop file length if not length % PAGE_SIZE
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileStreamFactory.Exists">
            <summary>
            Check if file exists (without open it)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileStreamFactory.Delete">
            <summary>
            Delete file (must all stream be closed)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileStreamFactory.IsLocked">
            <summary>
            Test if this file are locked by another process
            </summary>
        </member>
        <member name="P:LiteDB.Engine.FileStreamFactory.CloseOnDispose">
            <summary>
            Close all stream on end
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IStreamFactory">
            <summary>
            Interface factory to provider new Stream instances for datafile/walfile resources. It's useful to multiple threads can read same datafile
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IStreamFactory.Name">
            <summary>
            Get Stream name (filename)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IStreamFactory.GetStream(System.Boolean,System.Boolean)">
            <summary>
            Get new Stream instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IStreamFactory.GetLength">
            <summary>
            Get file length
            </summary>
            <returns></returns>
        </member>
        <member name="M:LiteDB.Engine.IStreamFactory.Exists">
            <summary>
            Checks if file exists
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IStreamFactory.Delete">
            <summary>
            Delete physical file on disk
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IStreamFactory.IsLocked">
            <summary>
            Test if this file are used by another process
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IStreamFactory.CloseOnDispose">
            <summary>
            Indicate that factory must be dispose on finish
            </summary>
        </member>
        <member name="T:LiteDB.Engine.StreamFactory">
            <summary>
            Simple Stream disk implementation of disk factory - used for Memory/Temp database
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.StreamFactory.Name">
            <summary>
            Stream has no name (use stream type)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamFactory.GetStream(System.Boolean,System.Boolean)">
            <summary>
            Use ConcurrentStream wrapper to support multi thread in same Stream (using lock control)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamFactory.GetLength">
            <summary>
            Get file length using _stream.Length
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamFactory.Exists">
            <summary>
            Check if file exists based on stream length
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamFactory.Delete">
            <summary>
            There is no delete method in Stream factory
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamFactory.IsLocked">
            <summary>
            Test if this file are locked by another process (there is no way to test when Stream only)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.StreamFactory.CloseOnDispose">
            <summary>
            Do no dispose on finish
            </summary>
        </member>
        <member name="T:LiteDB.Engine.StreamPool">
            <summary>
            Manage multiple open readonly Stream instances from same source (file). 
            Support single writer instance
            Close all Stream on dispose
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.StreamPool.Writer">
            <summary>
            Get single Stream writer instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamPool.Rent">
            <summary>
            Rent a Stream reader instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamPool.Return(System.IO.Stream)">
            <summary>
            After use, return Stream reader instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.StreamPool.Dispose">
            <summary>
            Close all Stream instances (readers/writer)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.AesStream">
            <summary>
            Encrypted AES Stream
            </summary>
        </member>
        <member name="M:LiteDB.Engine.AesStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Decrypt data from Stream
            </summary>
        </member>
        <member name="M:LiteDB.Engine.AesStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Encrypt data to Stream
            </summary>
        </member>
        <member name="M:LiteDB.Engine.AesStream.NewSalt">
            <summary>
            Get new salt for encryption
            </summary>
        </member>
        <member name="T:LiteDB.Engine.ConcurrentStream">
            <summary>
            Implement internal thread-safe Stream using lock control - A single instance of ConcurrentStream are not multi thread,
            but multiples ConcurrentStream instances using same stream base will support concurrency
            </summary>
        </member>
        <member name="T:LiteDB.Engine.TempStream">
            <summary>
            Implement a temporary stream that uses MemoryStream until get LIMIT bytes, then copy all to tempoary disk file and delete on dispose
            Can be pass 
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TempStream.InMemory">
            <summary>
            Indicate that stream are all in memory
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TempStream.InDisk">
            <summary>
            Indicate that stream is now on this
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TempStream.Filename">
            <summary>
            Get temp disk filename (if null will be generate only when create file)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.EnginePragmas">
            <summary>
            Internal database pragmas persisted inside header page
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.UserVersion">
            <summary>
            Internal user version control to detect database changes
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.Collation">
            <summary>
            Define collation for this database. Value will be persisted on disk at first write database. After this, there is no change of collation
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.Timeout">
            <summary>
            Timeout for waiting unlock operations (default: 1 minute)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.LimitSize">
            <summary>
            Max limit of datafile (in bytes) (default: MaxValue)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.UtcDate">
            <summary>
            Returns date in UTC timezone from BSON deserialization (default: false == LocalTime)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.Checkpoint">
            <summary>
            When LOG file gets larger than checkpoint size (in pages), do a soft checkpoint (and also do a checkpoint at shutdown)
            Checkpoint = 0 means there's no auto-checkpoint nor shutdown checkpoint
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EnginePragmas.Pragmas">
            <summary>
            Get all pragmas
            </summary>
        </member>
        <member name="T:LiteDB.Engine.EngineSettings">
            <summary>
            All engine settings used to starts new engine
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.DataStream">
            <summary>
            Get/Set custom stream to be used as datafile (can be MemoryStream or TempStream). Do not use FileStream - to use physical file, use "filename" attribute (and keep DataStream/WalStream null)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.LogStream">
            <summary>
            Get/Set custom stream to be used as log file. If is null, use a new TempStream (for TempStream datafile) or MemoryStream (for MemoryStream datafile)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.TempStream">
            <summary>
            Get/Set custom stream to be used as temp file. If is null, will create new FileStreamFactory with "-tmp" on name
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.Filename">
            <summary>
            Full path or relative path from DLL directory. Can use ':temp:' for temp database or ':memory:' for in-memory database. (default: null)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.Password">
            <summary>
            Get database password to decrypt pages
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.InitialSize">
            <summary>
            If database is new, initialize with allocated space (in bytes) (default: 0)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.Collation">
            <summary>
            Create database with custom string collection (used only to create database) (default: Collation.Default)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.ReadOnly">
            <summary>
            Indicate that engine will open files in readonly mode (and will not support any database change)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.AutoRebuild">
            <summary>
            After a Close with exception do a database rebuild on next open
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.Upgrade">
            <summary>
            If detect it's a older version (v4) do upgrade in datafile to new v5. A backup file will be keeped in same directory
            </summary>
        </member>
        <member name="P:LiteDB.Engine.EngineSettings.ReadTransform">
            <summary>
            Is used to transform a <see cref="T:LiteDB.BsonValue"/> from the database on read. This can be used to upgrade data from older versions.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.EngineSettings.CreateDataFactory(System.Boolean)">
            <summary>
            Create new IStreamFactory for datafile
            </summary>
        </member>
        <member name="M:LiteDB.Engine.EngineSettings.CreateLogFactory">
            <summary>
            Create new IStreamFactory for logfile
            </summary>
        </member>
        <member name="M:LiteDB.Engine.EngineSettings.CreateTempFactory">
            <summary>
            Create new IStreamFactory for temporary file (sort)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.LiteEngine">
            <summary>
            A public class that take care of all engine data structure access - it´s basic implementation of a NoSql database
            Its isolated from complete solution - works on low level only (no linq, no poco... just BSON objects)
            [ThreadSafe]
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.GetCollectionNames">
            <summary>
            Returns all collection inside datafile
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.DropCollection(System.String)">
            <summary>
            Drop collection including all documents, indexes and extended pages (do not support transactions)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.RenameCollection(System.String,System.String)">
            <summary>
            Rename a collection (do not support transactions)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Delete(System.String,System.Collections.Generic.IEnumerable{LiteDB.BsonValue})">
            <summary>
            Implements delete based on IDs enumerable
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.DeleteMany(System.String,LiteDB.BsonExpression)">
            <summary>
            Implements delete based on filter expression
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.EnsureIndex(System.String,System.String,LiteDB.BsonExpression,System.Boolean)">
            <summary>
            Create a new index (or do nothing if already exists) to a collection/field
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.DropIndex(System.String,System.String)">
            <summary>
            Drop an index from a collection
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Insert(System.String,System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonAutoId)">
            <summary>
            Insert all documents in collection. If document has no _id, use AutoId generation.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.InsertDocument(LiteDB.Engine.Snapshot,LiteDB.BsonDocument,LiteDB.BsonAutoId,LiteDB.Engine.IndexService,LiteDB.Engine.DataService)">
            <summary>
            Internal implementation of insert a document
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Pragma(System.String)">
            <summary>
            Get engine internal pragma value
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Pragma(System.String,LiteDB.BsonValue)">
            <summary>
            Set engine pragma new value (some pragmas will be affected only after realod)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Query(System.String,LiteDB.Query)">
            <summary>
            Run query over collection using a query definition. 
            Returns a new IBsonDataReader that run and return first document result (open transaction)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Rebuild(LiteDB.Engine.RebuildOptions)">
            <summary>
            Implement a full rebuild database. Engine will be closed and re-created in another instance.
            A backup copy will be created with -backup extention. All data will be readed and re created in another database
            After run, will re-open database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Rebuild">
            <summary>
            Implement a full rebuild database. A backup copy will be created with -backup extention. All data will be readed and re created in another database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.RebuildContent(LiteDB.Engine.IFileReader)">
            <summary>
            Fill current database with data inside file reader - run inside a transacion
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Recovery(LiteDB.Collation)">
            <summary>
            Recovery datafile using a rebuild process. Run only on "Open" database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.GetSequence(LiteDB.Engine.Snapshot,LiteDB.BsonAutoId)">
            <summary>
            Get lastest value from a _id collection and plus 1 - use _sequence cache
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.SetSequence(LiteDB.Engine.Snapshot,LiteDB.BsonValue)">
            <summary>
            Update sequence number with new _id passed by user, IF this number are higher than current last _id
            At this point, newId.Type is Number
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.GetLastId(LiteDB.Engine.Snapshot)">
            <summary>
            Get last _id index key from collection. Returns MinValue if collection are empty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.GetSystemCollection(System.String)">
            <summary>
            Get registered system collection
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.RegisterSystemCollection(LiteDB.Engine.SystemCollection)">
            <summary>
            Register a new system collection that can be used in query for input/output data
            Collection name must starts with $
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.RegisterSystemCollection(System.String,System.Func{System.Collections.Generic.IEnumerable{LiteDB.BsonDocument}})">
            <summary>
            Register a new system collection that can be used in query for input data
            Collection name must starts with $
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.BeginTrans">
            <summary>
            Initialize a new transaction. Transaction are created "per-thread". There is only one single transaction per thread.
            Return true if transaction was created or false if current thread already in a transaction.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Commit">
            <summary>
            Persist all dirty pages into LOG file
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Rollback">
            <summary>
            Do rollback to current transaction. Clear dirty pages in memory and return new pages to main empty linked-list
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.AutoTransaction``1(System.Func{LiteDB.Engine.TransactionService,``0})">
            <summary>
            Create (or reuse) a transaction an add try/catch block. Commit transaction if is new transaction
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Update(System.String,System.Collections.Generic.IEnumerable{LiteDB.BsonDocument})">
            <summary>
            Implement update command to a document inside a collection. Return number of documents updated
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.UpdateMany(System.String,LiteDB.BsonExpression,LiteDB.BsonExpression)">
            <summary>
            Update documents using transform expression (must return a scalar/document value) using predicate as filter
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.UpdateDocument(LiteDB.Engine.Snapshot,LiteDB.Engine.CollectionPage,LiteDB.BsonDocument,LiteDB.Engine.IndexService,LiteDB.Engine.DataService)">
            <summary>
            Implement internal update document
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.TryUpgrade">
            <summary>
            If Upgrade=true, run this before open Disk service
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Upgrade(System.String,System.String,LiteDB.Collation)">
            <summary>
            Upgrade old version of LiteDB into new LiteDB file structure. Returns true if database was completed converted
            If database already in current version just return false
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Upsert(System.String,System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonAutoId)">
            <summary>
            Implement upsert command to documents in a collection. Calls update on all documents,
            then any documents not updated are then attempted to insert.
            This will have the side effect of throwing if duplicate items are attempted to be inserted.
            </summary>
        </member>
        <member name="F:LiteDB.Engine.LiteEngine._systemCollections">
            <summary>
            All system read-only collections for get metadata database information
            </summary>
        </member>
        <member name="F:LiteDB.Engine.LiteEngine._sequences">
            <summary>
            Sequence cache for collections last ID (for int/long numbers only)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.#ctor">
            <summary>
            Initialize LiteEngine using connection memory database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.#ctor(System.String)">
            <summary>
            Initialize LiteEngine using connection string using key=value; parser
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.#ctor(LiteDB.Engine.EngineSettings)">
            <summary>
            Initialize LiteEngine using initial engine settings
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Close">
            <summary>
            Normal close process:
            - Stop any new transaction
            - Stop operation loops over database (throw in SafePoint)
            - Wait for writer queue
            - Close disks
            - Clean variables
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Close(System.Exception)">
            <summary>
            Exception close database:
            - Stop diskQueue
            - Stop any disk read/write (dispose)
            - Dispose sort disk
            - Dispose locker
            - Checks Exception type for INVALID_DATAFILE_STATE to auto rebuild on open
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.Checkpoint">
            <summary>
            Run checkpoint command to copy log file into data file
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LiteEngine.InitializeSystemCollections">
            <summary>
            Register all internal system collections avaiable by default
            </summary>
        </member>
        <member name="T:LiteDB.Engine.FileReaderError">
            <summary>
            </summary>
        </member>
        <member name="T:LiteDB.Engine.FileReaderV7">
            <summary>
            Internal class to read old LiteDB v4 database version (datafile v7 structure)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.IsVersion(System.Byte[])">
            <summary>
            Check header slots to test if data file is a LiteDB FILE_VERSION = v7
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.GetCollections">
            <summary>
            Read all collection based on header page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.GetIndexes(System.String)">
            <summary>
            Read all indexes from all collection pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.GetDocuments(System.String)">
            <summary>
            Get all document using an indexInfo as start point (_id index).
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.ReadPage(System.UInt32)">
            <summary>
            Read all database pages from v7 structure into a flexible BsonDocument - only read what really needs
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.ReadExtendData(System.UInt32)">
            <summary>
            Read extend data block
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV7.VisitIndexPages(System.UInt32)">
            <summary>
            Visit all index pages by starting index page. Get a list with all index pages from a collection
            </summary>
        </member>
        <member name="T:LiteDB.Engine.FileReaderV8">
            <summary>
            Internal class to read all datafile documents - use only Stream - no cache system. Read log file (read commited transtraction)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.Open">
            <summary>
            Open data file and log file, read header and collection pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.GetPragmas">
            <summary>
            Read all pragma values
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.GetCollections">
            <summary>
            Read all collection based on header page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.GetIndexes(System.String)">
            <summary>
            Read all indexes from all collection pages (except _id index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.GetDocuments(System.String)">
            <summary>
            Read all documents from current collection with NO index use - read direct from free lists
            There is no document order
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.LoadPragmas">
            <summary>
            Load all pragmas from header page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.LoadDataPages">
            <summary>
            Read all file (and log) to find all data pages (and store groupby colPageID)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.LoadCollections">
            <summary>
            Load all collections from header OR via all data-pages ColID
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.LoadIndexes">
            <summary>
            Load all indexes for all collections
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.IsVersion(System.Byte[])">
            <summary>
            Check header slots to test if data file is a LiteDB FILE_VERSION = v8
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.LoadIndexMap">
            <summary>
            Load log file to build index map (wal map index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.ReadPage(System.UInt32,LiteDB.Engine.FileReaderV8.PageInfo@)">
            <summary>
            Read page from data/log stream (checks in logIndexMap file/position). Capture any exception here, but don't call HandleError
            </summary>
        </member>
        <member name="M:LiteDB.Engine.FileReaderV8.HandleError(System.Exception,LiteDB.Engine.FileReaderV8.PageInfo)">
            <summary>
            Handle any error avoiding throw exceptions during process. If exception must stop process (ioexceptions), throw exception
            Add errors to log and continue reading data file
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IFileReader">
            <summary>
            Interface to read current or old datafile structure - Used to shirnk/upgrade datafile from old LiteDB versions
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IFileReader.Open">
            <summary>
            Open and initialize file reader (run before any other command)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IFileReader.GetPragmas">
            <summary>
            Get all database pragma variables
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IFileReader.GetCollections">
            <summary>
            Get all collections name from database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IFileReader.GetIndexes(System.String)">
            <summary>
            Get all indexes from collection (except _id index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IFileReader.GetDocuments(System.String)">
            <summary>
            Get all documents from a collection
            </summary>
        </member>
        <member name="F:LiteDB.Engine.BasePage.SLOT_SIZE">
            <summary>
            Bytes used in each offset slot (to store segment position (2) + length (2))
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.PageID">
            <summary>
            Represent page number - start in 0 with HeaderPage [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.PageType">
            <summary>
            Indicate the page type [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.PrevPageID">
            <summary>
            Represent the previous page. Used for page-sequences - MaxValue represent that has NO previous page [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.NextPageID">
            <summary>
            Represent the next page. Used for page-sequences - MaxValue represent that has NO next page [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.PageListSlot">
            <summary>
            Get/Set where this page are in free list slot [1 byte]
            Used only in DataPage (0-4) and IndexPage (0-1) - when new or not used: 255
            DataPage: 0 (7344 - 8160 free space) - 1 (6120 - 7343) - 2 (4896 - 6119) - 3 (2448 - 4895) - 4 (0 - 2447)
            IndexPage 0 (1400 - 8160 free bytes) - 1 (0 - 1399 bytes free)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.ItemsCount">
            <summary>
            Indicate how many items are used inside this page [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.UsedBytes">
            <summary>
            Get how many bytes are used on content area (exclude header and footer blocks) [2 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.FragmentedBytes">
            <summary>
            Get how many bytes are fragmented inside this page (free blocks inside used blocks) [2 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.NextFreePosition">
            <summary>
            Get next free position. Starts with 32 (first byte after header) - There is no fragmentation after this [2 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.HighestIndex">
            <summary>
            Get last (highest) used index slot - use byte.MaxValue for empty [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.FreeBytes">
            <summary>
            Get how many free bytes (including fragmented bytes) are in this page (content space) - Will return 0 bytes if page are full (or with max 255 items)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.FooterSize">
            <summary>
            Get how many bytes are used in footer page at this moment
            ((HighestIndex + 1) * 4 bytes per slot: [2 for position, 2 for length])
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.ColID">
            <summary>
            Set in all datafile pages the page id about data/index collection. Useful if want re-build database without any index [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.TransactionID">
            <summary>
            Represent transaction ID that was stored [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.IsConfirmed">
            <summary>
            Used in WAL, define this page is last transaction page and are confirmed on disk [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.IsDirty">
            <summary>
            Set this pages that was changed and must be persist in disk [not peristable]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.BasePage.Buffer">
            <summary>
            Get page buffer instance
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.#ctor(LiteDB.Engine.PageBuffer,System.UInt32,LiteDB.Engine.PageType)">
            <summary>
            Create new Page based on pre-defined PageID and PageType
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.#ctor(LiteDB.Engine.PageBuffer)">
            <summary>
            Read header data from byte[] buffer into local variables
            using fixed position be be faster than use BufferReader
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.UpdateBuffer">
            <summary>
            Write header data from variable into byte[] buffer. When override, call base.UpdateBuffer() after write your code
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.MarkAsEmtpy">
            <summary>
            Change current page to Empty page - fix variables and buffer (DO NOT change PageID)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.Get(System.Byte)">
            <summary>
            Get a page segment item based on index slot
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.Insert(System.UInt16,System.Byte@)">
            <summary>
            Get a new page segment for this length content
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.InternalInsert(System.UInt16,System.Byte@)">
            <summary>
            Get a new page segment for this length content using fixed index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.Delete(System.Byte)">
            <summary>
            Remove index slot about this page segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.Update(System.Byte,System.UInt16)">
            <summary>
            Update segment bytes with new data. Current page must have bytes enougth for this new size. Index will not be changed
            Update will try use same segment to store. If not possible, write on end of page (with possible Defrag operation)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.Defrag">
            <summary>
            Defrag method re-organize all byte data content removing all fragmented data. This will move all page segments
            to create a single continuous content area (just after header area). No index segment will be changed (only positions)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.BasePage._startIndex">
            <summary>
            Store start index used in GetFreeIndex to avoid always run full loop over all indexes
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.GetFreeIndex">
            <summary>
            Get a free index slot in this page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.GetUsedIndexs">
            <summary>
            Get all used slots indexes in this page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.UpdateHighestIndex">
            <summary>
            Update HighestIndex based on current HighestIndex (step back looking for next used slot)
            Used only in Delete() operation
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.IsValidPos(System.UInt16)">
            <summary>
            Checks if segment position has a valid value (used for DEBUG)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.IsValidLen(System.UInt16)">
            <summary>
            Checks if segment length has a valid value (used for DEBUG)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.CalcPositionAddr(System.Byte)">
            <summary>
            Get buffer offset position where one page segment length are located (based on index slot)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.CalcLengthAddr(System.Byte)">
            <summary>
            Get buffer offset position where one page segment length are located (based on index slot)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.GetPagePosition(System.UInt32)">
            <summary>
            Returns a size of specified number of pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.GetPagePosition(System.Int32)">
            <summary>
            Returns a size of specified number of pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.ReadPage``1(LiteDB.Engine.PageBuffer)">
            <summary>
            Create new page instance based on buffer (READ)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePage.CreatePage``1(LiteDB.Engine.PageBuffer,System.UInt32)">
            <summary>
            Create new page instance with new PageID and passed buffer (NEW)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionPage.FreeDataPageList">
            <summary>
            Free data page linked-list (N lists for different range of FreeBlocks)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.CollectionPage._indexes">
            <summary>
            All indexes references for this collection
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionPage.PK">
            <summary>
            Get PK index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.GetCollectionIndex(System.String)">
            <summary>
            Get index from index name (index name is case sensitive) - returns null if not found
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.GetCollectionIndexes">
            <summary>
            Get all indexes in this collection page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.GetCollectionIndexesSlots">
            <summary>
            Get all collections array based on slot number
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.InsertCollectionIndex(System.String,System.String,System.Boolean)">
            <summary>
            Insert new index inside this collection page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.UpdateCollectionIndex(System.String)">
            <summary>
            Return index instance and mark as updatable
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionPage.DeleteCollectionIndex(System.String)">
            <summary>
            Remove index reference in this page
            </summary>
        </member>
        <member name="T:LiteDB.Engine.DataPage">
            <summary>
            The DataPage thats stores object data.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.#ctor(LiteDB.Engine.PageBuffer)">
            <summary>
            Read existing DataPage in buffer
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.#ctor(LiteDB.Engine.PageBuffer,System.UInt32)">
            <summary>
            Create new DataPage
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.GetBlock(System.Byte)">
            <summary>
            Get single DataBlock
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.InsertBlock(System.Int32,System.Boolean)">
            <summary>
            Insert new DataBlock. Use extend to indicate document sequence (document are large than PAGE_SIZE)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.UpdateBlock(LiteDB.Engine.DataBlock,System.Int32)">
            <summary>
            Update current block returning data block to be fill
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.DeleteBlock(System.Byte)">
            <summary>
            Delete single data block inside this page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.GetBlocks">
            <summary>
            Get all block positions inside this page that are not extend blocks (initial data block)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.DataPage._freePageSlots">
            <summary>
            FreeBytes ranges on page slot for free list page
            90% - 100% = 0 (7344 - 8160)
            75% -  90% = 1 (6120 - 7343)
            60% -  75% = 2 (4896 - 6119)
            30% -  60% = 3 (2448 - 4895)
             0% -  30% = 4 (0000 - 2447)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataPage.FreeIndexSlot(System.Int32)">
            <summary>
            Returns the slot the page should be in, given the <paramref name="freeBytes"/> it has
            </summary>
            <returns>A slot number between 0 and 4</returns>
        </member>
        <member name="M:LiteDB.Engine.DataPage.GetMinimumIndexSlot(System.Int32)">
            <summary>
            Returns the slot where there is a page with enough space for <paramref name="length"/> bytes of data.
            Returns -1 if no space guaranteed (more than 90% of a DataPage net size)
            </summary>
            <returns>A slot number between -1 and 3</returns>
        </member>
        <member name="T:LiteDB.Engine.HeaderPage">
            <summary>
            Header page represent first page on datafile. Engine contains a single instance of HeaderPage and all changes
            must be synchronized (using lock).
            </summary>
        </member>
        <member name="F:LiteDB.Engine.HeaderPage.HEADER_INFO">
            <summary>
            Header info the validate that datafile is a LiteDB file (27 bytes)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.HeaderPage.FILE_VERSION">
            <summary>
            Datafile specification version
            </summary>
        </member>
        <member name="P:LiteDB.Engine.HeaderPage.FreeEmptyPageList">
            <summary>
            Get/Set the pageID that start sequence with a complete empty pages (can be used as a new page) [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.HeaderPage.LastPageID">
            <summary>
            Last created page - Used when there is no free page inside file [4 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.HeaderPage.CreationTime">
            <summary>
            DateTime when database was created [8 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.HeaderPage.Pragmas">
            <summary>
            Get database pragmas instance class
            </summary>
        </member>
        <member name="F:LiteDB.Engine.HeaderPage._collections">
            <summary>
            All collections names/link pointers are stored inside this document
            </summary>
        </member>
        <member name="F:LiteDB.Engine.HeaderPage._isCollectionsChanged">
            <summary>
            Check if collections was changed
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.#ctor(LiteDB.Engine.PageBuffer,System.UInt32)">
            <summary>
            Create new Header Page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.#ctor(LiteDB.Engine.PageBuffer)">
            <summary>
            Load HeaderPage from buffer page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.LoadPage">
            <summary>
            Load page content based on page buffer
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.Savepoint">
            <summary>
            Create a save point before do any change on header page (execute UpdateBuffer())
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.Restore(LiteDB.Engine.PageBuffer)">
            <summary>
            Restore savepoint content and override on page. Must run in lock(_header)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.GetCollectionPageID(System.String)">
            <summary>
            Get collection PageID - return uint.MaxValue if not exists
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.GetCollections">
            <summary>
            Get all collections with pageID
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.InsertCollection(System.String,System.UInt32)">
            <summary>
            Insert new collection in header
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.DeleteCollection(System.String)">
            <summary>
            Remove existing collection reference in header
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.RenameCollection(System.String,System.String)">
            <summary>
            Rename collection with new name
            </summary>
        </member>
        <member name="M:LiteDB.Engine.HeaderPage.GetAvailableCollectionSpace">
            <summary>
            Get how many bytes are available in collection to store new collections
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexPage">
            <summary>
            The IndexPage thats stores object data.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.#ctor(LiteDB.Engine.PageBuffer)">
            <summary>
            Read existing IndexPage in buffer
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.#ctor(LiteDB.Engine.PageBuffer,System.UInt32)">
            <summary>
            Create new IndexPage
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.GetIndexNode(System.Byte)">
            <summary>
            Read single IndexNode
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.InsertIndexNode(System.Byte,System.Byte,LiteDB.BsonValue,LiteDB.Engine.PageAddress,System.Int32)">
            <summary>
            Insert new IndexNode. After call this, "node" instance can't be changed
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.DeleteIndexNode(System.Byte)">
            <summary>
            Delete index node based on page index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.GetIndexNodes">
            <summary>
            Get all index nodes inside this page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexPage.FreeIndexSlot(System.Int32)">
            <summary>
            Get page index slot on FreeIndexPageID 
            8160 - 600 : Slot #0
            599  -   0 : Slot #1 (no page in list)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.Index">
            <summary>
            Class that implement higher level of index search operations (equals, greater, less, ...)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.Index.Name">
            <summary>
            Index name
            </summary>
        </member>
        <member name="P:LiteDB.Engine.Index.Order">
            <summary>
            Get/Set index order
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Index.GetCost(LiteDB.Engine.CollectionIndex)">
            <summary>
            Calculate cost based on type/value/collection - Lower is best (1)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Index.Execute(LiteDB.Engine.IndexService,LiteDB.Engine.CollectionIndex)">
            <summary>
            Abstract method that must be implement for index seek/scan - Returns IndexNodes that match with index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Index.Run(LiteDB.Engine.CollectionPage,LiteDB.Engine.IndexService)">
            <summary>
            Find witch index will be used and run Execute method
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexAll">
            <summary>
            Return all index nodes
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexEquals">
            <summary>
            Implement equals index operation =
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexIn">
            <summary>
            Implement IN index operation. Value must be an array
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexRange">
            <summary>
            Implement range operation - in asc or desc way - can be used as LT, LTE, GT, GTE too because support MinValue/MaxValue
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexScan">
            <summary>
            Execute an "index scan" passing a Func as where
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexVirtual">
            <summary>
            Implement virtual index for system collections AND full data collection read
            </summary>
        </member>
        <member name="T:LiteDB.Engine.DatafileLookup">
            <summary>
            Implement basic document loader based on data service/bson reader
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IDocumentLookup">
            <summary>
            Interface for abstract document lookup that can be direct from datafile or by virtual collections
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexLookup">
            <summary>
            Implement lookup based only in index Key
            </summary>
        </member>
        <member name="T:LiteDB.Engine.BasePipe">
            <summary>
            Abstract class with workflow method to be used in pipeline implementation
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePipe.Pipe(System.Collections.Generic.IEnumerable{LiteDB.Engine.IndexNode},LiteDB.Engine.QueryPlan)">
            <summary>
            Abstract method to be implement according pipe workflow
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePipe.Include(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonExpression)">
            <summary>
            INCLUDE: Do include in result document according path expression - Works only with DocumentLookup
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePipe.Filter(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonExpression)">
            <summary>
            WHERE: Filter document according expression. Expression must be an Bool result
            </summary>
        </member>
        <member name="M:LiteDB.Engine.BasePipe.OrderBy(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonExpression,System.Int32,System.Int32,System.Int32)">
            <summary>
            ORDER BY: Sort documents according orderby expression and order asc/desc
            </summary>
        </member>
        <member name="T:LiteDB.Engine.DocumentCacheEnumerable">
            <summary>
            Implement an IEnumerable document cache that read data first time and store in memory/disk cache
            Used in GroupBy operation and MUST read all IEnumerable source before dispose because are need be linear from main resultset
            </summary>
        </member>
        <member name="T:LiteDB.Engine.GroupByPipe">
            <summary>
            Implement query using GroupBy expression
            </summary>
        </member>
        <member name="M:LiteDB.Engine.GroupByPipe.Pipe(System.Collections.Generic.IEnumerable{LiteDB.Engine.IndexNode},LiteDB.Engine.QueryPlan)">
            <summary>
            GroupBy Pipe Order
            - LoadDocument
            - Filter
            - OrderBy (to GroupBy)
            - GroupBy
            - HavingSelectGroupBy
            - OffSet
            - Limit
            </summary>
        </member>
        <member name="M:LiteDB.Engine.GroupByPipe.GroupBy(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.Engine.GroupBy)">
            <summary>
            GROUP BY: Apply groupBy expression and aggregate results in DocumentGroup
            </summary>
        </member>
        <member name="M:LiteDB.Engine.GroupByPipe.YieldDocuments(LiteDB.BsonValue,System.Collections.Generic.IEnumerator{LiteDB.BsonDocument},LiteDB.Engine.GroupBy,LiteDB.Engine.Done)">
            <summary>
            YieldDocuments will run over all key-ordered source and returns groups of source
            </summary>
        </member>
        <member name="M:LiteDB.Engine.GroupByPipe.SelectGroupBy(System.Collections.Generic.IEnumerable{LiteDB.Engine.DocumentCacheEnumerable},LiteDB.Engine.GroupBy)">
            <summary>
            Run Select expression over a group source - each group will return a single value
            If contains Having expression, test if result = true before run Select
            </summary>
        </member>
        <member name="T:LiteDB.Engine.QueryPipe">
            <summary>
            Basic query pipe workflow - support filter, includes and orderby
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPipe.Pipe(System.Collections.Generic.IEnumerable{LiteDB.Engine.IndexNode},LiteDB.Engine.QueryPlan)">
            <summary>
            Query Pipe order
            - LoadDocument
            - IncludeBefore
            - Filter
            - OrderBy
            - OffSet
            - Limit
            - IncludeAfter
            - Select
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPipe.Select(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonExpression)">
            <summary>
            Pipe: Transaform final result appling expressin transform. Can return document or simple values
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPipe.SelectAll(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonExpression)">
            <summary>
            Pipe: Run select expression over all recordset
            </summary>
        </member>
        <member name="T:LiteDB.Engine.QueryExecutor">
            <summary>
            Class that execute QueryPlan returing results
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryExecutor.ExecuteQuery(System.Boolean)">
            <summary>
            Run query definition into engine. Execute optimization to get query planner
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryExecutor.ExecuteQueryInto(System.String,LiteDB.BsonAutoId)">
            <summary>
            Execute query and insert result into another collection. Support external collections
            </summary>
        </member>
        <member name="T:LiteDB.Engine.QueryOptimization">
            <summary>
            Class that optimize query transforming user "Query" into "QueryPlan"
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.ProcessQuery">
            <summary>
            Build QueryPlan instance based on QueryBuilder fields
            - Load used fields in all expressions
            - Select best index option
            - Fill includes 
            - Define orderBy
            - Define groupBy
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.SplitWherePredicateInTerms">
            <summary>
            Fill terms from where predicate list
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.OptimizeTerms">
            <summary>
            Do some pre-defined optimization on terms to convert expensive filter in indexable filter
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.DefineQueryFields">
            <summary>
            Load all fields that must be deserialize from document.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.ChooseIndex(System.Collections.Generic.HashSet{System.String})">
            <summary>
            Try select index based on lowest cost or GroupBy/OrderBy reuse - use this priority order:
            - Get lowest index cost used in WHERE expressions (will filter data)
            - If there is no candidate, try get:
                - Same of GroupBy
                - Same of OrderBy
                - Prefered single-field (when no lookup neeed)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.DefineOrderBy">
            <summary>
            Define OrderBy optimization (try re-use index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.DefineGroupBy">
            <summary>
            Define GroupBy optimization (try re-use index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryOptimization.DefineIncludes">
            <summary>
            Will define each include to be run BEFORE where (worst) OR AFTER where (best)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.GroupBy">
            <summary>
            Represent an GroupBy definition (is based on OrderByDefinition)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexCost">
            <summary>
            Calculate index cost based on expression/collection index. 
            Lower cost is better - lowest will be selected
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexCost.Expression">
            <summary>
            Get filtered expression: "$._id = 10"
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexCost.IndexExpression">
            <summary>
            Get index expression only: "$._id"
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexCost.Index">
            <summary>
            Get created Index instance used on query
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexCost.CreateIndex(LiteDB.BsonExpressionType,System.String,LiteDB.BsonValue)">
            <summary>
            Create index based on expression predicate
            </summary>
        </member>
        <member name="T:LiteDB.Engine.OrderBy">
            <summary>
            Represent an OrderBy definition
            </summary>
        </member>
        <member name="T:LiteDB.Engine.QueryPlan">
            <summary>
            This class are result from optimization from QueryBuild in QueryAnalyzer. Indicate how engine must run query - there is no more decisions to engine made, must only execute as query was defined
            Contains used index and estimate cost to run
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Collection">
            <summary>
            Get collection name (required)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Index">
            <summary>
            Index used on query (required)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.IndexExpression">
            <summary>
            Index expression that will be used in index (source only)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.IndexCost">
            <summary>
            Get index cost (lower is best)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.IsIndexKeyOnly">
            <summary>
            If true, gereate document result only with IndexNode.Key (avoid load all document)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Filters">
            <summary>
            List of filters of documents
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.IncludeBefore">
            <summary>
            List of includes must be done BEFORE filter (it's not optimized but some filter will use this include)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.IncludeAfter">
            <summary>
            List of includes must be done AFTER filter (it's optimized because will include result only)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.OrderBy">
            <summary>
            Expression to order by resultset
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.GroupBy">
            <summary>
            Expression to group by document results
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Select">
            <summary>
            Transaformation data before return - if null there is no transform (return document)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Fields">
            <summary>
            Get fields name that will be deserialize from disk
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Limit">
            <summary>
            Limit resultset
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.Offset">
            <summary>
            Skip documents before returns
            </summary>
        </member>
        <member name="P:LiteDB.Engine.QueryPlan.ForUpdate">
            <summary>
            Indicate this query is for update (lock mode = Write)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPlan.GetPipe(LiteDB.Engine.TransactionService,LiteDB.Engine.Snapshot,LiteDB.Engine.SortDisk,LiteDB.Engine.EnginePragmas,System.UInt32)">
            <summary>
            Select corrent pipe
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPlan.GetLookup(LiteDB.Engine.Snapshot,LiteDB.Engine.EnginePragmas,System.UInt32)">
            <summary>
            Get corrent IDocumentLookup
            </summary>
        </member>
        <member name="M:LiteDB.Engine.QueryPlan.GetExecutionPlan">
            <summary>
            Get detail about execution plan for this query definition
            </summary>
        </member>
        <member name="T:LiteDB.Engine.Select">
            <summary>
            Represent a Select expression
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionService.CheckName(System.String,LiteDB.Engine.HeaderPage)">
            <summary>
            Check collection name if is valid (and fit on header)
            Throw correct message error if not valid name or not fit on header page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionService.Get(System.String,System.Boolean,LiteDB.Engine.CollectionPage@)">
            <summary>
            Get collection page instance (or create a new one). Returns true if a new collection was created
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionService.Add(System.String,LiteDB.Engine.CollectionPage@)">
            <summary>
            Add a new collection. Check if name the not exists. Create only in transaction page - will update header only in commit
            </summary>
        </member>
        <member name="F:LiteDB.Engine.DataService.MAX_DATA_BYTES_PER_PAGE">
            <summary>
            Get maximum data bytes[] that fit in 1 page = 8150
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataService.Insert(LiteDB.BsonDocument)">
            <summary>
            Insert BsonDocument into new data pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataService.Update(LiteDB.Engine.CollectionPage,LiteDB.Engine.PageAddress,LiteDB.BsonDocument)">
            <summary>
            Update document using same page position as reference
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataService.Read(LiteDB.Engine.PageAddress)">
            <summary>
            Get all buffer slices that address block contains. Need use BufferReader to read document
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataService.Delete(LiteDB.Engine.PageAddress)">
            <summary>
            Delete all datablock that contains a document (can use multiples data blocks)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexService">
            <summary>
            Implement a Index service - Add/Remove index nodes on SkipList
            Based on: http://igoro.com/archive/skip-lists-are-fascinating/
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.CreateIndex(System.String,System.String,System.Boolean)">
            <summary>
            Create a new index and returns head page address (skip list)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.AddNode(LiteDB.Engine.CollectionIndex,LiteDB.BsonValue,LiteDB.Engine.PageAddress,LiteDB.Engine.IndexNode)">
            <summary>
            Insert a new node index inside an collection index. Flip coin to know level
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.AddNode(LiteDB.Engine.CollectionIndex,LiteDB.BsonValue,LiteDB.Engine.PageAddress,System.Byte,LiteDB.Engine.IndexNode)">
            <summary>
            Insert a new node index inside an collection index.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.Flip">
            <summary>
            Flip coin (skipped list): returns how many levels the node will have (starts in 1, max of INDEX_MAX_LEVELS)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.GetNode(LiteDB.Engine.PageAddress)">
            <summary>
            Get a node inside a page using PageAddress - Returns null if address IsEmpty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.GetNodeList(LiteDB.Engine.PageAddress)">
            <summary>
            Gets all node list from passed nodeAddress (forward only)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.DeleteAll(LiteDB.Engine.PageAddress)">
            <summary>
            Deletes all indexes nodes from pkNode
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.DeleteList(LiteDB.Engine.PageAddress,System.Collections.Generic.HashSet{LiteDB.Engine.PageAddress})">
            <summary>
            Deletes all list of nodes in toDelete - fix single linked-list and return last non-delete node
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.DeleteSingleNode(LiteDB.Engine.IndexNode,LiteDB.Engine.CollectionIndex)">
            <summary>
            Delete a single index node - fix tree double-linked list levels
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.DropIndex(LiteDB.Engine.CollectionIndex)">
            <summary>
            Delete all index nodes from a specific collection index. Scan over all PK nodes, read all nodes list and remove
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.FindAll(LiteDB.Engine.CollectionIndex,System.Int32)">
            <summary>
            Return all index nodes from an index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexService.Find(LiteDB.Engine.CollectionIndex,LiteDB.BsonValue,System.Boolean,System.Int32)">
            <summary>
            Find first node that index match with value .
            If index are unique, return unique value - if index are not unique, return first found (can start, middle or end)
            If not found but sibling = true and key are not found, returns next value index node (if order = Asc) or prev node (if order = Desc)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.LockService">
            <summary>
            Lock service are collection-based locks. Lock will support any threads reading at same time. Writing operations will be locked
            based on collection. Eventualy, write operation can change header page that has an exclusive locker for.
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.LockService.IsInTransaction">
            <summary>
            Return if current thread have open transaction
            </summary>
        </member>
        <member name="P:LiteDB.Engine.LockService.TransactionsCount">
            <summary>
            Return how many transactions are opened
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.EnterTransaction">
            <summary>
            Enter transaction read lock - should be called just before enter a new transaction
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.ExitTransaction">
            <summary>
            Exit transaction read lock
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.EnterLock(System.String)">
            <summary>
            Enter collection write lock mode (only 1 collection per time can have this lock)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.ExitLock(System.String)">
            <summary>
            Exit collection in reserved lock
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.EnterExclusive">
            <summary>
            Enter all database in exclusive lock. Wait for all transactions finish. In exclusive mode no one can enter in new transaction (for read/write)
            If current thread already in exclusive mode, returns false
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.TryEnterExclusive(System.Boolean@)">
            <summary>
            Try enter in exclusive mode - if not possible, just exit with false (do not wait and no exceptions)
            If mustExit returns true, must call ExitExclusive after use
            </summary>
        </member>
        <member name="M:LiteDB.Engine.LockService.ExitExclusive">
            <summary>
            Exit exclusive lock
            </summary>
        </member>
        <member name="T:LiteDB.Engine.RebuildService">
            <summary>
            [ThreadSafe]
            </summary>
        </member>
        <member name="M:LiteDB.Engine.RebuildService.ReadFirstBytes(System.Boolean)">
            <summary>
            Read first 16kb (2 PAGES) in bytes
            </summary>
        </member>
        <member name="T:LiteDB.Engine.Snapshot">
            <summary>
            Represent a single snapshot
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.GetWritablePages(System.Boolean,System.Boolean)">
            <summary>
            Get all snapshot pages (can or not include collectionPage) - If included, will be last page
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.Clear">
            <summary>
            Clear all local pages and return page buffer to file reader. Do not release CollectionPage (only in Dispose method)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.Dispose">
            <summary>
            Dispose stream readers and exit collection lock
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.GetPage``1(System.UInt32,System.Boolean)">
            <summary>
            Get a valid page for this snapshot (must consider local-index and wal-index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.GetPage``1(System.UInt32,LiteDB.Engine.FileOrigin@,System.Int64@,System.Int32@,System.Boolean)">
            <summary>
            Get a valid page for this snapshot (must consider local-index and wal-index)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.ReadPage``1(System.UInt32,LiteDB.Engine.FileOrigin@,System.Int64@,System.Int32@,System.Boolean)">
            <summary>
            Read page from disk (dirty, wal or data)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.GetFreeDataPage(System.Int32)">
            <summary>
            Returns a page that contains space enough to data to insert new object - if one does not exit, creates a new page.
            Before return page, fix empty free list slot according with passed length
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.GetFreeIndexPage(System.Int32,System.UInt32@)">
            <summary>
            Get a index page with space enouth for a new index node
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.NewPage``1">
            <summary>
            Get a new empty page from disk: can be a reused page (from header free list) or file extend
            Never re-use page from same transaction
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.AddOrRemoveFreeDataList(LiteDB.Engine.DataPage)">
            <summary>
            Add/Remove a data page from free list slots
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.AddOrRemoveFreeIndexList(LiteDB.Engine.IndexPage,System.UInt32@)">
            <summary>
            Add/Remove a index page from single free list
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.AddFreeList``1(``0,System.UInt32@)">
            <summary>
            Add page into double linked-list (always add as first element)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.RemoveFreeList``1(``0,System.UInt32@)">
            <summary>
            Remove a page from double linked list.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.DeletePage``1(``0)">
            <summary>
            Delete a page - this page will be marked as Empty page
            There is no re-use deleted page in same transaction - deleted pages will be in another linked list and will
            be part of Header free list page only in commit
            </summary>
        </member>
        <member name="M:LiteDB.Engine.Snapshot.DropCollection(System.Action)">
            <summary>
            Delete current collection and all pages - this snapshot can't be used after this
            </summary>
        </member>
        <member name="T:LiteDB.Engine.TransactionMonitor">
            <summary>
            This class monitor all open transactions to manage memory usage for each transaction
            [Singleton - ThreadSafe]
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.ReleaseTransaction(LiteDB.Engine.TransactionService)">
            <summary>
            Release current thread transaction
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.GetThreadTransaction">
            <summary>
            Get transaction from current thread (from thread slot or from queryOnly) - do not created new transaction
            Used only in SystemCollections to get running query transaction
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.GetInitialSize">
            <summary>
            Get initial transaction size - get from free pages or reducing from all open transactions
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.TryExtend(LiteDB.Engine.TransactionService)">
            <summary>
            Try extend max transaction size in passed transaction ONLY if contains free pages available
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.CheckSafepoint(LiteDB.Engine.TransactionService)">
            <summary>
            Check if transaction size reach limit AND check if is possible extend this limit
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionMonitor.Dispose">
            <summary>
            Dispose all open transactions
            </summary>
        </member>
        <member name="T:LiteDB.Engine.TransactionService">
            <summary>
            Represent a single transaction service. Need a new instance for each transaction.
            You must run each transaction in a different thread - no 2 transaction in same thread (locks as per-thread)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionService.OpenCursors">
            <summary>
            Get/Set how many open cursor this transaction are running
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionService.ExplicitTransaction">
            <summary>
            Get/Set if this transaction was opened by BeginTrans() method (not by AutoTransaction/Cursor)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.Finalize">
            <summary>
            Finalizer: Will be called once a thread is closed. The TransactionMonitor._slot releases the used TransactionService.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.CreateSnapshot(LiteDB.Engine.LockMode,System.String,System.Boolean)">
            <summary>
            Create (or get from transaction-cache) snapshot and return
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.Safepoint">
            <summary>
            If current transaction contains too much pages, now is safe to remove clean pages from memory and flush to wal disk dirty pages
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.PersistDirtyPages(System.Boolean)">
            <summary>
            Persist all dirty in-memory pages (in all snapshots) and clear local pages list (even clean pages)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.Commit">
            <summary>
            Write pages into disk and confirm transaction in wal-index. Returns true if any dirty page was updated
            After commit, all snapshot are closed
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.Rollback">
            <summary>
            Rollback transaction operation - ignore all modified pages and return new pages into disk
            After rollback, all snapshot are closed
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.ReturnNewPages">
            <summary>
            Return added pages when occurs an rollback transaction (run this only in rollback). Create new transactionID and add into
            Log file all new pages as EmptyPage in a linked order - also, update SharedPage before store
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionService.Dispose">
            <summary>
            Public implementation of Dispose pattern.
            </summary>
        </member>
        <member name="T:LiteDB.Engine.WalIndexService">
            <summary>
            Do all WAL index services based on LOG file - has only single instance per engine
            [Singleton - ThreadSafe]
            </summary>
        </member>
        <member name="F:LiteDB.Engine.WalIndexService._lastTransactionID">
            <summary>
            Store last used transaction ID
            </summary>
        </member>
        <member name="P:LiteDB.Engine.WalIndexService.CurrentReadVersion">
            <summary>
            Get current read version for all new transactions
            </summary>
        </member>
        <member name="P:LiteDB.Engine.WalIndexService.LastTransactionID">
            <summary>
            Get current counter for transaction ID
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.Clear">
            <summary>
            Clear WAL index links and cache memory. Used after checkpoint and rebuild rollback
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.NextTransactionID">
            <summary>
            Get new transactionID in thread safe way
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.GetPageIndex(System.UInt32,System.Int32,System.Int32@)">
            <summary>
            Checks if a Page/Version are in WAL-index memory. Consider version that are below parameter. Returns PagePosition of this page inside WAL-file or Empty if page doesn't found.
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.ConfirmTransaction(System.UInt32,System.Collections.Generic.ICollection{LiteDB.Engine.PagePosition})">
            <summary>
            Add transactionID in confirmed list and update WAL index with all pages positions
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.RestoreIndex(LiteDB.Engine.HeaderPage@)">
            <summary>
            Load all confirmed transactions from log file (used only when open datafile)
            Don't need lock because it's called on ctor of LiteEngine
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.Checkpoint">
            <summary>
            Do checkpoint operation to copy log pages into data file. Return how many transactions was commited inside data file
            Checkpoint requires exclusive lock database
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.TryCheckpoint">
            <summary>
            Run checkpoint only if there is no open transactions
            </summary>
        </member>
        <member name="M:LiteDB.Engine.WalIndexService.CheckpointInternal">
            <summary>
            Do checkpoint operation to copy log pages into data file. Return how many transactions was commited inside data file
            Checkpoint requires exclusive lock database
            If soft = true, just try enter in exclusive mode - if not possible, just exit (don't execute checkpoint)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SortContainer.IsEOF">
            <summary>
            Returns if current container has no more items to read
            </summary>
        </member>
        <member name="F:LiteDB.Engine.SortContainer.Current">
            <summary>
            Get current/last read value in container
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SortContainer.Position">
            <summary>
            Get container disk position
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SortContainer.Count">
            <summary>
            Get how many keyValues this container contains
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortContainer.InitializeReader(System.IO.Stream,LiteDB.BufferSlice,System.Boolean)">
            <summary>
            Initialize reader based on Stream (if data was persisted in disk) or Buffer (if all data fit in only 1 container)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortContainer.GetSourceFromStream(System.IO.Stream)">
            <summary>
            Get 8k buffer slices inside file container
            </summary>
        </member>
        <member name="T:LiteDB.Engine.SortDisk">
            <summary>
            Single instance of TempDisk manage read/write access to temporary disk - used in merge sort
            [ThreadSafe]
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortDisk.GetReader">
            <summary>
            Get a new reader stream from pool. Must return after use
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortDisk.Return(System.IO.Stream)">
            <summary>
            Return used open reader stream to be reused in next sort
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortDisk.Return(System.Int64)">
            <summary>
            Return used disk container position to be reused in next sort
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortDisk.GetContainerPosition">
            <summary>
            Get next avaiable disk position - can be a new extend file or reuse container slot
            Use thread safe classes to ensure multiple threads access at same time
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortDisk.Write(System.Int64,LiteDB.BufferSlice)">
            <summary>
            Write buffer container data into disk
            </summary>
        </member>
        <member name="T:LiteDB.Engine.SortService">
            <summary>
            Service to implement merge sort, in disk, to run ORDER BY command.
            [ThreadSafe]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SortService.Count">
            <summary>
            Get how many documents was inserted by Insert method
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SortService.Containers">
            <summary>
            Expose used container in this sort operation
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortService.Insert(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{LiteDB.BsonValue,LiteDB.Engine.PageAddress}})">
            <summary>
            Read all input items and store in temp disk ordered in each container
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortService.Sort">
            <summary>
            Slipt all items in big sorted containers - Do merge sort with all containers
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortService.SliptValues(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{LiteDB.BsonValue,LiteDB.Engine.PageAddress}},LiteDB.Engine.Done)">
            <summary>
            Split values in many IEnumerable. Each enumerable contains values to be insert in a single container
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SortService.YieldValues(System.Collections.Generic.IEnumerator{System.Collections.Generic.KeyValuePair{LiteDB.BsonValue,LiteDB.Engine.PageAddress}},LiteDB.Engine.Done)">
            <summary>
            Loop in values enumerator to return N values for a single container
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Slot">
            <summary>
            Slot index [0-255] used in all index nodes
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.IndexType">
            <summary>
            Indicate index type: 0 = SkipList (reserved for future use)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Name">
            <summary>
            Index name
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Expression">
            <summary>
            Get index expression (path or expr)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.BsonExpr">
            <summary>
            Get BsonExpression from Expression
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Unique">
            <summary>
            Indicate if this index has distinct values only
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Head">
            <summary>
            Head page address for this index
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Tail">
            <summary>
            A link pointer to tail node
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.Reserved">
            <summary>
            Reserved byte (old max level)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.CollectionIndex.FreeIndexPageList">
            <summary>
            Free index page linked-list (all pages here must have at least 600 bytes)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.CollectionIndex.IsEmpty">
            <summary>
            Returns if this index slot is empty and can be used as new index
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionIndex.GetLength(LiteDB.Engine.CollectionIndex)">
            <summary>
            Get index collection size used in CollectionPage
            </summary>
        </member>
        <member name="M:LiteDB.Engine.CollectionIndex.GetLength(System.String,System.String)">
            <summary>
            Get index collection size used in CollectionPage
            </summary>
        </member>
        <member name="T:LiteDB.Engine.CursorInfo">
            <summary>
            Represent a single query featching data from engine
            </summary>
        </member>
        <member name="F:LiteDB.Engine.DataBlock.DATA_BLOCK_FIXED_SIZE">
            <summary>
            Get fixed part of DataBlock (6 bytes)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DataBlock.Position">
            <summary>
            Position block inside page
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DataBlock.Extend">
            <summary>
            Indicate if this data block is first block (false) or extend block (true)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DataBlock.NextBlock">
            <summary>
            If document need more than 1 block, use this link to next block
            </summary>
        </member>
        <member name="P:LiteDB.Engine.DataBlock.Buffer">
            <summary>
            Document buffer slice
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataBlock.#ctor(LiteDB.Engine.DataPage,System.Byte,LiteDB.BufferSlice)">
            <summary>
            Read new DataBlock from filled page segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.DataBlock.#ctor(LiteDB.Engine.DataPage,System.Byte,LiteDB.BufferSlice,System.Boolean,LiteDB.Engine.PageAddress)">
            <summary>
            Create new DataBlock and fill into buffer
            </summary>
        </member>
        <member name="T:LiteDB.Engine.Done">
            <summary>
            Simple parameter class to be passed into IEnumerable classes loop ("ref" do not works)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.FileOrigin.None">
            <summary>
            There is no origin (new page)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.FileOrigin.Data">
            <summary>
            Data file 
            </summary>
        </member>
        <member name="F:LiteDB.Engine.FileOrigin.Log">
            <summary>
            Log file (-log)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.IndexNode">
            <summary>
            Represent a index node inside a Index Page
            </summary>
        </member>
        <member name="F:LiteDB.Engine.IndexNode.INDEX_NODE_FIXED_SIZE">
            <summary>
            Fixed length of IndexNode (12 bytes)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Position">
            <summary>
            Position of this node inside a IndexPage (not persist)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Slot">
            <summary>
            Index slot reference in CollectionIndex [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Levels">
            <summary>
            Skip-list levels (array-size) (1-32) - [1 byte]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Key">
            <summary>
            The object value that was indexed (max 255 bytes value)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.DataBlock">
            <summary>
            Reference for a datablock address
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.NextNode">
            <summary>
            Single linked-list for all nodes from a single document [5 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Prev">
            <summary>
            Link to prev value (used in skip lists - Prev.Length = Next.Length) [5 bytes]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Next">
            <summary>
            Link to next value (used in skip lists - Prev.Length = Next.Length)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.IndexNode.Page">
            <summary>
            Get index page reference
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.GetNodeLength(System.Byte,LiteDB.BsonValue,System.Int32@)">
            <summary>
            Calculate how many bytes this node will need on page segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.GetKeyLength(LiteDB.BsonValue,System.Boolean)">
            <summary>
            Get how many bytes will be used to store this value. Must consider:
            [1 byte] - BsonType
            [1 byte] - KeyLength (used only in String|Byte[])
            [N bytes] - BsonValue in bytes (0-254)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.#ctor(LiteDB.Engine.IndexPage,System.Byte,LiteDB.BufferSlice)">
            <summary>
            Read index node from page segment (lazy-load)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.#ctor(LiteDB.Engine.IndexPage,System.Byte,LiteDB.BufferSlice,System.Byte,System.Byte,LiteDB.BsonValue,LiteDB.Engine.PageAddress)">
            <summary>
            Create new index node and persist into page segment
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.#ctor(LiteDB.BsonDocument)">
            <summary>
            Create a fake index node used only in Virtual Index runner
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.SetNextNode(LiteDB.Engine.PageAddress)">
            <summary>
            Update NextNode pointer (update in buffer too). Also, set page as dirty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.SetPrev(System.Byte,LiteDB.Engine.PageAddress)">
            <summary>
            Update Prev[index] pointer (update in buffer too). Also, set page as dirty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.SetNext(System.Byte,LiteDB.Engine.PageAddress)">
            <summary>
            Update Next[index] pointer (update in buffer too). Also, set page as dirty
            </summary>
        </member>
        <member name="M:LiteDB.Engine.IndexNode.GetNextPrev(System.Byte,System.Int32)">
            <summary>
            Returns Next (order == 1) OR Prev (order == -1)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.LockMode">
            <summary>
            Represents a snapshot lock mode
            </summary>
        </member>
        <member name="F:LiteDB.Engine.LockMode.Read">
            <summary>
            Read only snap with read lock
            </summary>
        </member>
        <member name="F:LiteDB.Engine.LockMode.Write">
            <summary>
            Read/Write snapshot with reserved lock
            </summary>
        </member>
        <member name="T:LiteDB.Engine.PageAddress">
            <summary>
            Represents a page address inside a page structure - index could be byte offset position OR index in a list (6 bytes)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageAddress.PageID">
            <summary>
            PageID (4 bytes)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageAddress.Index">
            <summary>
            Page Segment index inside page (1 bytes)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.PageAddress.IsEmpty">
            <summary>
            Returns true if this PageAdress is empty value
            </summary>
        </member>
        <member name="T:LiteDB.Engine.PageBuffer">
            <summary>
            Represent page buffer to be read/write using FileMemory
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageBuffer.UniqueID">
            <summary>
            Get, on initialize, a unique ID in all database instance for this PageBufer. Is a simple global incremented counter
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageBuffer.Position">
            <summary>
            Get/Set page position. If page are writable, this postion CAN be MaxValue (has not defined position yet)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageBuffer.Origin">
            <summary>
            Get/Set page bytes origin (data/log)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageBuffer.ShareCounter">
            <summary>
            Get/Set how many read-share threads are using this page. -1 means 1 thread are using as writable
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PageBuffer.Timestamp">
            <summary>
            Get/Set timestamp from last request
            </summary>
        </member>
        <member name="M:LiteDB.Engine.PageBuffer.Release">
            <summary>
            Release this page - decrement ShareCounter
            </summary>
        </member>
        <member name="T:LiteDB.Engine.PagePosition">
            <summary>
            Represents a page position after save in disk. Used in WAL files where PageID do not match with PagePosition
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PagePosition.PageID">
            <summary>
            PageID (4 bytes)
            </summary>
        </member>
        <member name="F:LiteDB.Engine.PagePosition.Position">
            <summary>
            Position in disk
            </summary>
        </member>
        <member name="P:LiteDB.Engine.PagePosition.IsEmpty">
            <summary>
            Checks if current PagePosition is empty value
            </summary>
        </member>
        <member name="T:LiteDB.Engine.Pragma">
            <summary>
            Represent a single internal engine variable that user can read/change
            </summary>
        </member>
        <member name="T:LiteDB.Engine.RebuildOptions">
            <summary>
            </summary>
        </member>
        <member name="F:LiteDB.Engine.RebuildOptions._buildId">
            <summary>
            A random BuildID identifier
            </summary>
        </member>
        <member name="P:LiteDB.Engine.RebuildOptions.Password">
            <summary>
            Rebuild database with a new password
            </summary>
        </member>
        <member name="P:LiteDB.Engine.RebuildOptions.Collation">
            <summary>
            Define a new collation when rebuild
            </summary>
        </member>
        <member name="P:LiteDB.Engine.RebuildOptions.IncludeErrorReport">
            <summary>
            When set true, if any problem occurs in rebuild, a _rebuild_errors collection
            will contains all errors found
            </summary>
        </member>
        <member name="P:LiteDB.Engine.RebuildOptions.Errors">
            <summary>
            After run rebuild process, get a error report (empty if no error detected)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.RebuildOptions.GetErrorReport">
            <summary>
            Get a list of errors during rebuild process
            </summary>
        </member>
        <member name="T:LiteDB.Engine.TransactionPages">
            <summary>
            Represent a simple structure to store added/removed pages in a transaction. One instance per transaction
            [SingleThread]
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.TransactionSize">
            <summary>
            Get how many pages are involved in this transaction across all snapshots - Will be clear when get MAX_TRANSACTION_SIZE
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.DirtyPages">
            <summary>
            Contains all dirty pages already persist in LOG file (used in all snapshots). Store in [uint, PagePosition] to reuse same method in save pages into log and get saved page positions on log
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.NewPages">
            <summary>
            Handle created pages during transaction (for rollback) - Is a list because order is important
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.FirstDeletedPageID">
            <summary>
            First deleted pageID 
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.LastDeletedPageID">
            <summary>
            Last deleted pageID
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.DeletedPages">
            <summary>
            Get deleted page count
            </summary>
        </member>
        <member name="E:LiteDB.Engine.TransactionPages.Commit">
            <summary>
            Callback function to modify header page on commit
            </summary>
        </member>
        <member name="M:LiteDB.Engine.TransactionPages.OnCommit(LiteDB.Engine.HeaderPage)">
            <summary>
            Run Commit event
            </summary>
        </member>
        <member name="P:LiteDB.Engine.TransactionPages.HeaderChanged">
            <summary>
            Detect if this transaction will need persist header page (has added/deleted pages or added/deleted collections)
            </summary>
        </member>
        <member name="T:LiteDB.Engine.SysQuery">
            <summary>
            This class implement $query experimental system function to run sub-queries. It's experimental only - possible not be present in final release
            </summary>
        </member>
        <member name="T:LiteDB.Engine.SystemCollection">
            <summary>
            Implement a simple system collection with input data only (to use Output must inherit this class)
            </summary>
        </member>
        <member name="P:LiteDB.Engine.SystemCollection.Name">
            <summary>
            Get system collection name (must starts with $)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SystemCollection.Input(LiteDB.BsonValue)">
            <summary>
            Get input data source factory
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SystemCollection.Output(System.Collections.Generic.IEnumerable{LiteDB.BsonDocument},LiteDB.BsonValue)">
            <summary>
            Get output data source factory (must implement in inherit class)
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SystemCollection.GetOption(LiteDB.BsonValue,System.String)">
            <summary>
            Static helper to read options arg as plain value or as document fields
            </summary>
        </member>
        <member name="M:LiteDB.Engine.SystemCollection.GetOption(LiteDB.BsonValue,System.String,LiteDB.BsonValue)">
            <summary>
            Static helper to read options arg as plain value or as document fields
            </summary>
        </member>
        <member name="T:LiteDB.AesEncryption">
            <summary>
            Encryption AES wrapper to encrypt data pages
            </summary>
        </member>
        <member name="M:LiteDB.AesEncryption.Encrypt(System.Byte[])">
            <summary>
            Encrypt byte array returning new encrypted byte array with same length of original array (PAGE_SIZE)
            </summary>
        </member>
        <member name="M:LiteDB.AesEncryption.Decrypt(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Decrypt and byte array returning a new byte array
            </summary>
        </member>
        <member name="M:LiteDB.AesEncryption.HashSHA1(System.String)">
            <summary>
            Hash a password using SHA1 just to verify password
            </summary>
        </member>
        <member name="M:LiteDB.AesEncryption.Salt(System.Int32)">
            <summary>
            Generate a salt key that will be stored inside first page database
            </summary>
            <returns></returns>
        </member>
        <member name="T:LiteDB.BsonReader">
            <summary>
            Internal class to deserialize a byte[] into a BsonDocument using BSON data format
            </summary>
        </member>
        <member name="M:LiteDB.BsonReader.Deserialize(System.Byte[])">
            <summary>
            Main method - deserialize using ByteReader helper
            </summary>
        </member>
        <member name="M:LiteDB.BsonReader.ReadDocument(LiteDB.ByteReader)">
            <summary>
            Read a BsonDocument from reader
            </summary>
        </member>
        <member name="M:LiteDB.BsonReader.ReadArray(LiteDB.ByteReader)">
            <summary>
            Read an BsonArray from reader
            </summary>
        </member>
        <member name="M:LiteDB.BsonReader.ReadElement(LiteDB.ByteReader,System.String@)">
            <summary>
            Reads an element (key-value) from an reader
            </summary>
        </member>
        <member name="M:LiteDB.ByteReader.ReadBsonString">
            <summary>
            Read BSON string add \0x00 at and of string and add this char in length before
            </summary>
        </member>
        <member name="T:LiteDB.AsyncManualResetEvent">
            <summary>
            Async implementation of ManualResetEvent
            https://devblogs.microsoft.com/pfxteam/building-async-coordination-primitives-part-1-asyncmanualresetevent/
            </summary>
        </member>
        <member name="T:LiteDB.BufferSlice">
            <summary>
            Internal class that implement same idea from ArraySegment[byte] but use a class (not a struct). Works for byte[] only
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.Clear">
            <summary>
            Clear all page content byte array (not controls)
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.Clear(System.Int32,System.Int32)">
            <summary>
            Clear page content byte array
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.Fill(System.Byte)">
            <summary>
            Fill all content with value. Used for DEBUG propost
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.All(System.Byte)">
            <summary>
            Checks if all values contains only value parameter (used for DEBUG)
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.ToHex">
            <summary>
            Return byte[] slice into hex digits
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.Slice(System.Int32,System.Int32)">
            <summary>
            Slice this buffer into new BufferSlice according new offset and new count
            </summary>
        </member>
        <member name="M:LiteDB.BufferSlice.ToArray">
            <summary>
            Convert this buffer slice into new byte[]
            </summary>
        </member>
        <member name="T:LiteDB.Collation">
            <summary>
            Implement how database will compare to order by/find strings according defined culture/compare options
            If not set, default is CurrentCulture with IgnoreCase
            </summary>
        </member>
        <member name="P:LiteDB.Collation.LCID">
            <summary>
            Get LCID code from culture
            </summary>
        </member>
        <member name="P:LiteDB.Collation.Culture">
            <summary>
            Get database language culture
            </summary>
        </member>
        <member name="P:LiteDB.Collation.SortOptions">
            <summary>
            Get options to how string should be compared in sort
            </summary>
        </member>
        <member name="M:LiteDB.Collation.Compare(System.String,System.String)">
            <summary>
            Compare 2 string values using current culture/compare options
            </summary>
        </member>
        <member name="T:LiteDB.Constants">
            <summary>
            Class with all constants used in LiteDB + Debbuger HELPER
            </summary>
        </member>
        <member name="F:LiteDB.Constants.PAGE_SIZE">
            <summary>
            The size of each page in disk - use 8192 as all major databases
            </summary>
        </member>
        <member name="F:LiteDB.Constants.PAGE_HEADER_SIZE">
            <summary>
            Header page size
            </summary>
        </member>
        <member name="F:LiteDB.Constants.ENCRYPTION_SALT_SIZE">
            <summary>
            Bytes used in encryption salt
            </summary>
        </member>
        <member name="F:LiteDB.Constants.BUFFER_WRITABLE">
            <summary>
            Define ShareCounter buffer as writable
            </summary>
        </member>
        <member name="F:LiteDB.Constants.INDEX_NAME_MAX_LENGTH">
            <summary>
            Define index name max length
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_LEVEL_LENGTH">
            <summary>
            Max level used on skip list (index).
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_INDEX_KEY_LENGTH">
            <summary>
            Max size of a index entry - usde for string, binary, array and documents. Need fit in 1 byte length
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_INDEX_LENGTH">
            <summary>
            Get max length of 1 single index node
            </summary>
        </member>
        <member name="F:LiteDB.Constants.PAGE_FREE_LIST_SLOTS">
            <summary>
            Get how many slots collection pages will have for free list page (data/index)
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_DOCUMENT_SIZE">
            <summary>
            Document limit size - 2048 data pages limit (about 16Mb - same size as MongoDB)
            Using 2047 because first/last page can contain less than 8150 bytes.
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_OPEN_TRANSACTIONS">
            <summary>
            Define how many transactions can be open simultaneously
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MAX_TRANSACTION_SIZE">
            <summary>
            Define how many pages all transaction will consume, in memory, before persist in disk. This amount are shared across all open transactions
            100,000 ~= 1Gb memory
            </summary>
        </member>
        <member name="F:LiteDB.Constants.MEMORY_SEGMENT_SIZES">
            <summary>
            Size, in PAGES, for each buffer array (used in MemoryStore)
            It's an array to increase after each extend - limited in highest value
            Each byte array will be created with this size * PAGE_SIZE
            Use minimal 12 to allocate at least 85Kb per segment (will use LOH)
            </summary>
        </member>
        <member name="F:LiteDB.Constants.VIRTUAL_INDEX_MAX_CACHE">
            <summary>
            Define how many documents will be keep in memory until clear cache and remove support to orderby/groupby
            </summary>
        </member>
        <member name="F:LiteDB.Constants.CONTAINER_SORT_SIZE">
            <summary>
            Define how many bytes each merge sort container will be created
            </summary>
        </member>
        <member name="F:LiteDB.Constants.RANDOMIZER_SEED">
            <summary>
            Initial seed for Random
            </summary>
        </member>
        <member name="M:LiteDB.Constants.LOG(System.String,System.String)">
            <summary>
            Log a message using Debug.WriteLine
            </summary>
        </member>
        <member name="M:LiteDB.Constants.LOG(System.Boolean,System.String,System.String)">
            <summary>
            Log a message using Debug.WriteLine only if conditional = true
            </summary>
        </member>
        <member name="M:LiteDB.Constants.ENSURE(System.Boolean,System.String)">
            <summary>
            Ensure condition is true, otherwise throw exception (check contract)
            </summary>
        </member>
        <member name="M:LiteDB.Constants.ENSURE(System.Boolean,System.Boolean,System.String)">
            <summary>
            If ifTest are true, ensure condition is true, otherwise throw ensure exception (check contract)
            </summary>
        </member>
        <member name="M:LiteDB.Constants.DEBUG(System.Boolean,System.String)">
            <summary>
            Ensure condition is true, otherwise throw exception (runs only in DEBUG mode)
            </summary>
        </member>
        <member name="T:LiteDB.ExtendedLengthHelper">
            <summary>
            Class to help extend IndexNode key up to 1023 bytes length (for string/byte[]) using 2 first bits in BsonType
            </summary>
        </member>
        <member name="M:LiteDB.ExtendedLengthHelper.ReadLength(System.Byte,System.Byte,LiteDB.BsonType@,System.UInt16@)">
            <summary>
            Read BsonType and UShort length from 2 bytes
            </summary>
        </member>
        <member name="M:LiteDB.ExtendedLengthHelper.WriteLength(LiteDB.BsonType,System.UInt16,System.Byte@,System.Byte@)">
            <summary>
            Write BsonType and UShort length in 2 bytes
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.IsFullZero(System.Byte[])">
            <summary>
            Very fast way to check if all byte array is full of zero
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.Fill(System.Byte[],System.Byte,System.Int32,System.Int32)">
            <summary>
            Fill all array with defined value
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ReadCString(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Read UTF8 string until found \0
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.Int16,System.Byte[],System.Int32)">
            <summary>
            Copy Int16 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.Int32,System.Byte[],System.Int32)">
            <summary>
            Copy Int32 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.Int64,System.Byte[],System.Int32)">
            <summary>
            Copy Int64 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.UInt16,System.Byte[],System.Int32)">
            <summary>
            Copy UInt16 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.UInt32,System.Byte[],System.Int32)">
            <summary>
            Copy UInt32 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.UInt64,System.Byte[],System.Int32)">
            <summary>
            Copy Int64 bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.Single,System.Byte[],System.Int32)">
            <summary>
            Copy Single bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferExtensions.ToBytes(System.Double,System.Byte[],System.Int32)">
            <summary>
            Copy Double bytes direct into buffer
            </summary>
        </member>
        <member name="M:LiteDB.BufferSliceExtensions.ReadCString(LiteDB.BufferSlice,System.Int32,System.Int32@)">
            <summary>
            Read string with \0 on end. Returns full string length (including \0 char)
            </summary>
        </member>
        <member name="M:LiteDB.BufferSliceExtensions.ReadIndexKey(LiteDB.BufferSlice,System.Int32)">
            <summary>
            Read any BsonValue. Use 1 byte for data type, 1 byte for length (optional), 0-255 bytes to value. 
            For document or array, use BufferReader
            </summary>
        </member>
        <member name="M:LiteDB.BufferSliceExtensions.WriteIndexKey(LiteDB.BufferSlice,LiteDB.BsonValue,System.Int32)">
            <summary>
            Wrtie any BsonValue. Use 1 byte for data type, 1 byte for length (optional), 0-255 bytes to value. 
            For document or array, use BufferWriter
            </summary>
        </member>
        <member name="M:LiteDB.DateExtensions.Truncate(System.DateTime)">
            <summary>
            Truncate DateTime in milliseconds
            </summary>
        </member>
        <member name="M:LiteDB.DictionaryExtensions.GetValue``1(System.Collections.Generic.Dictionary{System.String,System.String},System.String,``0)">
            <summary>
            Get value from dictionary converting datatype T
            </summary>
        </member>
        <member name="M:LiteDB.DictionaryExtensions.GetFileSize(System.Collections.Generic.Dictionary{System.String,System.String},System.String,System.Int64)">
            <summary>
            Get a value from a key converted in file size format: "1gb", "10 mb", "80000"
            </summary>
        </member>
        <member name="M:LiteDB.ExpressionExtensions.GetPath(System.Linq.Expressions.Expression)">
            <summary>
            Get Path (better ToString) from an Expression.
            Support multi levels: x => x.Customer.Address
            Support list levels: x => x.Addresses.Select(z => z.StreetName)
            </summary>
        </member>
        <member name="M:LiteDB.IOExceptionExtensions.IsLocked(System.IO.IOException)">
            <summary>
            Detect if exception is an Locked exception
            </summary>
        </member>
        <member name="M:LiteDB.IOExceptionExtensions.WaitIfLocked(System.IO.IOException,System.Int32)">
            <summary>
            Wait current thread for N milliseconds if exception is about Locking
            </summary>
        </member>
        <member name="M:LiteDB.LinqExtensions.IsLast``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Return same IEnumerable but indicate if item last item in enumerable
            </summary>
        </member>
        <member name="M:LiteDB.StreamExtensions.FlushToDisk(System.IO.Stream)">
            <summary>
            If Stream are FileStream, flush content direct to disk (avoid OS cache)
            </summary>
        </member>
        <member name="M:LiteDB.StringExtensions.IsWord(System.String)">
            <summary>
            Test if string is simple word pattern ([a-Z$_])
            </summary>
        </member>
        <member name="M:LiteDB.StringExtensions.SqlLike(System.String,System.String,LiteDB.Collation)">
            <summary>
            Implement SqlLike in C# string - based on
            https://stackoverflow.com/a/8583383/3286260
            I remove support for [ and ] to avoid missing close brackets
            </summary>
        </member>
        <member name="M:LiteDB.StringExtensions.SqlLikeStartsWith(System.String,System.Boolean@)">
            <summary>
            Get first string before any `%` or `_` ... used to index startswith - out if has more string pattern after found wildcard
            </summary>
        </member>
        <member name="T:LiteDB.FileHelper">
            <summary>
            A simple file helper tool with static methods
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.GetSuffixFile(System.String,System.String,System.Boolean)">
            <summary>
            Create a temp filename based on original filename - checks if file exists (if exists, append counter number)
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.GetLogFile(System.String)">
            <summary>
            Get LOG file based on data file
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.GetTempFile(System.String)">
            <summary>
            Get TEMP file based on data file
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.IsFileLocked(System.String)">
            <summary>
            Test if file are used by any process
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.TryExec(System.Int32,System.Action)">
            <summary>
            Try execute some action while has lock exception
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.Exec(System.Int32,System.Action)">
            <summary>
            Try execute some action while has lock exception. If timeout occurs, throw last exception
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.ParseFileSize(System.String)">
            <summary>
            Convert storage unit string "1gb", "10 mb", "80000" to long bytes
            </summary>
        </member>
        <member name="M:LiteDB.FileHelper.FormatFileSize(System.Int64)">
            <summary>
            Format a long file length to pretty file unit
            </summary>
        </member>
        <member name="T:LiteDB.LCID">
            <summary>
            Get CultureInfo object from LCID code (not avaiable in .net standard 1.3)
            </summary>
        </member>
        <member name="P:LiteDB.LCID.Current">
            <summary>
            Get current system operation LCID culture
            </summary>
        </member>
        <member name="T:LiteDB.LiteException">
            <summary>
            The main exception for LiteDB
            </summary>
        </member>
        <member name="P:LiteDB.LiteException.IsCritical">
            <summary>
            Critical error should be stop engine and release data files and all memory allocation
            </summary>
        </member>
        <member name="T:LiteDB.MimeTypeConverter">
            <summary>
            Convert filename to mimetype (http://stackoverflow.com/questions/1029740/get-mime-type-from-filename-extension)
            </summary>
        </member>
        <member name="T:LiteDB.Randomizer">
            <summary>
            A singleton shared randomizer class
            </summary>
        </member>
        <member name="T:LiteDB.Result`1">
            <summary>
            Implement a generic result structure with value and exception. This value can be partial value (like BsonDocument/Array)
            </summary>
        </member>
        <member name="M:LiteDB.Result`1.GetValue">
            <summary>
            Get array result or throw exception if there is any error on read result
            </summary>
        </member>
        <member name="T:LiteDB.TokenType">
            <summary>
            ASCII char names: https://www.ascii.cl/htmlcodes.htm
            </summary>
        </member>
        <member name="F:LiteDB.TokenType.OpenBrace">
            <summary> { </summary>
        </member>
        <member name="F:LiteDB.TokenType.CloseBrace">
            <summary> } </summary>
        </member>
        <member name="F:LiteDB.TokenType.OpenBracket">
            <summary> [ </summary>
        </member>
        <member name="F:LiteDB.TokenType.CloseBracket">
            <summary> ] </summary>
        </member>
        <member name="F:LiteDB.TokenType.OpenParenthesis">
            <summary> ( </summary>
        </member>
        <member name="F:LiteDB.TokenType.CloseParenthesis">
            <summary> ) </summary>
        </member>
        <member name="F:LiteDB.TokenType.Comma">
            <summary> , </summary>
        </member>
        <member name="F:LiteDB.TokenType.Colon">
            <summary> : </summary>
        </member>
        <member name="F:LiteDB.TokenType.SemiColon">
            <summary> ; </summary>
        </member>
        <member name="F:LiteDB.TokenType.At">
            <summary> @ </summary>
        </member>
        <member name="F:LiteDB.TokenType.Hashtag">
            <summary> # </summary>
        </member>
        <member name="F:LiteDB.TokenType.Til">
            <summary> ~ </summary>
        </member>
        <member name="F:LiteDB.TokenType.Period">
            <summary> . </summary>
        </member>
        <member name="F:LiteDB.TokenType.Ampersand">
            <summary> &amp; </summary>
        </member>
        <member name="F:LiteDB.TokenType.Dollar">
            <summary> $ </summary>
        </member>
        <member name="F:LiteDB.TokenType.Exclamation">
            <summary> ! </summary>
        </member>
        <member name="F:LiteDB.TokenType.NotEquals">
            <summary> != </summary>
        </member>
        <member name="F:LiteDB.TokenType.Equals">
            <summary> = </summary>
        </member>
        <member name="F:LiteDB.TokenType.Greater">
            <summary> &gt; </summary>
        </member>
        <member name="F:LiteDB.TokenType.GreaterOrEquals">
            <summary> &gt;= </summary>
        </member>
        <member name="F:LiteDB.TokenType.Less">
            <summary> &lt; </summary>
        </member>
        <member name="F:LiteDB.TokenType.LessOrEquals">
            <summary> &lt;= </summary>
        </member>
        <member name="F:LiteDB.TokenType.Minus">
            <summary> - </summary>
        </member>
        <member name="F:LiteDB.TokenType.Plus">
            <summary> + </summary>
        </member>
        <member name="F:LiteDB.TokenType.Asterisk">
            <summary> * </summary>
        </member>
        <member name="F:LiteDB.TokenType.Slash">
            <summary> / </summary>
        </member>
        <member name="F:LiteDB.TokenType.Backslash">
            <summary> \ </summary>
        </member>
        <member name="F:LiteDB.TokenType.Percent">
            <summary> % </summary>
        </member>
        <member name="F:LiteDB.TokenType.String">
            <summary> "..." or '...' </summary>
        </member>
        <member name="F:LiteDB.TokenType.Int">
            <summary> [0-9]+ </summary>
        </member>
        <member name="F:LiteDB.TokenType.Double">
            <summary> [0-9]+.[0-9] </summary>
        </member>
        <member name="F:LiteDB.TokenType.Whitespace">
            <summary> \n\r\t \u0032 </summary>
        </member>
        <member name="F:LiteDB.TokenType.Word">
            <summary> [a-Z_$]+[a-Z0-9_$] </summary>
        </member>
        <member name="T:LiteDB.Token">
            <summary>
            Represent a single string token
            </summary>
        </member>
        <member name="M:LiteDB.Token.Expect(LiteDB.TokenType)">
            <summary>
            Expect if token is type (if not, throw UnexpectedToken)
            </summary>
        </member>
        <member name="M:LiteDB.Token.Expect(LiteDB.TokenType,LiteDB.TokenType)">
            <summary>
            Expect for type1 OR type2 (if not, throw UnexpectedToken)
            </summary>
        </member>
        <member name="M:LiteDB.Token.Expect(LiteDB.TokenType,LiteDB.TokenType,LiteDB.TokenType)">
            <summary>
            Expect for type1 OR type2 OR type3 (if not, throw UnexpectedToken)
            </summary>
        </member>
        <member name="T:LiteDB.Tokenizer">
            <summary>
            Class to tokenize TextReader input used in JsonRead/BsonExpressions
            This class are not thread safe
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.CheckEOF">
            <summary>
            If EOF throw an invalid token exception (used in while()) otherwise return "false" (not EOF)
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.IsWordChar(System.Char,System.Boolean)">
            <summary>
            Checks if char is an valid part of a word [a-Z_]+[a-Z0-9_$]*
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadChar">
            <summary>
            Read next char in stream and set in _current
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.LookAhead(System.Boolean)">
            <summary>
            Look for next token but keeps in buffer when run "ReadToken()" again.
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadToken(System.Boolean)">
            <summary>
            Read next token (or from ahead buffer).
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadNext(System.Boolean)">
            <summary>
            Read next token from reader
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.EatWhitespace">
            <summary>
            Eat all whitespace - used before a valid token
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadWord">
            <summary>
            Read a word (word = [\w$]+)
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadNumber(System.Boolean@)">
            <summary>
            Read a number - it's accepts all number char, but not validate. When run Convert, .NET will check if number is correct
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadString(System.Char)">
            <summary>
            Read a string removing open and close " or '
            </summary>
        </member>
        <member name="M:LiteDB.Tokenizer.ReadLine">
            <summary>
            Read all chars to end of LINE
            </summary>
        </member>
    </members>
</doc>
