using System;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Helper;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint管理器测试类
    /// </summary>
    public static class HintManagerTest
    {
        /// <summary>
        /// 执行基本功能测试
        /// </summary>
        public static void RunBasicTests()
        {
            Logger.Info("开始执行HintManager基本功能测试...");
            
            try
            {
                // 测试管理器初始化
                TestManagerInitialization();
                
                // 测试模板系统
                TestTemplateSystem();
                
                // 测试扩展方法
                TestExtensionMethods();
                
                // 测试迁移助手
                TestMigrationHelper();
                
                Logger.Info("所有HintManager基本功能测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"HintManager测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试管理器初始化
        /// </summary>
        private static void TestManagerInitialization()
        {
            Logger.Info("测试HintManager初始化...");
            
            var manager = HintManager.Instance;
            if (manager == null)
            {
                throw new Exception("HintManager初始化失败");
            }
            
            // 测试统计信息
            string stats = manager.GetStatistics();
            if (string.IsNullOrEmpty(stats))
            {
                throw new Exception("获取统计信息失败");
            }
            
            Logger.Info("✅ HintManager初始化测试通过");
        }

        /// <summary>
        /// 测试模板系统
        /// </summary>
        private static void TestTemplateSystem()
        {
            Logger.Info("测试模板系统...");
            
            // 测试获取模板
            var notificationTemplate = HintTemplate.GetTemplate(HintType.Notification);
            if (notificationTemplate == null || notificationTemplate.Type != HintType.Notification)
            {
                throw new Exception("通知模板获取失败");
            }
            
            // 测试创建预定义配置
            var levelUpConfig = HintTemplate.CreateLevelUp("TestPlayer", 5, 6);
            if (levelUpConfig == null || !levelUpConfig.Text.Contains("5 → 6"))
            {
                throw new Exception("升级配置创建失败");
            }
            
            var notificationConfig = HintTemplate.CreateNotification("测试通知", "#FF0000");
            if (notificationConfig == null || !notificationConfig.Text.Contains("测试通知"))
            {
                throw new Exception("通知配置创建失败");
            }
            
            Logger.Info("✅ 模板系统测试通过");
        }

        /// <summary>
        /// 测试扩展方法
        /// </summary>
        private static void TestExtensionMethods()
        {
            Logger.Info("测试扩展方法...");
            
            // 由于需要真实的Player对象，这里只测试配置创建
            var quickNotification = HintExtensions.CreateQuickNotification("快速通知", 500, 3f, "#00FF00");
            if (quickNotification == null || !quickNotification.Text.Contains("快速通知"))
            {
                throw new Exception("快速通知配置创建失败");
            }
            
            var customHint = HintExtensions.CreateCustomPositionHint("自定义Hint", 600, 18, 8f, true);
            if (customHint == null || customHint.YCoordinate != 600 || customHint.FontSize != 18)
            {
                throw new Exception("自定义位置Hint配置创建失败");
            }
            
            Logger.Info("✅ 扩展方法测试通过");
        }

        /// <summary>
        /// 测试迁移助手
        /// </summary>
        private static void TestMigrationHelper()
        {
            Logger.Info("测试迁移助手...");
            
            // 测试兼容Hint创建
            var compatibleHint = HintMigrationHelper.CreateCompatibleHint(HintType.Warning, "警告消息", 450);
            if (compatibleHint == null || compatibleHint.YCoordinate != 450)
            {
                throw new Exception("兼容Hint创建失败");
            }
            
            // 测试迁移指南
            try
            {
                HintMigrationHelper.PrintMigrationGuide();
            }
            catch (Exception ex)
            {
                throw new Exception($"打印迁移指南失败: {ex.Message}");
            }
            
            Logger.Info("✅ 迁移助手测试通过");
        }

        /// <summary>
        /// 执行性能测试
        /// </summary>
        public static void RunPerformanceTests()
        {
            Logger.Info("开始执行HintManager性能测试...");
            
            try
            {
                // 测试大量配置创建
                TestMassConfigCreation();
                
                // 测试模板缓存性能
                TestTemplateCachePerformance();
                
                Logger.Info("所有HintManager性能测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"HintManager性能测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试大量配置创建
        /// </summary>
        private static void TestMassConfigCreation()
        {
            Logger.Info("测试大量配置创建性能...");
            
            var startTime = DateTime.Now;
            
            // 创建1000个不同类型的配置
            for (int i = 0; i < 1000; i++)
            {
                var hintType = (HintType)(i % Enum.GetValues(typeof(HintType)).Length);
                var config = HintTemplate.GetTemplate(hintType);
                config.Text = $"测试消息 {i}";
            }
            
            var elapsed = DateTime.Now - startTime;
            Logger.Info($"创建1000个配置耗时: {elapsed.TotalMilliseconds:F2}ms");
            
            if (elapsed.TotalMilliseconds > 1000) // 如果超过1秒认为性能有问题
            {
                Logger.Warn("配置创建性能可能需要优化");
            }
            
            Logger.Info("✅ 大量配置创建测试通过");
        }

        /// <summary>
        /// 测试模板缓存性能
        /// </summary>
        private static void TestTemplateCachePerformance()
        {
            Logger.Info("测试模板缓存性能...");
            
            var startTime = DateTime.Now;
            
            // 重复获取相同模板1000次
            for (int i = 0; i < 1000; i++)
            {
                var notificationTemplate = HintTemplate.GetTemplate(HintType.Notification);
                var warningTemplate = HintTemplate.GetTemplate(HintType.Warning);
            }
            
            var elapsed = DateTime.Now - startTime;
            Logger.Info($"获取3000个模板耗时: {elapsed.TotalMilliseconds:F2}ms");
            
            if (elapsed.TotalMilliseconds > 500) // 如果超过0.5秒认为性能有问题
            {
                Logger.Warn("模板获取性能可能需要优化");
            }
            
            Logger.Info("✅ 模板缓存性能测试通过");
        }

        /// <summary>
        /// 执行集成测试（需要真实的玩家对象）
        /// </summary>
        /// <param name="testPlayer">测试玩家</param>
        public static void RunIntegrationTests(Player testPlayer)
        {
            if (testPlayer == null)
            {
                Logger.Warn("集成测试需要真实的玩家对象，跳过测试");
                return;
            }

            Logger.Info($"开始执行HintManager集成测试 - 测试玩家: {testPlayer.Nickname}");
            
            try
            {
                // 测试基本显示功能
                TestBasicDisplay(testPlayer);
                
                // 测试生命周期管理
                TestLifecycleManagement(testPlayer);
                
                // 测试位置管理
                TestPositionManagement(testPlayer);
                
                Logger.Info("所有HintManager集成测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"HintManager集成测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
            finally
            {
                // 清理测试数据
                testPlayer.RemoveAllHints();
            }
        }

        /// <summary>
        /// 测试基本显示功能
        /// </summary>
        private static void TestBasicDisplay(Player testPlayer)
        {
            Logger.Info("测试基本显示功能...");
            
            // 测试通知显示
            string notificationId = testPlayer.ShowNotification("测试通知", "#00FF00", 2f);
            if (string.IsNullOrEmpty(notificationId))
            {
                throw new Exception("通知显示失败");
            }
            
            // 测试警告显示
            string warningId = testPlayer.ShowWarning("测试警告", 3f);
            if (string.IsNullOrEmpty(warningId))
            {
                throw new Exception("警告显示失败");
            }
            
            // 检查Hint数量
            int hintCount = testPlayer.GetHintCount();
            if (hintCount != 2)
            {
                throw new Exception($"Hint数量不正确，期望2个，实际{hintCount}个");
            }
            
            Logger.Info("✅ 基本显示功能测试通过");
        }

        /// <summary>
        /// 测试生命周期管理
        /// </summary>
        private static void TestLifecycleManagement(Player testPlayer)
        {
            Logger.Info("测试生命周期管理...");
            
            // 清理之前的Hint
            testPlayer.RemoveAllHints();
            
            // 创建临时Hint
            string tempHintId = testPlayer.ShowSuccess("临时成功消息", 1f);
            if (string.IsNullOrEmpty(tempHintId))
            {
                throw new Exception("临时Hint创建失败");
            }
            
            // 等待一段时间后检查是否自动清理（这里只是模拟，实际需要等待）
            // 在真实测试中，可以使用Timing.CallDelayed来验证自动清理
            
            Logger.Info("✅ 生命周期管理测试通过");
        }

        /// <summary>
        /// 测试位置管理
        /// </summary>
        private static void TestPositionManagement(Player testPlayer)
        {
            Logger.Info("测试位置管理...");
            
            // 清理之前的Hint
            testPlayer.RemoveAllHints();
            
            // 测试相同位置的Hint冲突检测
            string hint1Id = testPlayer.ShowNotification("第一个通知");
            string hint2Id = testPlayer.ShowNotification("第二个通知"); // 应该被阻止或替换
            
            // 检查是否正确处理了位置冲突
            int hintCount = testPlayer.GetHintCount();
            Logger.Debug($"位置冲突测试 - 当前Hint数量: {hintCount}");
            
            Logger.Info("✅ 位置管理测试通过");
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public static void CleanupTestData()
        {
            Logger.Info("清理HintManager测试数据...");
            
            try
            {
                // 清理所有在线玩家的测试Hint
                foreach (var player in XHelper.PlayerList.Where(p => p != null))
                {
                    player.RemoveAllHints();
                }
                
                Logger.Info("HintManager测试数据清理完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理测试数据失败: {ex.Message}");
            }
        }
    }
}
