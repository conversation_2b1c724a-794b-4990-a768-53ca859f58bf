﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/MeowServer/HintServiceMeow</PackageProjectUrl>
    <RepositoryUrl>https://github.com/MeowServer/HintServiceMeow</RepositoryUrl>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{097B585F-438D-4D50-A8AC-0FE0E3A7DE77}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HintServiceMeow</RootNamespace>
    <AssemblyName>HintServiceMeow</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <langVersion>latest</langVersion>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>TRACE;EXILED</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>9.0</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="0Harmony, Version=2.3.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Lib.Harmony.2.3.6\lib\net48\0Harmony.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-firstpass, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Assembly-CSharp-firstpass.dll</HintPath>
    </Reference>
    <Reference Include="Assembly-CSharp-publicized, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Assembly-CSharp-publicized.dll</HintPath>
    </Reference>
    <Reference Include="CommandSystem.Core, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\CommandSystem.Core.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.API, Version=9.6.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Exiled.API.dll</HintPath>
    </Reference>
    <Reference Include="Exiled.Events, Version=9.6.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Exiled.Events.dll</HintPath>
    </Reference>
    <Reference Include="LabApi, Version=1.0.2.0, Culture=neutral, processorArchitecture=AMD64">
      <HintPath>..\packages\Northwood.LabAPI.1.0.2\lib\net48\LabApi.dll</HintPath>
    </Reference>
    <Reference Include="Mirror, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Mirror.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="YamlDotNet, Version=11.0.0.0, Culture=neutral, PublicKeyToken=ec19458f3c15af5e, processorArchitecture=MSIL">
      <HintPath>..\packages\YamlDotNet.11.0.1\lib\net45\YamlDotNet.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Core\Enum\DelayType.cs" />
    <Compile Include="Core\Interface\ICache.cs" />
    <Compile Include="Core\Interface\ICompatibilityAdaptor.cs" />
    <Compile Include="Core\Interface\ICoordinateTool.cs" />
    <Compile Include="Core\Interface\IDestructible.cs" />
    <Compile Include="Core\Interface\IDisplayOutput.cs" />
    <Compile Include="Core\Interface\IFontTool.cs" />
    <Compile Include="Core\Interface\IHintParser.cs" />
    <Compile Include="Core\Interface\ILogger.cs" />
    <Compile Include="Core\Interface\IConcurrentTaskDispatcher.cs" />
    <Compile Include="Core\Interface\IPlayerContext.cs" />
    <Compile Include="Core\Interface\IPlayerDisplay.cs" />
    <Compile Include="Core\Interface\IPool.cs" />
    <Compile Include="Core\Interface\ITaskScheduler.cs" />
    <Compile Include="Core\Models\Arguments\AutoContentUpdateArg.cs" />
    <Compile Include="Core\Models\Arguments\CompatibilityAdaptorArg.cs" />
    <Compile Include="Core\Models\Arguments\DisplayOutputArg.cs" />
    <Compile Include="Core\Models\CharacterInfo.cs" />
    <Compile Include="Core\Models\LineInfo.cs" />
    <Compile Include="Core\Models\ReferenceHubContext.cs" />
    <Compile Include="Core\Models\TextArea.cs" />
    <Compile Include="Core\Models\Arguments\ContentUpdateArg.cs" />
    <Compile Include="Core\Models\Arguments\UpdateAvailableEventArg.cs" />
    <Compile Include="Core\Utilities\Cache.cs" />
    <Compile Include="Core\Utilities\DefaultDisplayOutput.cs" />
    <Compile Include="Core\Utilities\PeriodicRunner.cs" />
    <Compile Include="Core\Utilities\Pools\RichTextParserPool.cs" />
    <Compile Include="Core\Utilities\Tools\Logger.cs" />
    <Compile Include="Core\Utilities\Tools\ConcurrentTaskDispatcher.cs" />
    <Compile Include="Plugin\Commands\GetCompatAssemblyName.cs" />
    <Compile Include="Core\Enum\CaseStyle.cs" />
    <Compile Include="Core\Enum\FontStyle.cs" />
    <Compile Include="Core\Enum\ScriptStyle.cs" />
    <Compile Include="Core\Extension\HintExtension.cs" />
    <Compile Include="Core\Extension\PlayerDisplayExtension.cs" />
    <Compile Include="Core\Interface\IUpdateAnalyser.cs" />
    <Compile Include="Core\Models\HintCollection.cs" />
    <Compile Include="Core\Models\HintContent\AutoContent.cs" />
    <Compile Include="Core\Models\HintContent\AbstractHintContent.cs" />
    <Compile Include="Core\Models\HintContent\StringContent.cs" />
    <Compile Include="Core\Utilities\CompatibilityAdaptor.cs" />
    <Compile Include="Core\Utilities\Pools\StringBuilderPool.cs" />
    <Compile Include="Core\Utilities\TaskScheduler.cs" />
    <Compile Include="Core\Utilities\Tools\CoordinateTools.cs" />
    <Compile Include="Core\Utilities\Parser\HintParser.cs" />
    <Compile Include="Core\Utilities\Patch\Patcher.cs" />
    <Compile Include="Core\Utilities\Tools\FontTool.cs" />
    <Compile Include="Core\Utilities\UpdateAnalyzer.cs" />
    <Compile Include="Core\Extension\PlayerExtension.cs" />
    <Compile Include="Core\Utilities\Parser\RichTextParser.cs" />
    <Compile Include="Core\Models\Hints\AbstractHint.cs" />
    <Compile Include="Core\Models\Hints\DynamicHint.cs" />
    <Compile Include="Core\Utilities\Patch\Patches.cs" />
    <Compile Include="Core\Enum\HintParameters .cs" />
    <Compile Include="UI\Extension\Extension.cs" />
    <Compile Include="UI\Utilities\CommonHint.cs" />
    <Compile Include="UI\Utilities\PlayerUI.cs" />
    <Compile Include="Plugin\Plugin.cs" />
    <Compile Include="Plugin\PluginConfig.cs" />
    <Compile Include="Core\Models\Hints\Hint.cs" />
    <Compile Include="Core\Utilities\PlayerDisplay.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="UpdateLog.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Update="UpdateLog.txt">
      <Pack>false</Pack>
    </None>
    <None Include="App.config" />
    <None Include="packages.config" />
    <EmbeddedResource Include="TextWidth" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>