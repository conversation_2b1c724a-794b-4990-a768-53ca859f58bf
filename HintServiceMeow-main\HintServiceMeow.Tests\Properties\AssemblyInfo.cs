using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("HintServiceMeow.Tests")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("HintServiceMeow.Tests")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("4d5ad48b-e99a-45a1-91e3-f1c01af31b6c")]

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
