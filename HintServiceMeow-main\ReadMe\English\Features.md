# Features Introduction
HintServiceMeow includes the following features

#### Hint Adapter
  HintServiceMeow includes a hint adapter that automatically converts other plugins' hints into HintServiceMeow-compatible Hints. This allows people to use any plugin that uses hints, even if they are neither compatible with each other nor with HintServiceMeow. 

## Developer Features
#### Hint Functions
  This framework provides methods for displaying text in a specific position on the screen without conflicting with other plugins.
#### Auto text
  An auto-update text in the Hint classes.
#### Dynamic Hint
  A hint that can automatically position itself on the screen in the most optimal position. 
#### Auto Update
  Any changes to a hint will be automatically updated on the player's screen.
#### Update forecast
  This framework can analyze the update frequency of each hint and schedule updates accordingly. You can also customize the update delay for each Hint, ranging from Fastest to No-sync.
#### Player UI
  Player UI includes advanced components to simplify your development. For example, the common hint component of PlayerUI allows you to show commonly used hints to a player using a single method.
