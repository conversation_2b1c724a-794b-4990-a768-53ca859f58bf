using System;
using InventorySystem.Items;
using PlayerRoles;

namespace xiaozhoucust.Utils
{
    public static class Extensions
    {
        /// <summary>
        /// 检查物品类型是否为弹药
        /// </summary>
        /// <param name="item">物品类型</param>
        /// <returns>是否为弹药</returns>
        public static bool IsAmmo(this ItemType item)
        {
            return item == ItemType.Ammo9x19 ||
                   item == ItemType.Ammo12gauge ||
                   item == ItemType.Ammo44cal ||
                   item == ItemType.Ammo762x39 ||
                   item == ItemType.Ammo556x45;
        }

        /// <summary>
        /// 检查物品类型是否为武器
        /// </summary>
        /// <param name="type">物品类型</param>
        /// <param name="checkMicro">是否检查MicroHID</param>
        /// <returns>是否为武器</returns>
        public static bool IsWeapon(this ItemType type, bool checkMicro = true)
        {
            switch (type)
            {
                case ItemType.GunCOM15:
                case ItemType.GunE11SR:
                case ItemType.GunCrossvec:
                case ItemType.GunFSP9:
                case ItemType.GunLogicer:
                case ItemType.GunCOM18:
                case ItemType.GunRevolver:
                case ItemType.GunAK:
                case ItemType.GunShotgun:
                case ItemType.ParticleDisruptor:
                case ItemType.GunCom45:
                case ItemType.GunFRMG0:
                case ItemType.Jailbird:
                    return true;
                case ItemType.MicroHID:
                    return checkMicro;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取角色的友好名称
        /// </summary>
        /// <param name="roleType">角色类型</param>
        /// <returns>友好名称</returns>
        public static string GetFriendlyName(this RoleTypeId roleType)
        {
            return roleType switch
            {
                RoleTypeId.Scp049 => "SCP-049",
                RoleTypeId.Scp0492 => "SCP-049-2",
                RoleTypeId.Scp096 => "SCP-096",
                RoleTypeId.Scp106 => "SCP-106",
                RoleTypeId.Scp173 => "SCP-173",
                RoleTypeId.Scp939 => "SCP-939",
                RoleTypeId.Scp3114 => "SCP-3114",
                RoleTypeId.ClassD => "D级人员",
                RoleTypeId.Scientist => "科学家",
                RoleTypeId.FacilityGuard => "设施警卫",
                RoleTypeId.NtfCaptain => "九尾狐指挥官",
                RoleTypeId.NtfSergeant => "九尾狐中士",
                RoleTypeId.NtfSpecialist => "九尾狐收容专家",
                RoleTypeId.NtfPrivate => "九尾狐列兵",
                RoleTypeId.ChaosConscript => "混沌征召兵",
                RoleTypeId.ChaosMarauder => "混沌掠夺者",
                RoleTypeId.ChaosRepressor => "混沌压制者",
                RoleTypeId.ChaosRifleman => "混沌步枪手",
                RoleTypeId.Tutorial => "教程角色",
                RoleTypeId.Spectator => "观察者",
                _ => roleType.ToString()
            };
        }

        /// <summary>
        /// 检查角色是否为人类
        /// </summary>
        /// <param name="roleType">角色类型</param>
        /// <returns>是否为人类</returns>
        public static bool IsHuman(this RoleTypeId roleType)
        {
            return !roleType.IsScp() && roleType != RoleTypeId.Spectator && roleType != RoleTypeId.None;
        }

        /// <summary>
        /// 检查角色是否为SCP
        /// </summary>
        /// <param name="roleType">角色类型</param>
        /// <returns>是否为SCP</returns>
        public static bool IsScp(this RoleTypeId roleType)
        {
            return roleType == RoleTypeId.Scp049 ||
                   roleType == RoleTypeId.Scp0492 ||
                   roleType == RoleTypeId.Scp096 ||
                   roleType == RoleTypeId.Scp106 ||
                   roleType == RoleTypeId.Scp173 ||
                   roleType == RoleTypeId.Scp939 ||
                   roleType == RoleTypeId.Scp3114;
        }
    }
}
