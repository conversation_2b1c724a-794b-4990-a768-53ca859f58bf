using BlackRoseServer.API;
using BlackRoseServer.Display;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using System;
using System.Collections.Concurrent;
using System.Linq;

namespace BlackRoseServer.Helper.Players
{
    /// <summary>
    /// 经验助手类 - 简化版本，使用新的右下角显示系统
    /// </summary>
    public class ExperienceHelper
    {
        private static ExperienceHelper _instance;
        public static ExperienceHelper Instance => _instance ??= new ExperienceHelper();

        private readonly ConcurrentDictionary<Player, DateTime> _lastExperienceTime;

        private ExperienceHelper()
        {
            _lastExperienceTime = new ConcurrentDictionary<Player, DateTime>();
            Logger.Info("ExperienceHelper已初始化（简化版本）");
        }

        /// <summary>
        /// 为玩家初始化（兼容性方法，现在使用新的显示系统）
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void InitForPlayer(Player player)
        {
            if (player == null) return;

            // 新系统不需要初始化，这里只是为了兼容性
            Logger.Debug($"玩家 {player.Nickname} 经验显示已初始化（使用新系统）");
        }

        /// <summary>
        /// 移除玩家显示（兼容性方法）
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void RemovePlayer(Player player)
        {
            if (player == null) return;

            // 清理新系统的数据
            RightBottomDisplayManager.Instance.CleanupPlayerData(player);
            _lastExperienceTime.TryRemove(player, out _);

            Logger.Debug($"玩家 {player.Nickname} 经验显示已清理");
        }

        /// <summary>
        /// 显示玩家的经验获得（简化版本，使用新的显示系统）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="gainedXP">获得的经验值</param>
        /// <param name="isLevelUp">是否升级</param>
        /// <param name="displayDuration">显示时长（秒）</param>
        public void ShowExperienceProgress(Player player, int gainedXP, bool isLevelUp = false, float displayDuration = 3f)
        {
            if (player == null || player.DoNotTrack) return;

            try
            {
                // 使用新的右下角显示系统
                RightBottomDisplayManager.Instance.ShowExperienceGain(player, gainedXP, isLevelUp);

                // 记录最后显示时间
                _lastExperienceTime.TryAdd(player, DateTime.Now);

                Logger.Debug($"显示经验获得 - 玩家: {player.Nickname}, 经验: +{gainedXP}, 升级: {isLevelUp}");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示经验进度失败: {ex.Message}");
                Logger.Debug($"ShowExperienceProgress详细错误: {ex}");
            }
        }

        /// <summary>
        /// 获取玩家最后经验显示时间
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>最后显示时间</returns>
        public DateTime? GetLastExperienceTime(Player player)
        {
            if (player == null) return null;
            return _lastExperienceTime.TryGetValue(player, out var time) ? time : null;
        }

        /// <summary>
        /// 重置所有经验显示数据
        /// </summary>
        public void Reset()
        {
            try
            {
                // 清理所有玩家的显示数据
                var players = _lastExperienceTime.Keys.ToList();
                foreach (var player in players)
                {
                    RightBottomDisplayManager.Instance.CleanupPlayerData(player);
                }

                _lastExperienceTime.Clear();
                Logger.Info("经验显示系统已重置");
            }
            catch (Exception ex)
            {
                Logger.Error($"重置经验显示系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetStatistics()
        {
            try
            {
                return $"ExperienceHelper统计:\n" +
                       $"- 跟踪的玩家数量: {_lastExperienceTime.Count}";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取经验助手统计信息失败: {ex.Message}");
                return "统计信息获取失败";
            }
        }
    }
}
