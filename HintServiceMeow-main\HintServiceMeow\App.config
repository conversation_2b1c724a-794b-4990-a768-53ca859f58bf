﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="0Harmony" publicKeyToken="null" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.3.6.0" newVersion="2.3.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="LabApi" publicKeyToken="null" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-0.4.0.0" newVersion="0.4.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="YamlDotNet" publicKeyToken="ec19458f3c15af5e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-16.0.0.0" newVersion="16.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>