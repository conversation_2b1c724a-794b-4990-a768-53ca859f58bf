﻿# Timers ![Downloads](https://img.shields.io/github/downloads/LumiFae/Timers/total)

A simple plugin that adds spawn timers below each spawn bar in the Spectator UI.

![Example](https://github.com/LumiFae/Timers/blob/master/imgs/snippet.png)

Dependencies:

[RueI](https://github.com/LolaLollipop/RueI) or [HintServiceMeow](https://github.com/MeowServer/HintServiceMeow)

### Timers is breaking on the 2nd round?

Rue<PERSON> may be the cause, it has issues with 2 very common plugins from what I've seen, in this case though you can use HSM, which doesn't go in the dependencies folder, but the plugins folder, you can download HSM from [here](https://github.com/MeowServer/HintServiceMeow/releases/latest).

## Download

You can download the latest release from the [releases](https://github.com/LumiFae/Timers/releases/latest) page.

Decide on what framework you are chosing to use first, either LabAPI or Exiled. Only difference here is how you load the plugins, you can use LabAPI on any EXILED server anyway. I recommend LabAPI.

Then choose on the hint framework, you either have RueI or HSM, your choice.

Download the right `.dll` file depending on those options.

If you are using neither RueI or HSM as your hint frameworks, I recommend RueI, if you don't already have RueI on your server installed, you can get it from [here](https://github.com/pawslee/RueI/releases/latest).

Put the downloaded plugin file inside the framework you have chosen's plugin folder, and then if you are using RueI, put RueI.dll into dependencies for that specific framework. If HSM, HSM is a plugin so that must be put in the plugins folder.

Restart your server, change config values (if needed) and you're good to go!

## Support

Need support? Join my Discord: https://discord.gg/4XGASxrN
