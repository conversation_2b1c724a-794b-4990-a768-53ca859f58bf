﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Mirror;

namespace BlackRoseServer
{
    public class FakeConnection : NetworkConnectionToClient
    {
        public FakeConnection(int networkConnectionId)
            : base(networkConnectionId)
        {
        }
        public override string address => "localhost";
        public override void Send(ArraySegment<byte> segment, int channelId = 0)
        {
        }
    }
}
