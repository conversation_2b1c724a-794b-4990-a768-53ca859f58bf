using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.UI.Extension;
using HintServiceMeow.Core.Utilities;
using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using NorthwoodLib.Pools;
using Respawning;
using Respawning.Waves;
using PlayerRoles;
using System.Collections.Concurrent;
using MEC;

namespace BlackRoseServer.Helper
{
    /// <summary>
    /// 计时器显示助手类
    /// </summary>
    public class TimerDisplayHelper
    {
        private static TimerDisplayHelper _instance;
        public static TimerDisplayHelper Instance => _instance ??= new TimerDisplayHelper();

        private readonly ConcurrentDictionary<Player, Hint> _timerHints = new();
        private readonly object _syncLock = new();

        // 重生波次引用
        private NtfSpawnWave _ntfWave;
        private NtfMiniWave _ntfMiniWave;
        private ChaosSpawnWave _chaosWave;
        private ChaosMiniWave _chaosMiniWave;

        // 回合开始时间
        private DateTime _roundStartTime;

        // 协程句柄
        private CoroutineHandle _managementCoroutine;

        private TimerDisplayHelper() { }

        /// <summary>
        /// 初始化计时器显示系统
        /// </summary>
        public void Initialize()
        {
            try
            {
                if (Config.Instance == null)
                {
                    Logger.Warn("Config.Instance为null，跳过计时器系统初始化");
                    return;
                }

                if (!Config.Instance.EnableRespawnTimers && !Config.Instance.EnableRoundTimer)
                    return;

                Logger.Info("初始化计时器显示系统");
                _roundStartTime = DateTime.Now;

                // 获取重生波次
                try
                {
                    if (WaveManager.TryGet(out NtfSpawnWave ntfWave))
                    {
                        _ntfWave = ntfWave;
                        Logger.Debug("找到NTF重生波次");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"获取NTF重生波次失败: {ex.Message}");
                }

                try
                {
                    if (WaveManager.TryGet(out NtfMiniWave ntfMiniWave))
                    {
                        _ntfMiniWave = ntfMiniWave;
                        Logger.Debug("找到NTF小波次");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"获取NTF小波次失败: {ex.Message}");
                }

                try
                {
                    if (WaveManager.TryGet(out ChaosSpawnWave chaosWave))
                    {
                        _chaosWave = chaosWave;
                        Logger.Debug("找到混沌重生波次");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"获取混沌重生波次失败: {ex.Message}");
                }

                try
                {
                    if (WaveManager.TryGet(out ChaosMiniWave chaosMiniWave))
                    {
                        _chaosMiniWave = chaosMiniWave;
                        Logger.Debug("找到混沌小波次");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warn($"获取混沌小波次失败: {ex.Message}");
                }

                // 启动每秒检查协程
                _managementCoroutine = Timing.RunCoroutine(TimerManagementCoroutine());

                Logger.Debug("计时器显示系统初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"初始化计时器显示系统失败: {ex.Message}");
                Logger.Debug($"Initialize详细错误: {ex}");
            }
        }

        /// <summary>
        /// 计时器管理协程，每秒检查观察者和OW模式玩家
        /// </summary>
        private IEnumerator<float> TimerManagementCoroutine()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(1f);

                try
                {
                    if (Config.Instance == null || (!Config.Instance.EnableRespawnTimers && !Config.Instance.EnableRoundTimer))
                        continue;

                    // 使用XHelper.PlayerList获取假人除外的玩家列表
                    var playerList = XHelper.PlayerList?.Where(p => p != null && p.IsReady).ToList();
                    if (playerList == null || playerList.Count == 0)
                        continue;

                    // 检查所有玩家，为观察者和OW模式添加计时器
                    foreach (var player in playerList)
                    {
                        try
                        {
                            bool shouldShowTimer = player.Role == RoleTypeId.Spectator || player.Role == RoleTypeId.Overwatch;
                            bool currentlyShowingTimer = _timerHints.ContainsKey(player);

                            if (shouldShowTimer && !currentlyShowingTimer)
                            {
                                InitForPlayer(player);
                            }
                            else if (!shouldShowTimer && currentlyShowingTimer)
                            {
                                RemovePlayer(player);
                            }
                        }
                        catch (Exception playerEx)
                        {
                            Logger.Debug($"处理玩家 {player?.Nickname} 计时器时出错: {playerEx.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"计时器管理协程错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 为玩家初始化计时器显示
        /// </summary>
        public void InitForPlayer(Player player)
        {
            try
            {
                // 检查配置是否可用
                if (Config.Instance == null)
                {
                    Logger.Warn("Config.Instance为null，跳过计时器初始化");
                    return;
                }

                if (!Config.Instance.EnableRespawnTimers && !Config.Instance.EnableRoundTimer)
                    return;

                if (player == null)
                {
                    Logger.Warn("玩家为null，跳过计时器初始化");
                    return;
                }

                // 检查玩家ReferenceHub是否可用
                if (player.ReferenceHub == null)
                {
                    Logger.Warn($"玩家 {player.Nickname} 的ReferenceHub为null，跳过计时器初始化");
                    return;
                }

                // 只为观察者和管理员显示
                if (player.Role != RoleTypeId.Spectator && player.Role != RoleTypeId.Overwatch)
                    return;

                lock (_syncLock)
                {
                    if (_timerHints.ContainsKey(player))
                        return;

                    var hint = CreateTimerHint();
                    if (hint == null)
                    {
                        Logger.Warn("创建计时器提示失败");
                        return;
                    }

                    var playerDisplay = PlayerDisplay.Get(player.ReferenceHub);
                    if (playerDisplay == null)
                    {
                        Logger.Warn($"无法获取玩家 {player.Nickname} 的PlayerDisplay");
                        return;
                    }

                    _timerHints[player] = hint;
                    playerDisplay.AddHint(hint);

                    Logger.Info($"为玩家 {player.Nickname} 初始化计时器显示成功");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"初始化玩家计时器显示失败: {ex.Message}");
                Logger.Debug($"InitForPlayer详细错误: {ex}");
            }
        }

        /// <summary>
        /// 移除玩家的计时器显示
        /// </summary>
        public void RemovePlayer(Player player)
        {
            try
            {
                if (player == null) return;

                lock (_syncLock)
                {
                    if (_timerHints.TryRemove(player, out var hint))
                    {
                        if (player.ReferenceHub != null)
                        {
                            var playerDisplay = PlayerDisplay.Get(player.ReferenceHub);
                            playerDisplay?.RemoveHint(hint);
                        }
                        Logger.Debug($"移除玩家 {player.Nickname} 的计时器显示");
                    }
                    // 如果玩家不在字典中，不记录日志，避免重复日志
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除玩家计时器显示失败: {ex.Message}");
                Logger.Debug($"RemovePlayer详细错误: {ex}");
            }
        }

        /// <summary>
        /// 处理玩家角色变更
        /// </summary>
        public void HandleRoleChange(Player player, RoleTypeId newRole)
        {
            try
            {
                if (Config.Instance == null || player == null)
                    return;

                if (!Config.Instance.EnableRespawnTimers && !Config.Instance.EnableRoundTimer)
                    return;

                // 如果角色是None或Destroyed，直接移除计时器（通常是玩家离开或回合结束）
                if (newRole == RoleTypeId.None || newRole.ToString().Contains("Destroyed"))
                {
                    RemovePlayer(player);
                    return;
                }

                bool shouldShowTimer = newRole == RoleTypeId.Spectator || newRole == RoleTypeId.Overwatch;
                bool currentlyShowingTimer = _timerHints.ContainsKey(player);

                if (shouldShowTimer && !currentlyShowingTimer)
                {
                    InitForPlayer(player);
                }
                else if (!shouldShowTimer && currentlyShowingTimer)
                {
                    RemovePlayer(player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理玩家角色变更失败: {ex.Message}");
                Logger.Debug($"HandleRoleChange详细错误: {ex}");
            }
        }

        /// <summary>
        /// 创建计时器提示
        /// </summary>
        private Hint CreateTimerHint()
        {
            return new Hint
            {
                Alignment = HintAlignment.Center,
                YCoordinate = 100, // 调整位置避免与其他UI重叠
                FontSize = 28, // 稍微减小字体大小
                LineHeight = 8, // 增加行间距
                SyncSpeed = HintSyncSpeed.Fast,
                AutoText = _ => GetTimerText()
            };
        }

        /// <summary>
        /// 获取NTF重生时间
        /// </summary>
        private TimeSpan GetNtfRespawnTime()
        {
            if (_ntfMiniWave != null && !_ntfMiniWave.Timer.IsPaused)
                return TimeSpan.FromSeconds(_ntfMiniWave.Timer.TimeLeft);

            return _ntfWave != null ? TimeSpan.FromSeconds(_ntfWave.Timer.TimeLeft) : TimeSpan.Zero;
        }

        /// <summary>
        /// 获取混沌重生时间
        /// </summary>
        private TimeSpan GetChaosRespawnTime()
        {
            if (_chaosMiniWave != null && !_chaosMiniWave.Timer.IsPaused)
                return TimeSpan.FromSeconds(_chaosMiniWave.Timer.TimeLeft);

            return _chaosWave != null ? TimeSpan.FromSeconds(_chaosWave.Timer.TimeLeft) : TimeSpan.Zero;
        }

        /// <summary>
        /// 获取回合进行时长
        /// </summary>
        private TimeSpan GetRoundDuration()
        {
            return DateTime.Now - _roundStartTime;
        }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        private string FormatTime(TimeSpan time)
        {
            return $"<size=24>{time.Minutes:D2}</size><size=18>分</size><size=24>{time.Seconds:D2}</size><size=18>秒</size>";
        }

        /// <summary>
        /// 获取计时器文本
        /// </summary>
        private string GetTimerText()
        {
            StringBuilder builder = StringBuilderPool.Shared.Rent();
            builder.Append("<align=center>");

            bool hasContent = false;

            // 显示重生计时器
            if (Config.Instance.EnableRespawnTimers)
            {
                TimeSpan ntfTime = GetNtfRespawnTime() + TimeSpan.FromSeconds(18);
                if (ntfTime < TimeSpan.Zero) ntfTime = TimeSpan.Zero;

                TimeSpan chaosTime = GetChaosRespawnTime() + TimeSpan.FromSeconds(13);
                if (chaosTime < TimeSpan.Zero) chaosTime = TimeSpan.Zero;

                // 添加标题
                builder.Append("<color=#CCCCCC><size=20>重生倒计时</size></color>\n");

                // NTF计时器
                builder.Append("<color=#4A90E2>九尾狐</color> ");
                if (WaveManager._nextWave != null && WaveManager._nextWave.TargetFaction == Faction.FoundationStaff && ntfTime.TotalSeconds <= 18)
                    builder.Append("<color=#00BFFF>").Append(FormatTime(ntfTime)).Append("</color>");
                else
                    builder.Append("<color=#87CEEB>").Append(FormatTime(ntfTime)).Append("</color>");

                builder.Append("   ");

                // 混沌计时器
                builder.Append("<color=#FF6B6B>混沌分裂者</color> ");
                if (WaveManager._nextWave != null && WaveManager._nextWave.TargetFaction == Faction.FoundationEnemy && chaosTime.TotalSeconds <= 13)
                    builder.Append("<color=#FF4500>").Append(FormatTime(chaosTime)).Append("</color>");
                else
                    builder.Append("<color=#FFA07A>").Append(FormatTime(chaosTime)).Append("</color>");

                hasContent = true;
            }

            // 显示回合时长（换行显示避免重叠）
            if (Config.Instance.EnableRoundTimer)
            {
                if (hasContent)
                    builder.AppendLine(); // 换行而不是空格

                TimeSpan roundTime = GetRoundDuration();
                builder.Append("<color=#FFD700>回合时长</color> <color=#FFFF99>").Append(FormatTime(roundTime)).Append("</color>");
            }

            builder.Append("</align>");
            return StringBuilderPool.Shared.ToStringReturn(builder);
        }

        /// <summary>
        /// 清理所有计时器显示
        /// </summary>
        public void Cleanup()
        {
            try
            {
                // 先停止协程，避免在清理过程中继续处理
                if (_managementCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_managementCoroutine);
                    Logger.Debug("计时器管理协程已停止");
                }

                lock (_syncLock)
                {
                    foreach (var kvp in _timerHints)
                    {
                        try
                        {
                            if (kvp.Key?.ReferenceHub != null)
                            {
                                var playerDisplay = PlayerDisplay.Get(kvp.Key.ReferenceHub);
                                playerDisplay?.RemoveHint(kvp.Value);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"清理单个计时器显示失败: {ex.Message}");
                        }
                    }
                    _timerHints.Clear();
                    Logger.Debug("计时器显示系统清理完成");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清理计时器显示系统失败: {ex.Message}");
                Logger.Debug($"Cleanup详细错误: {ex}");
            }
        }
    }
}
