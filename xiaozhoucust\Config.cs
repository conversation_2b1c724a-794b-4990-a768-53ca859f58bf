using System.Collections.Generic;
using System.ComponentModel;

namespace xiaozhoucust
{
    public class Config
    {
        [Description("是否启用无限子弹功能")]
        public bool EnableInfiniteAmmo { get; set; } = true;

        [Description("无限子弹刷新间隔（秒）")]
        public float InfiniteAmmoRefreshInterval { get; set; } = 3f;

        [Description("是否阻止玩家丢弃弹药")]
        public bool PreventAmmoDrop { get; set; } = true;

        [Description("弹药数量配置")]
        public AmmoConfig AmmoSettings { get; set; } = new AmmoConfig();

        [Description("是否启用HintUI功能")]
        public bool EnableHintUI { get; set; } = true;

        [Description("是否显示重生计时器")]
        public bool ShowRespawnTimer { get; set; } = true;

        [Description("是否显示群号和服务器名称")]
        public bool ShowServerInfo { get; set; } = true;

        [Description("群号")]
        public string GroupNumber { get; set; } = "123456789";

        [Description("服务器名称")]
        public string ServerName { get; set; } = "test服务器";

        [Description("群号显示位置Y坐标")]
        public float GroupNumberYPosition { get; set; } = 900f;

        [Description("服务器名称显示位置Y坐标")]
        public float ServerNameYPosition { get; set; } = 920f;

        [Description("重生计算器字体大小")]
        public int RespawnTimerFontSize { get; set; } = 35;

        [Description("群号字体大小")]
        public int GroupNumberFontSize { get; set; } = 25;

        [Description("服务器名称字体大小")]
        public int ServerNameFontSize { get; set; } = 25;

        [Description("是否启用SCP血量调整")]
        public bool EnableScpHealthAdjustment { get; set; } = true;

        [Description("SCP血量配置 - 格式: SCP名称=血量值")]
        public Dictionary<string, float> ScpHealthSettings { get; set; } = new Dictionary<string, float>
        {
            { "Scp049", 1700f },
            { "Scp0492", 500f },
            { "Scp096", 2000f },
            { "Scp106", 650f },
            { "Scp173", 3200f },
            { "Scp939", 2200f },
            { "Scp3114", 1200f }
        };

        [Description("是否启用调试日志")]
        public bool EnableDebugLog { get; set; } = false;
    }

    public class AmmoConfig
    {
        [Description("9毫米弹数量")]
        public ushort Ammo9x19 { get; set; } = 180;

        [Description("霰弹数量")]
        public ushort Ammo12gauge { get; set; } = 18;

        [Description(".44手枪弹数量")]
        public ushort Ammo44cal { get; set; } = 18;

        [Description("7.62子弹数量")]
        public ushort Ammo762x39 { get; set; } = 180;

        [Description("5.56子弹数量")]
        public ushort Ammo556x45 { get; set; } = 180;
    }
}
