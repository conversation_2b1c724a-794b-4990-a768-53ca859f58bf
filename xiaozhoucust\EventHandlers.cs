using System;
using LabApi.Events.Arguments.ServerEvents;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Features.Console;
using LabApi.Features.Wrappers;
using HintServiceMeow.Core.Utilities;
using MEC;

namespace xiaozhoucust
{
    /// <summary>
    /// 事件处理器类
    /// 负责处理所有LabAPI事件并分发给相应的功能模块
    /// </summary>
    public class EventHandlers
    {
        private readonly Plugin _plugin;

        public EventHandlers(Plugin plugin)
        {
            _plugin = plugin ?? throw new ArgumentNullException(nameof(plugin));
        }

        /// <summary>
        /// 处理回合开始事件
        /// </summary>
        public void OnRoundStarting(RoundStartingEventArgs ev)
        {
            try
            {


                // 延迟3秒后启动功能，避免回合开始时的阻塞
                Timing.CallDelayed(3f, () =>
                {
                    try
                    {
                        // 启动无限子弹功能
                        if (_plugin.Config.EnableInfiniteAmmo)
                        {
                            _plugin.InfiniteAmmo?.StartInfiniteAmmo();
                        }

                        // 启动重生计时器
                        if (_plugin.Config.EnableRespawnTimer)
                        {
                            _plugin.RespawnTimer?.StartTimer();
                        }

                        // 服务器信息现在集成在重生计时器中，不需要单独启动
                        // if (_plugin.Config.EnableServerInfoDisplay)
                        // {
                        //     _plugin.ServerInfoDisplay?.StartDisplay();
                        // }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"延迟初始化功能失败: {ex.Message}");
                        if (_plugin.Config.Debug)
                        {
                            Logger.Debug($"详细错误信息: {ex}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"回合开始事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理回合结束事件
        /// </summary>
        public void OnRoundEnded(RoundEndedEventArgs ev)
        {
            try
            {

                // 停止无限子弹功能
                _plugin.InfiniteAmmo?.StopInfiniteAmmo();

                // 停止重生计时器
                _plugin.RespawnTimer?.StopTimer();

                // 停止服务器信息显示
                _plugin.ServerInfoDisplay?.StopDisplay();

            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家加入事件
        /// </summary>
        public void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

            }
            catch (Exception ex)
            {
                Logger.Error($"玩家加入事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家离开事件
        /// </summary>
        public void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

                // 新的DynamicHint系统会自动处理玩家离开
                // 不需要手动移除玩家
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家离开事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家生成事件
        /// </summary>
        public void OnPlayerSpawning(PlayerSpawningEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;



                // 如果是SCP角色，调整血量
                if (_plugin.Config.EnableScpHealthAdjustment && ev.Role != null && IsSCPRole(ev.Role.RoleTypeId))
                {
                    _plugin.ScpHealthAdjuster?.AdjustScpHealth(ev.Player, ev.Role.RoleTypeId);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家生成事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家角色变更事件
        /// </summary>
        public void OnPlayerChangingRole(PlayerChangingRoleEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;



                // 处理HintServiceMeow显示
                HandleHintDisplay(ev.Player, ev.NewRole);
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家角色变更事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家丢弃弹药事件
        /// </summary>
        public void OnPlayerDroppingAmmo(PlayerDroppingAmmoEventArgs ev)
        {
            try
            {
                // 如果启用了无限子弹功能，阻止玩家丢弃弹药
                if (_plugin.Config.EnableInfiniteAmmo)
                {
                    ev.IsAllowed = false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家丢弃弹药事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 检查角色是否为SCP
        /// </summary>
        private bool IsSCPRole(PlayerRoles.RoleTypeId role)
        {
            return role switch
            {
                PlayerRoles.RoleTypeId.Scp173 => true,
                PlayerRoles.RoleTypeId.Scp106 => true,
                PlayerRoles.RoleTypeId.Scp049 => true,
                PlayerRoles.RoleTypeId.Scp0492 => true,
                PlayerRoles.RoleTypeId.Scp096 => true,
                PlayerRoles.RoleTypeId.Scp939 => true,
                PlayerRoles.RoleTypeId.Scp079 => true,
                PlayerRoles.RoleTypeId.Scp3114 => true,
                _ => false
            };
        }

        /// <summary>
        /// 处理Hint显示，参考Timers-master的实现
        /// </summary>
        private void HandleHintDisplay(Player player, PlayerRoles.RoleTypeId newRole)
        {
            try
            {
                bool willSendHint = CanSendHint(newRole);
                bool currentlySendingHint = CanSendHint(player.Role);

                if (willSendHint == currentlySendingHint) return;


                var display = PlayerDisplay.Get(player);
                if (display == null) return;

                if (willSendHint && player.ReferenceHub != null)
                {
                    // 添加重生计时器显示
                    if (_plugin.Config.EnableRespawnTimer && _plugin.RespawnTimer?._respawnTimerDisplay != null)
                    {
                        if (display.GetHint(_plugin.RespawnTimer._respawnTimerDisplay.Guid) == null)
                        {
                            display.AddHint(_plugin.RespawnTimer._respawnTimerDisplay);
                        }
                    }

                    // 添加服务器信息显示
                    if (_plugin.Config.EnableServerInfoDisplay && _plugin.ServerInfoDisplay?._serverInfoDisplay != null)
                    {
                        if (display.GetHint(_plugin.ServerInfoDisplay._serverInfoDisplay.Guid) == null)
                        {
                            display.AddHint(_plugin.ServerInfoDisplay._serverInfoDisplay);
                        }
                    }
                }
                else
                {
                    // 移除显示
                    if (_plugin.RespawnTimer?._respawnTimerDisplay != null)
                    {
                        display.RemoveHint(_plugin.RespawnTimer._respawnTimerDisplay);
                    }
                    if (_plugin.ServerInfoDisplay?._serverInfoDisplay != null)
                    {
                        display.RemoveHint(_plugin.ServerInfoDisplay._serverInfoDisplay);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"处理Hint显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否可以发送Hint（只有观察者）
        /// </summary>
        private bool CanSendHint(PlayerRoles.RoleTypeId roleId)
        {
            return roleId is PlayerRoles.RoleTypeId.Spectator or PlayerRoles.RoleTypeId.Overwatch;
        }
    }
}
