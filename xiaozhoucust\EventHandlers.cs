using System;
using LabApi.Events.Arguments.ServerEvents;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Features.Console;
using LabApi.Features.Wrappers;
using PlayerRoles;

namespace xiaozhoucust
{
    /// <summary>
    /// 事件处理器类
    /// 负责处理所有LabAPI事件并分发给相应的功能模块
    /// </summary>
    public class EventHandlers
    {
        private readonly Plugin _plugin;

        public EventHandlers(Plugin plugin)
        {
            _plugin = plugin ?? throw new ArgumentNullException(nameof(plugin));
        }

        /// <summary>
        /// 处理回合开始事件
        /// </summary>
        public void OnRoundStarting(RoundStartingEventArgs ev)
        {
            try
            {

                // 启动无限子弹功能
                if (_plugin.Config.EnableInfiniteAmmo)
                {
                    _plugin.InfiniteAmmo?.StartInfiniteAmmo();
                }

                // 启动重生计时器
                if (_plugin.Config.EnableRespawnTimer)
                {
                    _plugin.RespawnTimer?.StartTimer();
                }

                // 启动服务器信息显示
                if (_plugin.Config.EnableServerInfoDisplay)
                {
                    _plugin.ServerInfoDisplay?.StartDisplay();
                }

                Logger.Info("所有功能模块初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"回合开始事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理回合结束事件
        /// </summary>
        public void OnRoundEnded(RoundEndedEventArgs ev)
        {
            try
            {

                // 停止无限子弹功能
                _plugin.InfiniteAmmo?.StopInfiniteAmmo();

                // 停止重生计时器
                _plugin.RespawnTimer?.StopTimer();

                // 停止服务器信息显示
                _plugin.ServerInfoDisplay?.StopDisplay();

            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家加入事件
        /// </summary>
        public void OnPlayerJoined(PlayerJoinedEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

                Logger.Debug($"玩家 {ev.Player.Nickname} 加入服务器");

                // 为新玩家启动重生计时器显示
                if (_plugin.Config.EnableRespawnTimer)
                {
                    _plugin.RespawnTimer?.AddPlayer(ev.Player);
                }

                // 为新玩家启动服务器信息显示
                if (_plugin.Config.EnableServerInfoDisplay)
                {
                    _plugin.ServerInfoDisplay?.AddPlayer(ev.Player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家加入事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家离开事件
        /// </summary>
        public void OnPlayerLeft(PlayerLeftEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

                Logger.Debug($"玩家 {ev.Player.Nickname} 离开服务器");

                // 清理玩家的重生计时器显示
                _plugin.RespawnTimer?.RemovePlayer(ev.Player);

                // 清理玩家的服务器信息显示
                _plugin.ServerInfoDisplay?.RemovePlayer(ev.Player);
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家离开事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家生成事件
        /// </summary>
        public void OnPlayerSpawning(PlayerSpawningEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

                Logger.Debug($"玩家 {ev.Player.Nickname} 正在生成为角色 {ev.Role}");

                // 如果是SCP角色，调整血量
                if (_plugin.Config.EnableScpHealthAdjustment && IsSCPRole(ev.Role))
                {
                    _plugin.ScpHealthAdjuster?.AdjustScpHealth(ev.Player, ev.Role);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家生成事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家角色变更事件
        /// </summary>
        public void OnPlayerChangingRole(PlayerChangingRoleEventArgs ev)
        {
            try
            {
                if (ev.Player == null) return;

                Logger.Debug($"玩家 {ev.Player.Nickname} 角色变更为 {ev.NewRole}");

                // 更新重生计时器显示
                if (_plugin.Config.EnableRespawnTimer)
                {
                    _plugin.RespawnTimer?.UpdatePlayerRole(ev.Player, ev.NewRole);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家角色变更事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 处理玩家丢弃弹药事件
        /// </summary>
        public void OnPlayerDroppingAmmo(PlayerDroppingAmmoEventArgs ev)
        {
            try
            {
                // 如果启用了无限子弹功能，阻止玩家丢弃弹药
                if (_plugin.Config.EnableInfiniteAmmo)
                {
                    ev.IsAllowed = false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"玩家丢弃弹药事件处理失败: {ex.Message}");
                if (_plugin.Config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 检查角色是否为SCP
        /// </summary>
        private bool IsSCPRole(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp173 => true,
                RoleTypeId.Scp106 => true,
                RoleTypeId.Scp049 => true,
                RoleTypeId.Scp0492 => true,
                RoleTypeId.Scp096 => true,
                RoleTypeId.Scp939 => true,
                RoleTypeId.Scp079 => true,
                _ => false
            };
        }
    }
}
