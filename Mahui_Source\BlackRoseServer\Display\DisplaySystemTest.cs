using System;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Helper;
using PlayerRoles;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// 显示系统测试类
    /// </summary>
    public static class DisplaySystemTest
    {
        /// <summary>
        /// 执行基本功能测试
        /// </summary>
        public static void RunBasicTests()
        {
            Logger.Info("开始执行显示系统基本功能测试...");
            
            try
            {
                // 测试区域检测系统
                TestZoneDetector();
                
                // 测试简单经验显示
                TestSimpleExperienceDisplay();
                
                // 测试右下角显示管理器
                TestRightBottomDisplayManager();
                
                Logger.Info("所有显示系统基本功能测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示系统测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试区域检测系统
        /// </summary>
        private static void TestZoneDetector()
        {
            Logger.Info("测试区域检测系统...");
            
            // 测试区域名称获取
            var surfaceName = ZoneDetector.GetZoneName(ZoneDetector.ZoneType.Surface);
            if (surfaceName != "地表")
            {
                throw new Exception("地表区域名称获取失败");
            }
            
            var lightName = ZoneDetector.GetZoneName(ZoneDetector.ZoneType.LightContainment);
            if (lightName != "轻收")
            {
                throw new Exception("轻收容区名称获取失败");
            }
            
            // 测试颜色获取
            var surfaceColor = ZoneDetector.GetZoneColor(ZoneDetector.ZoneType.Surface);
            if (string.IsNullOrEmpty(surfaceColor))
            {
                throw new Exception("地表区域颜色获取失败");
            }
            
            // 测试带颜色的区域名称
            var coloredName = ZoneDetector.GetColoredZoneName(ZoneDetector.ZoneType.Surface);
            if (!coloredName.Contains("地表"))
            {
                throw new Exception("带颜色区域名称生成失败");
            }
            
            Logger.Info("✅ 区域检测系统测试通过");
        }

        /// <summary>
        /// 测试简单经验显示
        /// </summary>
        private static void TestSimpleExperienceDisplay()
        {
            Logger.Info("测试简单经验显示系统...");
            
            var display = SimpleExperienceDisplay.Instance;
            if (display == null)
            {
                throw new Exception("SimpleExperienceDisplay初始化失败");
            }
            
            // 测试统计信息
            var stats = display.GetStatistics();
            if (string.IsNullOrEmpty(stats))
            {
                throw new Exception("获取经验显示统计信息失败");
            }
            
            // 测试活跃提示数量
            var count = display.GetActiveHintCount();
            if (count < 0)
            {
                throw new Exception("获取活跃提示数量失败");
            }
            
            Logger.Info("✅ 简单经验显示系统测试通过");
        }

        /// <summary>
        /// 测试右下角显示管理器
        /// </summary>
        private static void TestRightBottomDisplayManager()
        {
            Logger.Info("测试右下角显示管理器...");
            
            var manager = RightBottomDisplayManager.Instance;
            if (manager == null)
            {
                throw new Exception("RightBottomDisplayManager初始化失败");
            }
            
            // 测试统计信息
            var stats = manager.GetStatistics();
            if (string.IsNullOrEmpty(stats))
            {
                throw new Exception("获取显示管理器统计信息失败");
            }
            
            Logger.Info("✅ 右下角显示管理器测试通过");
        }

        /// <summary>
        /// 执行集成测试（需要真实的玩家对象）
        /// </summary>
        /// <param name="testPlayer">测试玩家</param>
        public static void RunIntegrationTests(Player testPlayer)
        {
            if (testPlayer == null)
            {
                Logger.Warn("集成测试需要真实的玩家对象，跳过测试");
                return;
            }

            Logger.Info($"开始执行显示系统集成测试 - 测试玩家: {testPlayer.Nickname}");
            
            try
            {
                // 测试经验显示
                TestExperienceDisplay(testPlayer);
                
                // 测试SCP显示（如果是SCP）
                if (testPlayer.IsSCP)
                {
                    TestSCPDisplay(testPlayer);
                }
                
                // 测试区域检测
                TestPlayerZoneDetection(testPlayer);
                
                Logger.Info("所有显示系统集成测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示系统集成测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
            finally
            {
                // 清理测试数据
                RightBottomDisplayManager.Instance.CleanupPlayerData(testPlayer);
            }
        }

        /// <summary>
        /// 测试经验显示
        /// </summary>
        private static void TestExperienceDisplay(Player testPlayer)
        {
            Logger.Info("测试经验显示功能...");
            
            if (!testPlayer.IsSCP)
            {
                // 测试经验获得显示
                RightBottomDisplayManager.Instance.ShowExperienceGain(testPlayer, 5, false);
                
                // 测试带消息的经验显示
                RightBottomDisplayManager.Instance.ShowExperienceGainWithMessage(testPlayer, 3, "测试经验", "#00FF00");
            }
            
            Logger.Info("✅ 经验显示功能测试通过");
        }

        /// <summary>
        /// 测试SCP显示
        /// </summary>
        private static void TestSCPDisplay(Player testPlayer)
        {
            Logger.Info("测试SCP显示功能...");
            
            if (testPlayer.Role == RoleTypeId.Scp079)
            {
                // 测试SCP-079信息显示
                RightBottomDisplayManager.Instance.UpdateSCP079Info(testPlayer);
            }
            else if (testPlayer.IsSCP)
            {
                // 测试SCP队友信息显示
                RightBottomDisplayManager.Instance.UpdateSCPTeammateInfo(testPlayer);
            }
            
            Logger.Info("✅ SCP显示功能测试通过");
        }

        /// <summary>
        /// 测试玩家区域检测
        /// </summary>
        private static void TestPlayerZoneDetection(Player testPlayer)
        {
            Logger.Info("测试玩家区域检测...");
            
            var zone = ZoneDetector.GetPlayerZone(testPlayer);
            var zoneName = ZoneDetector.GetZoneName(zone);
            var coloredZone = ZoneDetector.GetPlayerColoredZone(testPlayer);
            
            Logger.Info($"玩家 {testPlayer.Nickname} 当前区域: {zoneName} ({zone})");
            Logger.Info($"带颜色区域名称: {coloredZone}");
            
            // 测试调试信息
            var debugInfo = ZoneDetector.GetDebugInfo(testPlayer);
            if (string.IsNullOrEmpty(debugInfo))
            {
                throw new Exception("获取区域调试信息失败");
            }
            
            Logger.Info("✅ 玩家区域检测测试通过");
        }

        /// <summary>
        /// 执行性能测试
        /// </summary>
        public static void RunPerformanceTests()
        {
            Logger.Info("开始执行显示系统性能测试...");
            
            try
            {
                // 测试区域检测性能
                TestZoneDetectionPerformance();
                
                // 测试显示管理器性能
                TestDisplayManagerPerformance();
                
                Logger.Info("所有显示系统性能测试通过！");
            }
            catch (Exception ex)
            {
                Logger.Error($"显示系统性能测试失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试区域检测性能
        /// </summary>
        private static void TestZoneDetectionPerformance()
        {
            Logger.Info("测试区域检测性能...");
            
            var startTime = DateTime.Now;
            
            // 执行1000次区域检测
            for (int i = 0; i < 1000; i++)
            {
                var position = new UnityEngine.Vector3(i % 100, i % 50, i % 200);
                var zone = ZoneDetector.GetZoneByPosition(position);
                var zoneName = ZoneDetector.GetZoneName(zone);
            }
            
            var elapsed = DateTime.Now - startTime;
            Logger.Info($"1000次区域检测耗时: {elapsed.TotalMilliseconds:F2}ms");
            
            if (elapsed.TotalMilliseconds > 1000) // 如果超过1秒认为性能有问题
            {
                Logger.Warn("区域检测性能可能需要优化");
            }
            
            Logger.Info("✅ 区域检测性能测试通过");
        }

        /// <summary>
        /// 测试显示管理器性能
        /// </summary>
        private static void TestDisplayManagerPerformance()
        {
            Logger.Info("测试显示管理器性能...");
            
            var startTime = DateTime.Now;
            
            // 执行1000次统计信息获取
            for (int i = 0; i < 1000; i++)
            {
                var stats = RightBottomDisplayManager.Instance.GetStatistics();
            }
            
            var elapsed = DateTime.Now - startTime;
            Logger.Info($"1000次统计信息获取耗时: {elapsed.TotalMilliseconds:F2}ms");
            
            if (elapsed.TotalMilliseconds > 500) // 如果超过0.5秒认为性能有问题
            {
                Logger.Warn("显示管理器性能可能需要优化");
            }
            
            Logger.Info("✅ 显示管理器性能测试通过");
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public static void CleanupTestData()
        {
            Logger.Info("清理显示系统测试数据...");
            
            try
            {
                // 清理所有在线玩家的显示数据
                foreach (var player in XHelper.PlayerList.Where(p => p != null))
                {
                    RightBottomDisplayManager.Instance.CleanupPlayerData(player);
                }
                
                Logger.Info("显示系统测试数据清理完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理测试数据失败: {ex.Message}");
            }
        }
    }
}
