-   V1.0.0  First Release
-   V1.0.1
-       Update the display based on hint's Content update
======================================================================================
-   V2.0.0  Dynamic Hint
-       Support Dynamic Hint
-       Limit maximum update rate to 0.5/second
-       Fixed some bugs     
-   V2.1.0
-       Add Common Hints
-   V2.1.1
-       Fix some bugs
-   V2.2.0
-       Use the event to update the ReferenceHub display, increase stability, and decrease costs
======================================================================================
-   V3.0.0  ReferenceHub UI
-       ReferenceHub UI is separated from PlayerDisplay and extended for more methods
-   V3.0.1
-       Fix some bugs
-   V3.0.2
-       Fix the bug that crush the PlayerDisplay when there's no hint displayed on the screen
-   V3.1.0
-       Add PlayerUIConfig Config
-       TODO: ADD configs for spectator template, scp extra info, spectator info, etc.
-   V3.1.1
-       bug fixing
-   V3.1.2
-       Use patch to block all the hints from other plugins
-   V3.2.0
-       Organized config
-       Make PlayerUIConfig more customizable
-   V3.3.0
-       Separate PlayerUITemplate from PlayerUIConfig
-       PlayerUITemplate is now a new plugin called CustomizableUIMeow
======================================================================================
-   V4.0.0  Customizable
-       Add config class for hints
-       Add refresh event in PlayerDisplay
-       Add hint Priority
-       Make common hint customizable
-       Improve code quality
======================================================================================
-   V5.0.0  Rework(Pre-release)
-       Rewrite core code
-       Add sync speed, auto text, and several new properties to hint
-       Standardized code style
-       Add NW API support
-       Remove hint config template
-       Separate PlayerUI and CommonHint
-   V5.0.0  Rework
-       Fix the bug that cause the font file to place in the TEMP folder
-       Fix the bug that NW API cannot load this plugin correctly
-   V5.0.1
-       Improve experience in font installation
-       Fix the bug in Dynamic Hint arranging
-   V5.0.2
-       Bug fixing
-   V5.1.0
-       Add support for \n in text
-       Improve DynamicHint's performance
-   V5.1.1
-       Fix the bug that cause text length to be calculated incorrectly
-   V5.1.2
-       Adjust sync speed to improve display performance
-       Add LineHeight property for all hints
-   V5.2.0
-       Add Compatibility Adapter
-       Improve performance
-   V5.2.1
-       Fix the bug that config might not be apply for compatibility adapter
-   V5.2.2
-       Bug fixing
-       Performance improvement
-       Improve code quality
-   V5.2.3
-       Improve compatibility adapter's accuracy. Fix the font size issue
-   V5.2.4
-       Add support for color, b, i tags in compatibility adapter.
-       Add more methods to player display
-   V5.2.5
-       Fix the problem that the compatibility adapter's cache might cause high memory usage
-   V5.3.0 Pre-release 1.0
-       Add multi-thread support for Core functions
-       Add pos tag support in compatibility adapter
-       Add Style component in PlayerUI
-   V5.3.0 Pre-release 1.1
-       Fix the bug that cause the color of Style component not working.
-       Fix the bug that cause Pos tag in compatibility adapter not working.
-       Support em unit in line-height of compatibility adapter
-   V5.3.0 Pre-release 1.2
-       Improve HintParser's behavior
-       Improve thread safety
-   V5.3.0 Pre-release 1.3
-       Fix the bug that cause the compatibility adapter's hints to glitter
-       Fix the bug that cause multi line hints not displayed correctly
-   V5.3.0 Pre-release 1.4
-       Fix the bug that cause dynamic hint to be displayed incorrectly
-       Fix the bug null reference problem in player display
-       Fix the bug that cause empty line handled incorrectly 
-   V5.3.0 Pre-release 2.0
-       Fix the bug that causes the server to crash when getting the player display
-       Improve the behavior of the compatibility adaptor
-       Support size tag in a hint
-   V5.3.0 Pre-release 2.1
-       Support for case style and script style tags
-       Fix the bug that causes the rich text parser to handle the alignment incorrectly
-       Fix the bug that causes rich text parser break line incorrectly
-       Add margin properties in the dynamic hint
-   V5.3.0 Pre-release 2.2
-       Fix the bug that causes the line height not to be usable
-       Minor updates and bug fixing
-   V5.3.0 Pre-release 2.3
-       Fix the problem that line height was not included when calculating the text height
-       Fix the problem that font tools does not calculate character length correctly
-       Fix the problem that rich text parser does not handle line break correctly
-   V5.3.0
-       Use 4.8 instead of 4.8.1 as default .net version
-       Fix the problem that ReceiveHint patch cause crush
-       Add string builder pool to improve performance
-       Improve NW API compatibility
-       Minor naming update
-   V5.3.1
-       Add RemoveAfter and HideAfter to PlayerDisplay and AbstractHint
-       Rewrite update management code in PlayerDisplay
-   V5.3.2
-       Fix the bug that cause CompatibilityAdapter to not work correctly
-       Fix the bug that cause update management to not work correctly
-   V5.3.3
-       Fix an issue in Timing.CallDelayed
-       Improve code quality
-   V5.3.4
-       Fix a bug in CompatibilityAdapter that appear when negative Duration is passed
-       Fix a thread safety issue in TaskScheduler
-       Fix a issue in FontTool
-   V5.3.5
-       Fix a bug that can cause update rate to be higher than expected
-       Improve stability of compatibility adaptor
-       Improve performance
-       Fix some bugs in extension
-       Fix some thread safety issues in Hint Collection
-       Add more customizable properties in PlayerDisplay
-       Minor change on code quality
-       Fix the bug that cause crush on Linux system
-   V5.3.6
-       Improve code quality
-       Add delay time in TextUpdateArg
-       Fix some issues
-       Fix a thread safety issue in PlayerDisplay
-   V5.3.7
-       Add error handling issue in PlayerDisplay.StartParserTask
-       Add support for <br> tag in text
-   V5.3.8
-      Use MainThreadDispatcher to replace MultiThreadTool
-      Fix a issue in StringBuilderPool that could cause memory leak
-   V5.3.9
-      Fix a issue in RichTextParser that caused <br> line break tag not working correclty
-      Add support for \n (pure text) to break the line
-	V5.3.10
-		Fix a bug that cause the hint to stuck on the screen
-		Standardize the code style
-		Re-implemented API for the plugin frameworks
-	    Minor adjustment to improve performance
-	V5.3.11
-		Fix a bug in PlayerDisplay that cause the removal using id not working correctly
-		Fixed a namespace error in HintContent. !!!This might break your plugin if you are using HintContents!!!
-	V5.3.12
-		Fix a bug that cause the RemoveAfter in PlayerDisplay not working correctly.
- 		Remove overflow detection since it is not working correctly. Please break the line manually if you need to.
-		Minor adjustments to prevent bugs.
-	V5.3.13
-		Bug Fixing
-   V5.3.14
-       Fix the bug that the AutoText will continue to call after PlayerDisplay is being destructed
-	V5.4.0 Beta 1
-		Support for LabAPI
-		Remove support for NWAPI
-		Remove hint update frequency limitation
-		Bug Fixing
-	V5.4.0 Beta 2
-		Fix a part of code that mistakenly used PluginAPI instead of LabAPI
-	V5.4.0
-		==============Breaking Change================
-		AutoText parameter is updated
-	V5.4.1
-		Centralized multithread actions to improve performance
-		Removed YamlDotNet for better compatibility
-	V5.4.2
-		Fix a bug that cause CA not to clear the hint correctly
-	V5.4.3
-		Fix a bug that cause CoordinateTool::GetTextWidth to throw exception when handling empty string
-	V5.4.4
-		Fix a bug that cause PlayerDisplay::CoroutineMethod not working correctly