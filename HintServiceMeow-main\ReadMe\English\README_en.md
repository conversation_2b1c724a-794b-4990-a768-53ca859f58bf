# Introduction
**HintServiceMeow (HSM)** is a framework that allows plugins to display text on a selected position on a player's screen. 

---

# Installation

To install this plugin, follow these steps:

1. Go to the [Release Page](https://github.com/MeowServer/HintServiceMeow/releases) and download the latest `HintServiceMeow.dll`. Then, paste it into your plugin folder.
2. If you are using **PluginAPI** (the default API), place `Harmony.dll` into the **dependencies** folder.
3. Restart your server.

---

# Documents

Here are some useful resources to get you started:

- [Features Introduction](Features.md)
- [Getting Started](GettingStarted.md)
- [Core Features](CoreFeatures.md)

---

# FAQ

### 1. Why doesn't the plugin work?
- Ensure that **HintServiceMeow** is installed correctly.
- Check if any other plugins conflict with **HintServiceMeow**.
- Review any errors that occur when activating plugins.

### 2. Why do hints overlap with each other?
- This might happen when multiple plugins place hints in the same position. You can adjust the UI position in each plugin's configuration file. 
- If a plugin doesn't allow you to change the position via its config file, please contact the plugin's author for assistance.
