using System;

namespace BlackRoseServer.API
{
    /// <summary>
    /// 玩家数据模型
    /// </summary>
    public class PlayerData
    {
        public string UserId { get; set; }
        public int DisplayUID { get; set; }
        public string NickName { get; set; }
        public int Level { get; set; } = 1;
        public int Experience { get; set; } = 1;
        public string IpAddress { get; set; } = "";
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 权限数据模型
    /// </summary>
    public class PermissionData
    {
        public int Id { get; set; }
        public string SteamId { get; set; }
        public string QQ { get; set; }
        public string PermissionGroup { get; set; }
        public string PlayerName { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// VIP进服信息模型
    /// </summary>
    public class JoinedInfo
    {
        public string UserId { get; set; }
        public string NickName { get; set; }
        public string Text { get; set; }
    }

    /// <summary>
    /// 徽章信息模型
    /// </summary>
    public class BadgeInfo
    {
        public string UserId { get; set; }
        public string Text { get; set; }
        public string Color { get; set; }
        public DateTime ExpireTime { get; set; }
    }

    /// <summary>
    /// 经验记录模型
    /// </summary>
    public class ExperienceRecord
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public int ExpValue { get; set; }
        public DateTime PlusTime { get; set; }
        public double Multiple { get; set; } = 1.0;
    }


}
