using System;
using System.Collections.Generic;
using LabApi.Loader.Features.Plugins;
using LabApi.Events.Handlers;
using LabApi.Features.Console;
using xiaozhoucust.Features;

namespace xiaozhoucust
{
    public class Plugin : LabApi.Loader.Features.Plugins.Plugin
    {
        public override string Name => "XiaoZhouCust";
        public override string Author => "XiaoZhou";
        public override string Description => "小周定制插件 - 无限子弹、阴间HintUI、SCP血量调整";
        public override Version Version { get; } = new Version(1, 0, 0);
        public override Version RequiredApiVersion { get; } = Version.Parse(LabApi.Features.LabApiProperties.CompiledVersion);

        public static Plugin Instance { get; private set; }
        public Config Config { get; private set; }

        private InfiniteAmmo _infiniteAmmo;
        private HintUIManager _hintUIManager;
        private ScpHealthManager _scpHealthManager;

        public override void Enable()
        {
            Instance = this;
            Config = new Config();

            Logger.Info("小周定制插件正在启用...");

            // 初始化功能模块
            _infiniteAmmo = new InfiniteAmmo();
            _hintUIManager = new HintUIManager();
            _scpHealthManager = new ScpHealthManager();

            // 注册事件
            RegisterEvents();

            Logger.Info("小周定制插件已成功启用！");
        }

        public override void Disable()
        {
            Logger.Info("小周定制插件正在禁用...");

            // 注销事件
            UnregisterEvents();

            // 清理资源
            _infiniteAmmo?.Dispose();
            _hintUIManager?.Dispose();
            _scpHealthManager?.Dispose();

            Instance = null;
            Logger.Info("小周定制插件已禁用！");
        }

        private void RegisterEvents()
        {
            // 回合事件
            ServerEvents.RoundStarted += OnRoundStarted;
            ServerEvents.RoundEnded += OnRoundEnded;

            // 玩家事件
            PlayerEvents.Joined += OnPlayerJoined;
            PlayerEvents.Spawning += OnPlayerSpawning;
            PlayerEvents.DroppingAmmo += OnPlayerDroppingAmmo;
        }

        private void UnregisterEvents()
        {
            // 回合事件
            ServerEvents.RoundStarted -= OnRoundStarted;
            ServerEvents.RoundEnded -= OnRoundEnded;

            // 玩家事件
            PlayerEvents.Joined -= OnPlayerJoined;
            PlayerEvents.Spawning -= OnPlayerSpawning;
            PlayerEvents.DroppingAmmo -= OnPlayerDroppingAmmo;
        }

        private void OnRoundStarted()
        {
            _infiniteAmmo?.OnRoundStarted();
            _hintUIManager?.OnRoundStarted();
        }

        private void OnRoundEnded(LabApi.Events.Arguments.ServerEvents.RoundEndedEventArgs ev)
        {
            _infiniteAmmo?.OnRoundEnded();
            _hintUIManager?.OnRoundEnded();
        }

        private void OnPlayerJoined(LabApi.Events.Arguments.PlayerEvents.PlayerJoinedEventArgs ev)
        {
            _hintUIManager?.OnPlayerJoined(ev.Player);
        }

        private void OnPlayerSpawning(LabApi.Events.Arguments.PlayerEvents.PlayerSpawningEventArgs ev)
        {
            _scpHealthManager?.OnPlayerSpawning(ev);
        }

        private void OnPlayerDroppingAmmo(LabApi.Events.Arguments.PlayerEvents.PlayerDroppingAmmoEventArgs ev)
        {
            _infiniteAmmo?.OnPlayerDroppingAmmo(ev);
        }
    }
}
