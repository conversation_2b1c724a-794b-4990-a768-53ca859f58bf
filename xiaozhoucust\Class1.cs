﻿using System;
using System.Collections.Generic;
using LabApi.Loader.Features.Plugins;
using LabApi.Events.Handlers;
using LabApi.Features.Console;
using xiaozhoucust.Features;

namespace xiaozhoucust
{
    /// <summary>
    /// 小周自定义SCPSL插件
    /// 功能包括：无限子弹、阴间HintUI、SCP血量调整、重生计算器、服务器信息显示
    /// </summary>
    public class Plugin : LabApi.Loader.Features.Plugins.Plugin<Config>
    {
        public override string Name => "XiaoZhouCustom";
        public override string Description => "XiaoZhouCustom插件";
        public override string Author => "kldhsh123";
        public override Version Version => new(1, 0, 0);
        public override Version RequiredApiVersion => new(1, 1, 0);

        private EventHandlers _eventHandlers;
        private InfiniteAmmo _infiniteAmmo;
        private RespawnTimer _respawnTimer;
        private ScpHealthAdjuster _scpHealthAdjuster;
        private ServerInfoDisplay _serverInfoDisplay;

        public override void Enable()
        {
            // 初始化事件处理器
            _eventHandlers = new EventHandlers(this);

            // 初始化功能模块
            _infiniteAmmo = new InfiniteAmmo(Config);
            _respawnTimer = new RespawnTimer(Config);
            _scpHealthAdjuster = new ScpHealthAdjuster(Config);
            _serverInfoDisplay = new ServerInfoDisplay(Config);

            // 注册事件
            RegisterEvents();

            Logger.Info($"{Name} v{Version} 加载成功");
        }

        public override void Disable()
        {
            // 注销事件
            UnregisterEvents();

            // 清理资源
            _infiniteAmmo?.Dispose();
            _respawnTimer?.Dispose();
            _scpHealthAdjuster?.Dispose();
            _serverInfoDisplay?.Dispose();
        }

        private void RegisterEvents()
        {
            // 服务器事件
            ServerEvents.RoundStarting += _eventHandlers.OnRoundStarting;
            ServerEvents.RoundEnded += _eventHandlers.OnRoundEnded;

            // 玩家事件
            PlayerEvents.Joined += _eventHandlers.OnPlayerJoined;
            PlayerEvents.Left += _eventHandlers.OnPlayerLeft;
            PlayerEvents.Spawning += _eventHandlers.OnPlayerSpawning;
            PlayerEvents.ChangingRole += _eventHandlers.OnPlayerChangingRole;
            PlayerEvents.DroppingAmmo += _eventHandlers.OnPlayerDroppingAmmo;
        }

        private void UnregisterEvents()
        {
            // 服务器事件
            ServerEvents.RoundStarting -= _eventHandlers.OnRoundStarting;
            ServerEvents.RoundEnded -= _eventHandlers.OnRoundEnded;

            // 玩家事件
            PlayerEvents.Joined -= _eventHandlers.OnPlayerJoined;
            PlayerEvents.Left -= _eventHandlers.OnPlayerLeft;
            PlayerEvents.Spawning -= _eventHandlers.OnPlayerSpawning;
            PlayerEvents.ChangingRole -= _eventHandlers.OnPlayerChangingRole;
            PlayerEvents.DroppingAmmo -= _eventHandlers.OnPlayerDroppingAmmo;
        }

        /// <summary>
        /// 获取无限子弹功能实例
        /// </summary>
        public InfiniteAmmo InfiniteAmmo => _infiniteAmmo;

        /// <summary>
        /// 获取重生计时器功能实例
        /// </summary>
        public RespawnTimer RespawnTimer => _respawnTimer;

        /// <summary>
        /// 获取SCP血量调整功能实例
        /// </summary>
        public ScpHealthAdjuster ScpHealthAdjuster => _scpHealthAdjuster;

        /// <summary>
        /// 获取服务器信息显示功能实例
        /// </summary>
        public ServerInfoDisplay ServerInfoDisplay => _serverInfoDisplay;
    }
}
