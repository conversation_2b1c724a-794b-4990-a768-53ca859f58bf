# 小周自定义SCPSL插件

一个功能丰富的SCP: Secret Laboratory LabAPI插件，提供多种游戏增强功能。

## 功能特性

### 🔫 无限子弹功能
- 自动为所有人类玩家补充弹药至最大值
- 可配置刷新间隔
- 自动阻止玩家丢弃弹药
- 支持所有弹药类型（9x19mm、12号霰弹、.44马格南、7.62x39mm、5.56x45mm）

### ⏰ 重生计时器
- 基于HintServiceMeow框架的美观计时器显示
- 显示NTF和混沌的重生倒计时
- 只对观察者玩家显示
- 可配置显示位置、字体大小和颜色
- 支持动态颜色变化（接近重生时高亮显示）

### 💪 SCP血量调整
- 在SCP生成时自动调整血量
- 支持SCP-173、SCP-106、SCP-049、SCP-049-2、SCP-096、SCP-939
- 可单独配置每个SCP的血量值
- 延迟设置确保角色完全生成

### 📱 服务器信息显示
- 在右下角显示服务器名称和QQ群号
- 基于HintServiceMeow框架的持久化显示
- 可配置显示内容、位置和样式
- 支持自定义颜色和字体大小

### 🎨 阴间HintUI系统
- 使用HintServiceMeow框架提供更好的UI体验
- 自动位置管理，避免重叠
- 支持动态更新和自动清理
- 统一的Hint管理工具类

## 安装方法

1. 确保服务器已安装LabAPI
2. 将`HintServiceMeow-LabAPI.dll`放入dependencies文件夹
3. 将编译好的`xiaozhoucust.dll`放入plugins文件夹
4. 重启服务器

## 配置说明

插件会在首次运行时生成配置文件，包含以下主要配置项：

### 无限子弹配置
```yaml
# 是否启用无限子弹功能
enable_infinite_ammo: true
# 无限子弹刷新间隔（秒）
infinite_ammo_refresh_interval: 3.0
# 各种弹药数量配置
ammo9x19_count: 180
ammo12gauge_count: 18
ammo44cal_count: 18
ammo762x39_count: 180
ammo556x45_count: 180
```

### 重生计时器配置
```yaml
# 是否启用重生计时器
enable_respawn_timer: true
# 计时器显示Y坐标
respawn_timer_y_coordinate: 105
# 计时器字体大小
respawn_timer_font_size: 35
# NTF重生颜色
ntf_spawn_color: "#4169E1"
# 混沌重生颜色
chaos_spawn_color: "#DC143C"
```

### SCP血量调整配置
```yaml
# 是否启用SCP血量调整
enable_scp_health_adjustment: true
# 各SCP血量配置
scp173_health: 5000.0
scp106_health: 2600.0
scp049_health: 3000.0
scp0492_health: 500.0
scp096_health: 3000.0
scp939_health: 2800.0
```

### 服务器信息显示配置
```yaml
# 是否启用服务器信息显示
enable_server_info_display: true
# 服务器名称
server_name: "小周的服务器"
# QQ群号
qq_group_number: "123456789"
# 显示位置和样式
server_info_y_coordinate: 950
server_info_font_size: 18
server_info_color: "#00BFFF"
```

## 依赖项

- LabAPI 1.0.0+
- HintServiceMeow-LabAPI.dll
- MEC (通常包含在LabAPI中)

## 技术特性

- 基于LabAPI插件框架
- 使用HintServiceMeow提供优秀的UI体验
- 模块化设计，各功能独立可配置
- 完善的错误处理和日志记录
- 支持热重载配置

## 兼容性

- 支持LabAPI 1.0.0及以上版本
- 兼容SCP: Secret Laboratory最新版本
- 与其他LabAPI插件兼容

## 更新日志

### v1.0.0
- 初始版本发布
- 实现无限子弹功能
- 实现重生计时器功能
- 实现SCP血量调整功能
- 实现服务器信息显示功能
- 集成HintServiceMeow框架

## 支持

如有问题或建议，请联系插件作者。

## 许可证

本插件仅供学习和个人使用。
