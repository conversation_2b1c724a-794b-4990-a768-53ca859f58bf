# 小周定制插件 (XiaoZhouCust)

一个为SCP: Secret Laboratory服务器定制的LabAPI插件，提供无限子弹、阴间HintUI和SCP血量调整功能。

## 功能特性

### 🔫 无限子弹功能
- **自动补充弹药**：每3秒自动为所有人类玩家补充弹药至满值
- **防止弹药丢弃**：阻止玩家丢弃弹药，确保无限弹药体验
- **可配置弹药数量**：支持自定义各种弹药类型的数量
- **性能优化**：使用协程避免阻塞主线程

### 🖥️ 阴间HintUI
- **重生计算器**：基于Timers项目美化，显示MTF和混沌的重生倒计时
- **群号显示**：在屏幕右下角(Y=900)显示QQ群号
- **服务器名称显示**：在群号下方显示服务器名称
- **使用HintServiceMeow框架**：提供流畅的UI显示体验
- **动态排版**：重生计算器使用动态提示，避免UI重叠

### 🩸 SCP血量调整
- **自定义SCP血量**：为每个SCP设置自定义血量值
- **生成时调整**：在SCP生成时自动调整血量
- **支持所有SCP**：包括SCP-049、SCP-096、SCP-173等

## 安装方法

1. 确保服务器已安装LabAPI
2. 下载并安装HintServiceMeow框架
3. 将编译好的`xiaozhoucust.dll`放入服务器的plugins文件夹
4. 重启服务器

## 配置文件

插件会自动生成配置文件，包含以下主要配置项：

```yaml
# 无限子弹配置
EnableInfiniteAmmo: true
InfiniteAmmoRefreshInterval: 3.0
PreventAmmoDrop: true

# HintUI配置
EnableHintUI: true
ShowRespawnTimer: true
ShowServerInfo: true
GroupNumber: "123456789"
ServerName: "小周定制服务器"
GroupNumberYPosition: 900.0

# SCP血量配置
EnableScpHealthAdjustment: true
ScpHealthSettings:
  Scp049: 1700.0
  Scp096: 2000.0
  Scp173: 3200.0
  # ... 其他SCP配置
```

## 依赖项

- **LabAPI** 1.1.0+
- **HintServiceMeow** (最新版本)
- **YamlDotNet** 11.0.1
- **.NET Framework** 4.8.1

## 技术特性

- **模块化设计**：每个功能独立实现，便于维护
- **异常处理**：完善的错误处理和日志记录
- **性能优化**：使用协程和事件驱动，避免性能问题
- **配置灵活**：所有功能都可通过配置文件控制

## 版本信息

- **版本**：1.0.0
- **作者**：XiaoZhou
- **兼容性**：LabAPI 1.1.0+

## 更新日志

### v1.0.0
- 初始版本发布
- 实现无限子弹功能
- 实现阴间HintUI系统
- 实现SCP血量调整功能

## 支持

如有问题或建议，请联系插件作者或在相关社区反馈。

---

*本插件基于LabAPI开发，使用HintServiceMeow框架提供UI功能。*
