using System;
using System.Text;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using Respawning;
using Respawning.Waves;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 重生计时器功能实现
    /// </summary>
    public class RespawnTimer : IDisposable
    {
        private readonly Config _config;
        public DynamicHint _respawnTimerDisplay;
        private bool _isRunning = false;

        // 重生波次引用
        internal NtfSpawnWave NtfWave;
        internal NtfMiniWave NtfMiniWave;
        internal ChaosSpawnWave ChaosWave;
        internal ChaosMiniWave ChaosMiniWave;

        public RespawnTimer(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 启动重生计时器
        /// </summary>
        public void StartTimer()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("重生计时器已在运行中");
                    return;
                }

                if (!_config.EnableRespawnTimer)
                {

                    return;
                }

                // 初始化DynamicHint
                _respawnTimerDisplay = new DynamicHint()
                {
                    AutoText = arg => GetTimerText(Player.Get(arg.PlayerDisplay.ReferenceHub)),
                    TargetY = _config.RespawnTimerYCoordinate,
                    FontSize = _config.RespawnTimerFontSize,
                    SyncSpeed = HintSyncSpeed.Fast
                };

                _isRunning = true;


                // 延迟初始化重生波次引用
                Timing.CallDelayed(2f, () =>
                {
                    try
                    {
                        InitializeWaveReferences();
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"初始化重生波次引用失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"启动重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止重生计时器
        /// </summary>
        public void StopTimer()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                _isRunning = false;
            }
            catch (Exception ex)
            {
                Logger.Error($"停止重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 获取计时器文本
        /// </summary>
        private string GetTimerText(Player player)
        {
            try
            {
                // 只为观察者显示计时器
                if (player == null || !player.IsReady ||
                    (player.Role != PlayerRoles.RoleTypeId.Spectator && player.Role != PlayerRoles.RoleTypeId.Overwatch))
                {
                    return string.Empty;
                }

                var ntfTime = GetNtfRespawnTime() + TimeSpan.FromSeconds(18);
                if (ntfTime < TimeSpan.Zero) ntfTime = TimeSpan.Zero;

                var chaosTime = GetChaosRespawnTime() + TimeSpan.FromSeconds(13);
                if (chaosTime < TimeSpan.Zero) chaosTime = TimeSpan.Zero;

                var builder = new StringBuilder();
                builder.Append("<align=right>");

                // NTF计时器标题和时间
                builder.Append("<color=#4A90E2><b>九尾狐</b></color> ");
                if (ntfTime.TotalSeconds <= 18 && ntfTime.TotalSeconds > 0)
                {
                    builder.Append($"<color=#FF6B6B><b>{FormatTime(ntfTime)}</b></color>");
                }
                else
                {
                    builder.Append($"<color=#87CEEB><b>{FormatTime(ntfTime)}</b></color>");
                }

                // 换行
                builder.Append("\n");

                // 混沌计时器标题和时间
                builder.Append("<color=#32CD32><b>混沌分裂者</b></color> ");
                if (chaosTime.TotalSeconds <= 13 && chaosTime.TotalSeconds > 0)
                {
                    builder.Append($"<color=#FF6B6B><b>{FormatTime(chaosTime)}</b></color>");
                }
                else
                {
                    builder.Append($"<color=#90EE90><b>{FormatTime(chaosTime)}</b></color>");
                }

                builder.Append("</align>");
                return builder.ToString();
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取计时器文本失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        private string FormatTime(TimeSpan time)
        {
            return $"{time.Minutes:D1}:{time.Seconds:D2}";
        }

        /// <summary>
        /// 获取NTF重生时间
        /// </summary>
        private TimeSpan GetNtfRespawnTime()
        {
            try
            {
                if (NtfMiniWave != null && !NtfMiniWave.Timer.IsPaused)
                    return TimeSpan.FromSeconds(NtfMiniWave.Timer.TimeLeft);

                return NtfWave != null ? TimeSpan.FromSeconds(NtfWave.Timer.TimeLeft) : TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取NTF重生时间失败: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        /// <summary>
        /// 获取混沌重生时间
        /// </summary>
        private TimeSpan GetChaosRespawnTime()
        {
            try
            {
                if (ChaosMiniWave != null && !ChaosMiniWave.Timer.IsPaused)
                    return TimeSpan.FromSeconds(ChaosMiniWave.Timer.TimeLeft);

                return ChaosWave != null ? TimeSpan.FromSeconds(ChaosWave.Timer.TimeLeft) : TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取混沌重生时间失败: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        /// <summary>
        /// 初始化重生波次引用
        /// </summary>
        private void InitializeWaveReferences()
        {
            try
            {
                if (WaveManager.TryGet(out NtfSpawnWave ntfWave))
                {

                    NtfWave = ntfWave;
                }

                if (WaveManager.TryGet(out NtfMiniWave ntfMiniWave))
                {

                    NtfMiniWave = ntfMiniWave;
                }

                if (WaveManager.TryGet(out ChaosSpawnWave chaosWave))
                {

                    ChaosWave = chaosWave;
                }

                if (WaveManager.TryGet(out ChaosMiniWave chaosMiniWave))
                {
                    Logger.Debug("ChaosMiniWave found", _config.Debug);
                    ChaosMiniWave = chaosMiniWave;
                }


            }
            catch (Exception ex)
            {
                Logger.Error($"初始化重生波次引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopTimer();
            }
            catch (Exception ex)
            {
                Logger.Error($"释放重生计时器资源失败: {ex.Message}");
            }
        }
    }
}