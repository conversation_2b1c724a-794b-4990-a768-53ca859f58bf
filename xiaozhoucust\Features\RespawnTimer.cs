using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.Core.Extension;
using PlayerRoles;
using Respawning;
using Respawning.Waves;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 重生计时器功能实现
    /// 基于Timers-master项目的HintManager实现
    /// </summary>
    public class RespawnTimer : IDisposable
    {
        private readonly Config _config;
        private readonly Dictionary<Player, DynamicHint> _playerTimers;
        private CoroutineHandle _timerUpdateCoroutine;
        private bool _isRunning = false;

        // 重生波次引用
        private NtfSpawnWave _ntfWave;
        private NtfMiniWave _ntfMiniWave;
        private ChaosSpawnWave _chaosWave;
        private ChaosMiniWave _chaosMiniWave;

        public RespawnTimer(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _playerTimers = new Dictionary<Player, DynamicHint>();
        }

        /// <summary>
        /// 启动重生计时器
        /// </summary>
        public void StartTimer()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("重生计时器已在运行中");
                    return;
                }

                if (!_config.EnableRespawnTimer)
                {
                    Logger.Info("重生计时器功能已在配置中禁用");
                    return;
                }

                // 初始化重生波次引用
                InitializeWaveReferences();

                // 为所有观察者玩家创建计时器显示
                var spectatorPlayers = Player.GetPlayers().Where(p => p != null && 
                    (p.Role == RoleTypeId.Spectator || p.Role == RoleTypeId.Overwatch)).ToList();

                foreach (var player in spectatorPlayers)
                {
                    AddPlayer(player);
                }

                // 启动计时器更新协程
                _timerUpdateCoroutine = Timing.RunCoroutine(TimerUpdateCoroutine());
                _isRunning = true;

                Logger.Info("重生计时器已启动");
            }
            catch (Exception ex)
            {
                Logger.Error($"启动重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止重生计时器
        /// </summary>
        public void StopTimer()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                // 停止协程
                if (_timerUpdateCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_timerUpdateCoroutine);
                }

                // 清理所有玩家的计时器显示
                foreach (var kvp in _playerTimers.ToList())
                {
                    RemovePlayer(kvp.Key);
                }

                _isRunning = false;
                Logger.Info("重生计时器已停止");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 为玩家添加计时器显示
        /// </summary>
        public void AddPlayer(Player player)
        {
            try
            {
                if (player == null || _playerTimers.ContainsKey(player))
                    return;

                // 只为观察者显示计时器
                if (player.Role != RoleTypeId.Spectator && player.Role != RoleTypeId.Overwatch)
                    return;

                var timerHint = new DynamicHint
                {
                    AutoText = _ => GetTimerText(player),
                    TargetY = _config.RespawnTimerYCoordinate,
                    FontSize = _config.RespawnTimerFontSize,
                    SyncSpeed = ParseHintSyncSpeed(_config.HintSyncSpeed)
                };

                player.AddHint(timerHint);
                _playerTimers[player] = timerHint;

                if (_config.Debug)
                {
                    Logger.Debug($"为玩家 {player.Nickname} 添加重生计时器显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家添加计时器显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 移除玩家的计时器显示
        /// </summary>
        public void RemovePlayer(Player player)
        {
            try
            {
                if (player == null || !_playerTimers.ContainsKey(player))
                    return;

                var hint = _playerTimers[player];
                player.RemoveHint(hint);
                _playerTimers.Remove(player);

                if (_config.Debug)
                {
                    Logger.Debug($"移除玩家 {player.Nickname} 的重生计时器显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除玩家计时器显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 更新玩家角色时的处理
        /// </summary>
        public void UpdatePlayerRole(Player player, RoleTypeId newRole)
        {
            try
            {
                if (player == null) return;

                // 如果玩家变成观察者，添加计时器显示
                if ((newRole == RoleTypeId.Spectator || newRole == RoleTypeId.Overwatch) && 
                    !_playerTimers.ContainsKey(player))
                {
                    AddPlayer(player);
                }
                // 如果玩家不再是观察者，移除计时器显示
                else if (newRole != RoleTypeId.Spectator && newRole != RoleTypeId.Overwatch && 
                         _playerTimers.ContainsKey(player))
                {
                    RemovePlayer(player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"更新玩家角色时处理计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 计时器更新协程
        /// </summary>
        private IEnumerator<float> TimerUpdateCoroutine()
        {
            while (true)
            {
                try
                {
                    if (Round.IsRoundEnded)
                    {
                        yield break;
                    }

                    // 更新重生波次引用
                    UpdateWaveReferences();

                    // 等待1秒后继续更新
                    yield return Timing.WaitForSeconds(1f);
                }
                catch (Exception ex)
                {
                    Logger.Error($"计时器更新协程执行失败: {ex.Message}");
                    yield return Timing.WaitForSeconds(5f);
                }
            }
        }

        /// <summary>
        /// 获取计时器文本
        /// </summary>
        private string GetTimerText(Player player)
        {
            try
            {
                if (player == null || !player.IsReady)
                    return string.Empty;

                var ntfTime = GetNtfRespawnTime() + TimeSpan.FromSeconds(18);
                if (ntfTime < TimeSpan.Zero) ntfTime = TimeSpan.Zero;

                var chaosTime = GetChaosRespawnTime() + TimeSpan.FromSeconds(13);
                if (chaosTime < TimeSpan.Zero) chaosTime = TimeSpan.Zero;

                var builder = new StringBuilder();
                builder.Append("<align=center>");

                // NTF计时器
                if (WaveManager._nextWave != null && 
                    WaveManager._nextWave.TargetFaction == Faction.FoundationStaff && 
                    ntfTime.TotalSeconds <= 18)
                {
                    builder.Append($"<color={_config.NtfSpawnColor}>");
                    builder.Append(FormatTime(ntfTime));
                    builder.Append("</color>");
                }
                else
                {
                    builder.Append(FormatTime(ntfTime));
                }

                builder.Append($"<space={_config.TimerSpacing}ems>");

                // 混沌计时器
                if (WaveManager._nextWave != null && 
                    WaveManager._nextWave.TargetFaction == Faction.FoundationEnemy && 
                    chaosTime.TotalSeconds <= 13)
                {
                    builder.Append($"<color={_config.ChaosSpawnColor}>");
                    builder.Append(FormatTime(chaosTime));
                    builder.Append("</color>");
                }
                else
                {
                    builder.Append(FormatTime(chaosTime));
                }

                builder.Append("</align>");
                return builder.ToString();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取计时器文本失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        private string FormatTime(TimeSpan time)
        {
            return $"{time.Minutes:D1}:{time.Seconds:D2}";
        }

        /// <summary>
        /// 获取NTF重生时间
        /// </summary>
        private TimeSpan GetNtfRespawnTime()
        {
            if (_ntfMiniWave != null && !_ntfMiniWave.Timer.IsPaused)
                return TimeSpan.FromSeconds(_ntfMiniWave.Timer.TimeLeft);

            return _ntfWave != null ? TimeSpan.FromSeconds(_ntfWave.Timer.TimeLeft) : TimeSpan.Zero;
        }

        /// <summary>
        /// 获取混沌重生时间
        /// </summary>
        private TimeSpan GetChaosRespawnTime()
        {
            if (_chaosMiniWave != null && !_chaosMiniWave.Timer.IsPaused)
                return TimeSpan.FromSeconds(_chaosMiniWave.Timer.TimeLeft);

            return _chaosWave != null ? TimeSpan.FromSeconds(_chaosWave.Timer.TimeLeft) : TimeSpan.Zero;
        }

        /// <summary>
        /// 初始化重生波次引用
        /// </summary>
        private void InitializeWaveReferences()
        {
            try
            {
                // 获取当前的重生波次引用
                // 注意：这里的实现可能需要根据LabAPI的具体版本进行调整
                UpdateWaveReferences();
            }
            catch (Exception ex)
            {
                Logger.Error($"初始化重生波次引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新重生波次引用
        /// </summary>
        private void UpdateWaveReferences()
        {
            try
            {
                // 由于LabAPI的重生系统可能与Exiled不同，这里提供一个基础实现
                // 实际使用时可能需要根据LabAPI的具体实现进行调整

                // 尝试获取当前的重生波次
                // _ntfWave = WaveManager.GetCurrentNtfWave();
                // _chaosWave = WaveManager.GetCurrentChaosWave();

                if (_config.Debug)
                {
                    Logger.Debug("重生波次引用已更新");
                }
            }
            catch (Exception ex)
            {
                Logger.Debug($"更新重生波次引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析Hint同步速度
        /// </summary>
        private HintSyncSpeed ParseHintSyncSpeed(string speed)
        {
            return speed?.ToLower() switch
            {
                "slow" => HintSyncSpeed.Slow,
                "normal" => HintSyncSpeed.Normal,
                "fast" => HintSyncSpeed.Fast,
                _ => HintSyncSpeed.Fast
            };
        }

        /// <summary>
        /// 检查功能是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopTimer();
        }
    }
}
