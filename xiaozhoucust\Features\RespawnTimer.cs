using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.Core.Extension;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 重生计时器功能实现
    /// 基于Timers-master项目的HintManager实现
    /// </summary>
    public class RespawnTimer : IDisposable
    {
        private readonly Config _config;
        private readonly Dictionary<Player, DynamicHint> _playerTimers;
        private CoroutineHandle _timerUpdateCoroutine;
        private bool _isRunning = false;

        // 重生波次引用
        private MtfWave _ntfWave;
        private MiniMtfWave _ntfMiniWave;
        private ChaosWave _chaosWave;
        private MiniChaosWave _chaosMiniWave;

        public RespawnTimer(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _playerTimers = new Dictionary<Player, DynamicHint>();
        }

        /// <summary>
        /// 启动重生计时器
        /// </summary>
        public void StartTimer()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("重生计时器已在运行中");
                    return;
                }

                if (!_config.EnableRespawnTimer)
                {
                    Logger.Info("重生计时器功能已在配置中禁用");
                    return;
                }

                _isRunning = true;
                Logger.Info("重生计时器已启动（简化模式）");

                // 延迟启动，避免阻塞
                Timing.CallDelayed(3f, () =>
                {
                    try
                    {
                        if (_isRunning && !Round.IsRoundEnded)
                        {
                            // 初始化重生波次引用
                            InitializeWaveReferences();

                            // 为所有观察者玩家创建计时器显示
                            var spectatorPlayers = Player.List?.Where(p => p != null &&
                                (p.Role == PlayerRoles.RoleTypeId.Spectator || p.Role == PlayerRoles.RoleTypeId.Overwatch))?.ToList();

                            if (spectatorPlayers != null)
                            {
                                foreach (var player in spectatorPlayers)
                                {
                                    if (player != null)
                                    {
                                        AddPlayer(player);
                                    }
                                }
                            }

                            // 启动计时器更新协程
                            _timerUpdateCoroutine = Timing.RunCoroutine(TimerUpdateCoroutine());
                            Logger.Debug("重生计时器协程已启动");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"延迟启动重生计时器失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"启动重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止重生计时器
        /// </summary>
        public void StopTimer()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                // 停止协程
                if (_timerUpdateCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_timerUpdateCoroutine);
                }

                // 清理所有玩家的计时器显示
                foreach (var kvp in _playerTimers.ToList())
                {
                    RemovePlayer(kvp.Key);
                }

                _isRunning = false;
                Logger.Info("重生计时器已停止");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止重生计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 为玩家添加计时器显示
        /// </summary>
        public void AddPlayer(Player player)
        {
            try
            {
                if (player == null || _playerTimers.ContainsKey(player))
                    return;

                // 只为观察者显示计时器
                if (player.Role != PlayerRoles.RoleTypeId.Spectator && player.Role != PlayerRoles.RoleTypeId.Overwatch)
                    return;

                var timerHint = new DynamicHint
                {
                    AutoText = _ => GetTimerText(player),
                    TargetY = _config.RespawnTimerYCoordinate,
                    FontSize = _config.RespawnTimerFontSize,
                    SyncSpeed = ParseHintSyncSpeed(_config.HintSyncSpeed)
                };

                player.AddHint(timerHint);
                _playerTimers[player] = timerHint;

                if (_config.Debug)
                {
                    Logger.Debug($"为玩家 {player.Nickname} 添加重生计时器显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家添加计时器显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 移除玩家的计时器显示
        /// </summary>
        public void RemovePlayer(Player player)
        {
            try
            {
                if (player == null || !_playerTimers.ContainsKey(player))
                    return;

                var hint = _playerTimers[player];
                player.RemoveHint(hint);
                _playerTimers.Remove(player);

                if (_config.Debug)
                {
                    Logger.Debug($"移除玩家 {player.Nickname} 的重生计时器显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除玩家计时器显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 更新玩家角色时的处理
        /// </summary>
        public void UpdatePlayerRole(Player player, PlayerRoles.RoleTypeId newRole)
        {
            try
            {
                if (player == null) return;

                // 如果玩家变成观察者，添加计时器显示
                if ((newRole == PlayerRoles.RoleTypeId.Spectator || newRole == PlayerRoles.RoleTypeId.Overwatch) &&
                    !_playerTimers.ContainsKey(player))
                {
                    AddPlayer(player);
                }
                // 如果玩家不再是观察者，移除计时器显示
                else if (newRole != PlayerRoles.RoleTypeId.Spectator && newRole != PlayerRoles.RoleTypeId.Overwatch &&
                         _playerTimers.ContainsKey(player))
                {
                    RemovePlayer(player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"更新玩家角色时处理计时器失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 计时器更新协程
        /// </summary>
        private IEnumerator<float> TimerUpdateCoroutine()
        {
            while (true)
            {
                // 等待1秒
                yield return Timing.WaitForSeconds(1f);

                if (Round.IsRoundEnded)
                {
                    yield break;
                }

                try
                {
                    // 更新重生波次引用
                    UpdateWaveReferences();
                }
                catch (Exception ex)
                {
                    Logger.Error($"计时器更新协程执行失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取计时器文本
        /// </summary>
        private string GetTimerText(Player player)
        {
            try
            {
                if (player == null || !player.IsReady)
                    return string.Empty;

                var ntfTime = GetNtfRespawnTime() + TimeSpan.FromSeconds(18);
                if (ntfTime < TimeSpan.Zero) ntfTime = TimeSpan.Zero;

                var chaosTime = GetChaosRespawnTime() + TimeSpan.FromSeconds(13);
                if (chaosTime < TimeSpan.Zero) chaosTime = TimeSpan.Zero;

                var builder = new StringBuilder();
                builder.Append("<align=center>");

                // NTF计时器
                if (ntfTime.TotalSeconds <= 18 && ntfTime.TotalSeconds > 0)
                {
                    builder.Append($"<color={_config.NtfSpawnColor}>");
                    builder.Append(FormatTime(ntfTime));
                    builder.Append("</color>");
                }
                else
                {
                    builder.Append(FormatTime(ntfTime));
                }

                builder.Append($"<space={_config.TimerSpacing}ems>");

                // 混沌计时器
                if (chaosTime.TotalSeconds <= 13 && chaosTime.TotalSeconds > 0)
                {
                    builder.Append($"<color={_config.ChaosSpawnColor}>");
                    builder.Append(FormatTime(chaosTime));
                    builder.Append("</color>");
                }
                else
                {
                    builder.Append(FormatTime(chaosTime));
                }

                builder.Append("</align>");
                return builder.ToString();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取计时器文本失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        private string FormatTime(TimeSpan time)
        {
            return $"{time.Minutes:D1}:{time.Seconds:D2}";
        }

        /// <summary>
        /// 获取NTF重生时间
        /// </summary>
        private TimeSpan GetNtfRespawnTime()
        {
            try
            {
                if (_ntfMiniWave != null && _ntfMiniWave.TimeLeft > 0)
                    return TimeSpan.FromSeconds(_ntfMiniWave.TimeLeft);

                return _ntfWave != null ? TimeSpan.FromSeconds(_ntfWave.TimeLeft) : TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取NTF重生时间失败: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        /// <summary>
        /// 获取混沌重生时间
        /// </summary>
        private TimeSpan GetChaosRespawnTime()
        {
            try
            {
                if (_chaosMiniWave != null && _chaosMiniWave.TimeLeft > 0)
                    return TimeSpan.FromSeconds(_chaosMiniWave.TimeLeft);

                return _chaosWave != null ? TimeSpan.FromSeconds(_chaosWave.TimeLeft) : TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取混沌重生时间失败: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        /// <summary>
        /// 初始化重生波次引用
        /// </summary>
        private void InitializeWaveReferences()
        {
            try
            {
                // 暂时禁用重生波次引用，避免可能的卡死问题
                // TODO: 需要进一步调试RespawnWaves的访问问题
                Logger.Info("重生波次引用初始化已跳过（调试模式）");

                /*
                // 使用LabAPI的RespawnWaves获取重生波次引用
                _ntfWave = RespawnWaves.PrimaryMtfWave as MtfWave;
                _ntfMiniWave = RespawnWaves.MiniMtfWave as MiniMtfWave;
                _chaosWave = RespawnWaves.PrimaryChaosWave as ChaosWave;
                _chaosMiniWave = RespawnWaves.MiniChaosWave as MiniChaosWave;

                if (_config.Debug)
                {
                    Logger.Debug("重生波次引用初始化完成");
                }
                */
            }
            catch (Exception ex)
            {
                Logger.Error($"初始化重生波次引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新重生波次引用
        /// </summary>
        private void UpdateWaveReferences()
        {
            try
            {
                // LabAPI的重生波次是静态的，不需要频繁更新
                // 只在初始化时设置一次即可
                if (_config.Debug)
                {
                    Logger.Debug("重生波次引用已更新");
                }
            }
            catch (Exception ex)
            {
                Logger.Debug($"更新重生波次引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析Hint同步速度
        /// </summary>
        private HintSyncSpeed ParseHintSyncSpeed(string speed)
        {
            return speed?.ToLower() switch
            {
                "slow" => HintSyncSpeed.Slow,
                "normal" => HintSyncSpeed.Normal,
                "fast" => HintSyncSpeed.Fast,
                _ => HintSyncSpeed.Fast
            };
        }

        /// <summary>
        /// 检查功能是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopTimer();
        }
    }
}
