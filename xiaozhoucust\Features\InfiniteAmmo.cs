using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using InventorySystem.Items;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 无限子弹功能实现
    /// 基于Mahui_Source项目的XHelper.InAmmo()方法
    /// </summary>
    public class InfiniteAmmo : IDisposable
    {
        private readonly Config _config;
        private CoroutineHandle _infiniteAmmoCoroutine;
        private bool _isRunning = false;

        public InfiniteAmmo(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 启动无限子弹功能
        /// </summary>
        public void StartInfiniteAmmo()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("无限子弹功能已在运行中");
                    return;
                }

                if (!_config.EnableInfiniteAmmo)
                {
                    Logger.Info("无限子弹功能已在配置中禁用");
                    return;
                }

                _infiniteAmmoCoroutine = Timing.RunCoroutine(InfiniteAmmoCoroutine());
                _isRunning = true;

                Logger.Info($"无限子弹功能已启动，刷新间隔: {_config.InfiniteAmmoRefreshInterval}秒");
            }
            catch (Exception ex)
            {
                Logger.Error($"启动无限子弹功能失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止无限子弹功能
        /// </summary>
        public void StopInfiniteAmmo()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                if (_infiniteAmmoCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_infiniteAmmoCoroutine);
                }

                _isRunning = false;
                Logger.Info("无限子弹功能已停止");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止无限子弹功能失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 无限子弹协程
        /// 基于Mahui_Source项目的实现
        /// </summary>
        private IEnumerator<float> InfiniteAmmoCoroutine()
        {
            while (true)
            {
                try
                {
                    // 检查回合是否结束
                    if (Round.IsRoundEnded)
                    {
                        Logger.Debug("回合已结束，停止无限子弹协程");
                        yield break;
                    }

                    // 获取所有人类玩家
                    var humanPlayers = Player.GetPlayers().Where(p => p != null && p.IsHuman && p.IsAlive).ToList();

                    if (_config.Debug)
                    {
                        Logger.Debug($"为 {humanPlayers.Count} 个人类玩家补充弹药");
                    }

                    // 为每个人类玩家补充弹药
                    foreach (var player in humanPlayers)
                    {
                        try
                        {
                            SetPlayerAmmo(player);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"为玩家 {player.Nickname} 补充弹药失败: {ex.Message}");
                        }
                    }

                    // 等待指定时间后继续下一次循环
                    yield return Timing.WaitForSeconds(_config.InfiniteAmmoRefreshInterval);
                }
                catch (Exception ex)
                {
                    Logger.Error($"无限子弹协程执行失败: {ex.Message}");
                    if (_config.Debug)
                    {
                        Logger.Debug($"详细错误信息: {ex}");
                    }
                    
                    // 出现异常时等待一段时间再继续
                    yield return Timing.WaitForSeconds(5f);
                }
            }
        }

        /// <summary>
        /// 为玩家设置弹药数量
        /// </summary>
        private void SetPlayerAmmo(Player player)
        {
            if (player == null || !player.IsAlive || !player.IsHuman)
                return;

            try
            {
                // 设置各种弹药类型的数量
                player.SetAmmo(ItemType.Ammo9x19, (ushort)_config.Ammo9x19Count);      // 9x19mm帕拉贝鲁姆弹
                player.SetAmmo(ItemType.Ammo12gauge, (ushort)_config.Ammo12gaugeCount); // 12号霰弹
                player.SetAmmo(ItemType.Ammo44cal, (ushort)_config.Ammo44calCount);     // .44马格南弹
                player.SetAmmo(ItemType.Ammo762x39, (ushort)_config.Ammo762x39Count);   // 7.62×39mm弹
                player.SetAmmo(ItemType.Ammo556x45, (ushort)_config.Ammo556x45Count);   // 5.56×45mm弹

                if (_config.Debug)
                {
                    Logger.Debug($"已为玩家 {player.Nickname} 补充弹药");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家 {player.Nickname} 弹药失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查功能是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopInfiniteAmmo();
        }
    }
}
