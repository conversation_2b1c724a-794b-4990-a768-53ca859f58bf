using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 无限子弹功能实现
    /// 基于Mahui_Source项目的XHelper.InAmmo()方法
    /// </summary>
    public class InfiniteAmmo : IDisposable
    {
        private readonly Config _config;
        private CoroutineHandle _infiniteAmmoCoroutine;
        private bool _isRunning = false;

        public InfiniteAmmo(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 启动无限子弹功能
        /// </summary>
        public void StartInfiniteAmmo()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("无限子弹功能已在运行中");
                    return;
                }

                if (!_config.EnableInfiniteAmmo)
                {
                    Logger.Info("无限子弹功能已在配置中禁用");
                    return;
                }

                _isRunning = true;


                // 延迟2秒后启动协程，避免启动时的阻塞
                Timing.CallDelayed(2f, () =>
                {
                    try
                    {
                        if (_isRunning && !Round.IsRoundEnded)
                        {
                            _infiniteAmmoCoroutine = Timing.RunCoroutine(InfiniteAmmoCoroutine());
            
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"延迟启动无限子弹协程失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"启动无限子弹功能失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止无限子弹功能
        /// </summary>
        public void StopInfiniteAmmo()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                if (_infiniteAmmoCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_infiniteAmmoCoroutine);
                }

                _isRunning = false;
            }
            catch (Exception ex)
            {
                Logger.Error($"停止无限子弹功能失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 无限子弹协程
        /// 基于Mahui_Source项目的实现
        /// </summary>
        private IEnumerator<float> InfiniteAmmoCoroutine()
        {
            while (true)
            {
                // 等待3秒
                yield return Timing.WaitForSeconds(3.0f);

                // 检查回合是否结束
                if (Round.IsRoundEnded)
                {

                    yield break;
                }

                try
                {
                    // 获取所有人类玩家
                    var humanPlayers = Player.List?.Where(p => p != null && p.IsHuman && p.IsAlive)?.ToList();

                    if (humanPlayers == null || humanPlayers.Count == 0)
                    {
                        continue;
                    }



                    // 为每个人类玩家补充弹药
                    foreach (var player in humanPlayers)
                    {
                        try
                        {
                            if (player != null && player.IsAlive && player.IsHuman)
                            {
                                SetPlayerAmmo(player);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"为玩家 {player?.Nickname ?? "Unknown"} 补充弹药失败: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"无限子弹协程执行失败: {ex.Message}");
                    if (_config.Debug)
                    {
                        Logger.Debug($"详细错误信息: {ex}");
                    }
                }
            }
        }

        /// <summary>
        /// 为玩家设置弹药数量
        /// </summary>
        private void SetPlayerAmmo(Player player)
        {
            if (player == null || !player.IsAlive || !player.IsHuman)
                return;

            try
            {
                // 设置各种弹药类型，写死数值
                player.SetAmmo(ItemType.Ammo9x19, 180);   // 9x19mm帕拉贝鲁姆弹
                player.SetAmmo(ItemType.Ammo12gauge, 18);  // 12号霰弹
                player.SetAmmo(ItemType.Ammo44cal, 18);    // .44马格南弹
                player.SetAmmo(ItemType.Ammo762x39, 180);  // 7.62×39mm弹
                player.SetAmmo(ItemType.Ammo556x45, 180);  // 5.56×45mm弹

                if (_config.Debug)
                {
                    Logger.Debug($"已为玩家 {player.Nickname} 补充弹药");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"设置玩家 {player.Nickname} 弹药失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查功能是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopInfiniteAmmo();
        }
    }
}
