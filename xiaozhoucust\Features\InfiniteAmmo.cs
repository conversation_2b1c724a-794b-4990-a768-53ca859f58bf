using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using LabApi.Events.Arguments.PlayerEvents;

namespace xiaozhoucust.Features
{
    public class InfiniteAmmo : IDisposable
    {
        private CoroutineHandle _ammoCoroutine;
        private bool _isRoundActive = false;

        public void OnRoundStarted()
        {
            if (!Plugin.Instance.Config.EnableInfiniteAmmo)
            {
                Logger.Info("无限子弹功能已禁用");
                return;
            }

            _isRoundActive = true;
            _ammoCoroutine = Timing.RunCoroutine(InfiniteAmmoCoroutine());
            Logger.Info("无限子弹功能已启用");
        }

        public void OnRoundEnded()
        {
            _isRoundActive = false;
            if (_ammoCoroutine.IsValid)
            {
                Timing.KillCoroutines(_ammoCoroutine);
            }
            Logger.Debug("无限子弹协程已停止", Plugin.Instance.Config.EnableDebugLog);
        }

        public void OnPlayerDroppingAmmo(PlayerDroppingAmmoEventArgs ev)
        {
            if (!Plugin.Instance.Config.PreventAmmoDrop)
                return;

            ev.IsAllowed = false;
            Logger.Debug($"阻止玩家 {ev.Player.Nickname} 丢弃弹药", Plugin.Instance.Config.EnableDebugLog);
        }

        private IEnumerator<float> InfiniteAmmoCoroutine()
        {
            while (_isRoundActive)
            {
                try
                {
                    foreach (var player in Player.List.Where(p => p.IsHuman && p.IsAlive))
                    {
                        SetPlayerAmmo(player);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"无限子弹协程执行出错: {ex.Message}");
                }

                yield return Timing.WaitForSeconds(Plugin.Instance.Config.InfiniteAmmoRefreshInterval);
            }
        }

        private void SetPlayerAmmo(Player player)
        {
            try
            {
                var config = Plugin.Instance.Config.AmmoSettings;
                
                player.SetAmmo(ItemType.Ammo9x19, config.Ammo9x19);
                player.SetAmmo(ItemType.Ammo12gauge, config.Ammo12gauge);
                player.SetAmmo(ItemType.Ammo44cal, config.Ammo44cal);
                player.SetAmmo(ItemType.Ammo762x39, config.Ammo762x39);
                player.SetAmmo(ItemType.Ammo556x45, config.Ammo556x45);

                Logger.Debug($"为玩家 {player.Nickname} 补充弹药", Plugin.Instance.Config.EnableDebugLog);
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家 {player.Nickname} 设置弹药时出错: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _isRoundActive = false;
            if (_ammoCoroutine.IsValid)
            {
                Timing.KillCoroutines(_ammoCoroutine);
            }
        }
    }
}
