@echo off
echo 正在编译小周自定义SCPSL插件...
echo.

REM 检查是否存在MSBuild
where msbuild >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到MSBuild，请确保已安装Visual Studio或.NET SDK
    pause
    exit /b 1
)

REM 清理之前的编译结果
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM 编译项目
echo 开始编译...
msbuild xiaozhoucust.csproj /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %errorlevel% equ 0 (
    echo.
    echo ✓ 编译成功！
    echo.
    echo 编译输出位置: bin\Release\xiaozhoucust.dll
    echo.
    echo 安装说明:
    echo 1. 将 HintServiceMeow-LabAPI.dll 放入服务器的 dependencies 文件夹
    echo 2. 将 xiaozhoucust.dll 放入服务器的 plugins 文件夹
    echo 3. 重启服务器
    echo.
) else (
    echo.
    echo ✗ 编译失败！请检查错误信息。
    echo.
)

pause
