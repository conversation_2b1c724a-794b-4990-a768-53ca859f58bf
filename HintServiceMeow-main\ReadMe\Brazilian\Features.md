# Introdução das Funções
HintServiceMeow inclui as seguintes funções

#### Adaptador de Hint
  HintServiceMeow inclui um adaptador de hint que converte automaticamente outras hints de plug-ins para Hints compatíveis ao HintServiceMeow. Isso permite que pessoas utilizem qualquer plug-in que use hints, mesmo que não seja compatíveis entre si ou com o HintServiceMeow.

## Funções de Desenvolvedor
#### Funções de Hint
  Esse framework fornece métodos para exibir texto em uma posição específica na tela sem entrar em conflito com outros plug-ins.
#### Texto automático
  Um texto de atualização automática nas classes de Hint.
#### Dica Dinâmica
  Uma dica que se posiciona automaticamente na tela na posição mais ideal.
#### Atualização Automática
  Quaisquer mudanças a uma dica são automaticamente atualizadas na tela do jogador.
#### Previsão de atualização
  Esse framework pode analisar a frequência de atualização de cada dica e agendar atualizações de acordo. Você também pode personalizar o atraso de atualização para cada Hint, variando de Mais Rápido para 'Não-Sincronizado'.
#### UI do Jogador
  A UI do Jogador inclui componentes para simplificar seu desenvolvimento. Por exemplo, o componente de Common Hint da UI do Jogador permite que você mostre hints comumente utilizadas para um jogador usando um único método.
