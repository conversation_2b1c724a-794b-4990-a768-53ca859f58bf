using System;
using System.Collections.Generic;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint迁移助手 - 帮助现有代码迁移到新的hint管理器
    /// </summary>
    public static class HintMigrationHelper
    {


        /// <summary>
        /// 迁移现有的升级显示代码
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="levelBefore">升级前等级</param>
        /// <param name="levelAfter">升级后等级</param>
        /// <example>
        /// 旧代码：
        /// var levelUpHint = new Hint { ... };
        /// player.AddHint(levelUpHint);
        /// Timing.CallDelayed(15f, () => player.RemoveHint(levelUpHint));
        /// 
        /// 新代码：
        /// HintMigrationHelper.ShowLevelUpDisplay(player, levelBefore, levelAfter);
        /// </example>
        public static string ShowLevelUpDisplay(Player player, int levelBefore, int levelAfter)
        {
            Logger.Debug($"使用HintManager显示升级信息 - 玩家: {player.Nickname}, {levelBefore} -> {levelAfter}");
            return HintManager.Instance.ShowLevelUp(player, levelBefore, levelAfter);
        }

        /// <summary>
        /// 迁移现有的SCP-914启动信息代码
        /// </summary>
        /// <param name="nearbyPlayers">附近玩家列表</param>
        /// <param name="message">启动信息</param>
        /// <example>
        /// 旧代码：
        /// var scp914Hint = new Hint { YCoordinate = 800, ... };
        /// nearbyPlayer.AddHint(scp914Hint);
        /// Timing.CallDelayed(5f, () => nearbyPlayer.RemoveHint(scp914Hint));
        /// 
        /// 新代码：
        /// HintMigrationHelper.ShowSCP914StartupInfo(nearbyPlayers, message);
        /// </example>
        public static List<string> ShowSCP914StartupInfo(IEnumerable<Player> nearbyPlayers, string message)
        {
            var hintIds = new List<string>();
            foreach (var player in nearbyPlayers)
            {
                if (player != null)
                {
                    string hintId = player.ShowSCP914Info(message);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            Logger.Debug($"使用HintManager显示SCP-914启动信息给 {hintIds.Count} 个玩家");
            return hintIds;
        }

        /// <summary>
        /// 迁移现有的电梯交互代码
        /// </summary>
        /// <param name="nearbyPlayers">附近玩家列表</param>
        /// <param name="interactionMessage">交互信息</param>
        /// <example>
        /// 旧代码：
        /// var elevatorHint = new Hint { YCoordinate = 800, ... };
        /// nearbyPlayer.AddHint(elevatorHint);
        /// Timing.CallDelayed(3f, () => nearbyPlayer.RemoveHint(elevatorHint));
        /// 
        /// 新代码：
        /// HintMigrationHelper.ShowElevatorInteraction(nearbyPlayers, interactionMessage);
        /// </example>
        public static List<string> ShowElevatorInteraction(IEnumerable<Player> nearbyPlayers, string interactionMessage)
        {
            var hintIds = new List<string>();
            foreach (var player in nearbyPlayers)
            {
                if (player != null)
                {
                    string hintId = player.ShowElevatorInteraction(interactionMessage);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            Logger.Debug($"使用HintManager显示电梯交互信息给 {hintIds.Count} 个玩家");
            return hintIds;
        }

        /// <summary>
        /// 迁移现有的保安下班提示代码
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <example>
        /// 旧代码：
        /// var offDutyHint = new Hint { YCoordinate = 400, DelayTime = 0.5f, ... };
        /// Timing.CallDelayed(0.5f, () => {
        ///     player.AddHint(offDutyHint);
        ///     Timing.CallDelayed(10f, () => player.RemoveHint(offDutyHint));
        /// });
        /// 
        /// 新代码：
        /// HintMigrationHelper.ShowSecurityOffDutyHint(player);
        /// </example>
        public static string ShowSecurityOffDutyHint(Player player)
        {
            Logger.Debug($"使用HintManager显示保安下班提示 - 玩家: {player.Nickname}");
            return HintManager.Instance.ShowSecurityOffDuty(player);
        }

        /// <summary>
        /// 创建兼容的Hint对象（用于逐步迁移）
        /// </summary>
        /// <param name="hintType">Hint类型</param>
        /// <param name="text">显示文本</param>
        /// <param name="customYCoordinate">自定义Y坐标（可选）</param>
        /// <returns>兼容的Hint对象</returns>
        /// <example>
        /// 旧代码：
        /// var hint = new Hint { Alignment = HintAlignment.Center, YCoordinate = 400, ... };
        /// 
        /// 迁移代码：
        /// var hint = HintMigrationHelper.CreateCompatibleHint(HintType.Notification, "消息", 400);
        /// </example>
        public static Hint CreateCompatibleHint(HintType hintType, string text, int? customYCoordinate = null)
        {
            var config = HintTemplate.GetTemplate(hintType);
            config.Text = text;
            
            if (customYCoordinate.HasValue)
            {
                config.YCoordinate = customYCoordinate.Value;
            }

            return new Hint
            {
                Alignment = config.Alignment,
                YCoordinate = config.YCoordinate,
                FontSize = config.FontSize,
                LineHeight = config.LineHeight,
                SyncSpeed = config.SyncSpeed,
                Text = config.Text
            };
        }

        /// <summary>
        /// 批量迁移现有的Hint创建代码
        /// </summary>
        /// <param name="players">玩家列表</param>
        /// <param name="hintType">Hint类型</param>
        /// <param name="text">显示文本</param>
        /// <param name="duration">持续时间</param>
        /// <returns>显示的Hint ID列表</returns>
        /// <example>
        /// 旧代码：
        /// foreach (var player in players) {
        ///     var hint = new Hint { ... };
        ///     player.AddHint(hint);
        ///     Timing.CallDelayed(duration, () => player.RemoveHint(hint));
        /// }
        /// 
        /// 新代码：
        /// HintMigrationHelper.ShowHintToMultiplePlayers(players, HintType.Notification, "消息", 5f);
        /// </example>
        public static List<string> ShowHintToMultiplePlayers(IEnumerable<Player> players, HintType hintType, string text, float duration)
        {
            var hintIds = new List<string>();
            foreach (var player in players)
            {
                if (player != null)
                {
                    string hintId = HintManager.Instance.ShowHint(player, hintType, text, duration);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            Logger.Debug($"使用HintManager批量显示 {hintType} 给 {hintIds.Count} 个玩家");
            return hintIds;
        }

        /// <summary>
        /// 迁移指南 - 打印迁移建议到日志
        /// </summary>
        public static void PrintMigrationGuide()
        {
            Logger.Info("=== Hint管理器迁移指南 ===");
            Logger.Info("1. MVP显示迁移:");
            Logger.Info("   旧: var mvpHint = new Hint {...}; player.AddHint(mvpHint); Timing.CallDelayed(15f, () => player.RemoveHint(mvpHint));");
            Logger.Info("   新: player.ShowMVP(mvpPlayerName, damage);");
            Logger.Info("");
            Logger.Info("2. 升级显示迁移:");
            Logger.Info("   旧: var levelUpHint = new Hint {...}; player.AddHint(levelUpHint); Timing.CallDelayed(15f, () => player.RemoveHint(levelUpHint));");
            Logger.Info("   新: player.ShowLevelUp(levelBefore, levelAfter);");
            Logger.Info("");
            Logger.Info("3. 通知显示迁移:");
            Logger.Info("   旧: var hint = new Hint {...}; player.AddHint(hint); Timing.CallDelayed(5f, () => player.RemoveHint(hint));");
            Logger.Info("   新: player.ShowNotification(\"消息\", \"#FFFFFF\", 5f);");
            Logger.Info("");
            Logger.Info("4. 批量显示迁移:");
            Logger.Info("   旧: foreach循环创建和管理Hint");
            Logger.Info("   新: players.ShowNotificationToAll(\"消息\", \"#FFFFFF\", 5f);");
            Logger.Info("");
            Logger.Info("5. 清理迁移:");
            Logger.Info("   旧: 手动调用RemoveHint");
            Logger.Info("   新: 自动生命周期管理，或使用player.RemoveAllHints();");
            Logger.Info("========================");
        }

        /// <summary>
        /// 检查现有代码的迁移状态
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>迁移状态报告</returns>
        public static string CheckMigrationStatus(Player player)
        {
            if (player == null)
                return "玩家对象为null";

            try
            {
                int hintCount = HintManager.Instance.GetPlayerHintCount(player);
                // 由于_positionManager是私有的，我们无法直接访问，这里使用占位符
                var activeLayers = new List<int>(); // TODO: 需要在HintManager中添加公共方法来获取活跃层级
                
                return $"玩家 {player.Nickname} 迁移状态:\n" +
                       $"- 活跃Hint数量: {hintCount}\n" +
                       $"- 使用的层级: {string.Join(", ", activeLayers)}\n" +
                       $"- 管理器状态: 正常";
            }
            catch (Exception ex)
            {
                Logger.Error($"检查迁移状态失败: {ex.Message}");
                return $"检查失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 清理遗留的Hint（用于迁移过程中的清理）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>清理的数量</returns>
        public static int CleanupLegacyHints(Player player)
        {
            if (player == null)
                return 0;

            try
            {
                // 移除所有通过新管理器创建的Hint
                int removedCount = HintManager.Instance.RemovePlayerHints(player);
                Logger.Debug($"已清理玩家 {player.Nickname} 的 {removedCount} 个遗留Hint");
                return removedCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"清理遗留Hint失败: {ex.Message}");
                return 0;
            }
        }
    }
}
