using System;
using System.Collections.Concurrent;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using PlayerRoles;
using BlackRoseServer.Helper;
using MEC;


namespace BlackRoseServer.Display
{
    /// <summary>
    /// 右下角显示管理器 - 统一管理右下角的所有显示内容
    /// </summary>
    public class RightBottomDisplayManager : IDisposable
    {
        private static RightBottomDisplayManager _instance;
        private static readonly object _lock = new object();

        private readonly SimpleExperienceDisplay _experienceDisplay;
        private readonly SCPTeammateDisplay _scpTeammateDisplay;
        private readonly SCP079InfoDisplay _scp079InfoDisplay;
        private readonly ConcurrentDictionary<Player, DateTime> _lastUpdateTimes;
        
        private CoroutineHandle _updateCoroutine;
        private bool _disposed = false;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static RightBottomDisplayManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new RightBottomDisplayManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private RightBottomDisplayManager()
        {
            _experienceDisplay = SimpleExperienceDisplay.Instance;
            _scpTeammateDisplay = new SCPTeammateDisplay();
            _scp079InfoDisplay = new SCP079InfoDisplay();
            _lastUpdateTimes = new ConcurrentDictionary<Player, DateTime>();
            
            StartUpdateCoroutine();
            Logger.Info("RightBottomDisplayManager已初始化");
        }

        /// <summary>
        /// 显示经验获得提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="experienceGained">获得的经验值</param>
        /// <param name="isLevelUp">是否升级</param>
        public void ShowExperienceGain(Player player, int experienceGained, bool isLevelUp = false)
        {
            if (player == null || player.IsSCP || player.DoNotTrack)
                return;

            _experienceDisplay.ShowExperienceGain(player, experienceGained, isLevelUp);
        }

        /// <summary>
        /// 显示带消息的经验获得提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="experienceGained">获得的经验值</param>
        /// <param name="message">自定义消息</param>
        /// <param name="color">颜色（可选）</param>
        public void ShowExperienceGainWithMessage(Player player, int experienceGained, string message, string color = "#00FF00")
        {
            if (player == null || player.IsSCP || player.DoNotTrack)
                return;

            _experienceDisplay.ShowExperienceGainWithMessage(player, experienceGained, message, color);
        }

        /// <summary>
        /// 更新SCP队友信息显示
        /// </summary>
        /// <param name="player">SCP玩家</param>
        public void UpdateSCPTeammateInfo(Player player)
        {
            if (player == null || !player.IsSCP || player.Role == RoleTypeId.Scp079)
                return;

            try
            {
                _scpTeammateDisplay.ShowSCPTeammateInfo(player);
                _lastUpdateTimes.TryAdd(player, DateTime.Now);
            }
            catch (Exception ex)
            {
                Logger.Error($"更新SCP队友信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新SCP-079信息显示（使用专用简化UI）
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        public void UpdateSCP079Info(Player player)
        {
            if (player == null || player.Role != RoleTypeId.Scp079)
                return;

            try
            {
                // SCP-079使用专用的简化阵营显示
                BlackRoseServer.Display.SCP079TeammateDisplay.Instance.ShowSCP079TeammateInfo(player);
                _lastUpdateTimes.TryAdd(player, DateTime.Now);

                // 启动定期刷新，确保能看到新的队友
                Timing.CallDelayed(5.0f, () =>
                {
                    if (player != null && player.Role == RoleTypeId.Scp079)
                    {
                        BlackRoseServer.Display.SCP079TeammateDisplay.Instance.ShowSCP079TeammateInfo(player);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"更新SCP-079显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录电板激活
        /// </summary>
        /// <param name="generatorId">电板ID</param>
        /// <param name="position">电板位置</param>
        public void RecordGeneratorActivated(int generatorId, UnityEngine.Vector3 position)
        {
            _scp079InfoDisplay.RecordGeneratorActivated(generatorId, position);
            
            // 立即更新所有SCP-079玩家的显示
            foreach (var player in XHelper.PlayerList)
            {
                if (player != null && player.Role == RoleTypeId.Scp079)
                {
                    UpdateSCP079Info(player);
                }
            }
        }

        /// <summary>
        /// 记录电板取消激活
        /// </summary>
        /// <param name="generatorId">电板ID</param>
        public void RecordGeneratorDeactivated(int generatorId)
        {
            _scp079InfoDisplay.RecordGeneratorDeactivated(generatorId);
            
            // 立即更新所有SCP-079玩家的显示
            foreach (var player in XHelper.PlayerList)
            {
                if (player != null && player.Role == RoleTypeId.Scp079)
                {
                    UpdateSCP079Info(player);
                }
            }
        }

        /// <summary>
        /// 清理玩家数据
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void CleanupPlayerData(Player player)
        {
            if (player == null)
                return;

            try
            {
                _experienceDisplay.CleanupPlayerData(player);
                _scpTeammateDisplay.RemoveSCPTeammateInfo(player);

                // 清理SCP-079专用显示
                BlackRoseServer.Display.SCP079TeammateDisplay.Instance.RemoveSCP079TeammateInfo(player);

                _lastUpdateTimes.TryRemove(player, out _);

                Logger.Debug($"清理右下角显示数据 - 玩家: {player.Nickname}");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理玩家显示数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理玩家角色变更
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="oldRole">旧角色</param>
        /// <param name="newRole">新角色</param>
        public void OnPlayerRoleChanged(Player player, RoleTypeId oldRole, RoleTypeId newRole)
        {
            if (player == null)
                return;

            try
            {
                // 如果角色没有变化，跳过处理
                if (oldRole == newRole)
                {
                    Logger.Debug($"角色未变化，跳过处理 - 玩家: {player.Nickname}, 角色: {newRole}");
                    return;
                }

                // 清理旧角色的显示
                if (oldRole == RoleTypeId.Scp079)
                {
                    // 清理SCP-079专用显示
                    BlackRoseServer.Display.SCP079TeammateDisplay.Instance.RemoveSCP079TeammateInfo(player);
                }
                else if (XHelper.IsSCP(oldRole))
                {
                    // 清理普通SCP显示
                    _scpTeammateDisplay.RemoveSCPTeammateInfo(player);
                    Logger.Debug($"清理普通SCP显示 - 玩家: {player.Nickname}, 旧角色: {oldRole}");
                }

                // 根据新角色设置显示
                if (newRole == RoleTypeId.Scp079)
                {
                    // 延迟更长时间确保所有玩家角色切换完成
                    Timing.CallDelayed(2.0f, () =>
                    {
                        if (player != null && player.Role == RoleTypeId.Scp079)
                        {
                            UpdateSCP079Info(player);
                        }
                    });
                }
                else if (XHelper.IsSCP(newRole))
                {
                    UpdateSCPTeammateInfo(player);
                }

                // 处理计时器显示（观察者和OW模式）
                if (newRole == RoleTypeId.Spectator || newRole == RoleTypeId.Overwatch)
                {
                    BlackRoseServer.Helper.TimerDisplayHelper.Instance.InitForPlayer(player);
                }
                else if (oldRole == RoleTypeId.Spectator || oldRole == RoleTypeId.Overwatch)
                {
                    BlackRoseServer.Helper.TimerDisplayHelper.Instance.RemovePlayer(player);
                }

                Logger.Debug($"处理角色变更显示 - 玩家: {player.Nickname}, {oldRole} -> {newRole}");
            }
            catch (Exception ex)
            {
                Logger.Error($"处理玩家角色变更失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动更新协程
        /// </summary>
        private void StartUpdateCoroutine()
        {
            _updateCoroutine = Timing.RunCoroutine(UpdateDisplayCoroutine());
        }

        /// <summary>
        /// 显示更新协程
        /// </summary>
        private System.Collections.Generic.IEnumerator<float> UpdateDisplayCoroutine()
        {
            while (!_disposed)
            {
                try
                {
                    var now = DateTime.Now;
                    
                    foreach (var player in XHelper.PlayerList)
                    {
                        if (player == null || !player.IsAlive)
                            continue;

                        // 检查是否需要更新显示
                        bool needsUpdate = !_lastUpdateTimes.TryGetValue(player, out var lastUpdate) ||
                                         (now - lastUpdate).TotalSeconds >= 3.0; // 每3秒更新一次，减少频率

                        if (needsUpdate)
                        {
                            if (player.Role == RoleTypeId.Scp079)
                            {
                                UpdateSCP079Info(player);
                            }
                            else if (player.IsSCP)
                            {
                                UpdateSCPTeammateInfo(player);
                            }
                        }
                    }

                    // 清理过期的电板记录
                    _scp079InfoDisplay.CleanupExpiredRecords();
                }
                catch (Exception ex)
                {
                    Logger.Error($"显示更新协程错误: {ex.Message}");
                }

                yield return Timing.WaitForSeconds(1f);
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetStatistics()
        {
            try
            {
                var expStats = _experienceDisplay.GetStatistics();
                var activeUpdates = _lastUpdateTimes.Count;
                
                return $"RightBottomDisplayManager统计:\n" +
                       $"- 活跃更新玩家: {activeUpdates}\n" +
                       $"{expStats}";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取右下角显示统计信息失败: {ex.Message}");
                return "统计信息获取失败";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                // 停止更新协程
                if (_updateCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_updateCoroutine);
                }

                // 清理所有显示数据
                foreach (var player in XHelper.PlayerList)
                {
                    if (player != null)
                    {
                        CleanupPlayerData(player);
                    }
                }

                _lastUpdateTimes.Clear();
                _scp079InfoDisplay.ClearAllRecords();

                Logger.Info("RightBottomDisplayManager已释放");
            }
            catch (Exception ex)
            {
                Logger.Error($"释放RightBottomDisplayManager失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 静态释放方法
        /// </summary>
        public static void DisposeInstance()
        {
            lock (_lock)
            {
                _instance?.Dispose();
                _instance = null;
            }
        }
    }
}
