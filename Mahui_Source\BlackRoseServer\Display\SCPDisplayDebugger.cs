using System;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Helper;
using PlayerRoles;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// SCP显示调试工具
    /// </summary>
    public static class SCPDisplayDebugger
    {
        /// <summary>
        /// 调试SCP显示系统
        /// </summary>
        public static void DebugSCPDisplay()
        {
            Logger.Info("=== SCP显示系统调试 ===");
            
            try
            {
                // 检查RightBottomDisplayManager状态
                CheckDisplayManager();
                
                // 检查所有SCP玩家
                CheckSCPPlayers();
                
                // 检查显示更新协程
                CheckUpdateCoroutine();
                
                Logger.Info("=== SCP显示系统调试完成 ===");
            }
            catch (Exception ex)
            {
                Logger.Error($"SCP显示调试失败: {ex.Message}");
                Logger.Debug($"调试详细错误: {ex}");
            }
        }

        /// <summary>
        /// 检查显示管理器状态
        /// </summary>
        private static void CheckDisplayManager()
        {
            Logger.Info("--- 检查显示管理器 ---");
            
            try
            {
                var manager = RightBottomDisplayManager.Instance;
                if (manager == null)
                {
                    Logger.Error("❌ RightBottomDisplayManager.Instance 为 null");
                    return;
                }
                
                Logger.Info("✅ RightBottomDisplayManager 已初始化");
                
                // 获取统计信息
                var stats = manager.GetStatistics();
                Logger.Info($"📊 管理器统计: {stats}");
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 检查显示管理器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查SCP玩家
        /// </summary>
        private static void CheckSCPPlayers()
        {
            Logger.Info("--- 检查SCP玩家 ---");
            
            try
            {
                var allPlayers = XHelper.PlayerList.Where(p => p != null).ToList();
                var scpPlayers = allPlayers.Where(p => p.IsSCP).ToList();
                var scp079Players = allPlayers.Where(p => p.Role == RoleTypeId.Scp079).ToList();
                
                Logger.Info($"📈 总玩家数: {allPlayers.Count}");
                Logger.Info($"🔴 SCP玩家数: {scpPlayers.Count}");
                Logger.Info($"🤖 SCP-079玩家数: {scp079Players.Count}");
                
                if (scpPlayers.Count == 0)
                {
                    Logger.Warn("⚠️ 当前没有SCP玩家，无法测试SCP显示");
                    return;
                }
                
                // 详细检查每个SCP玩家
                foreach (var player in scpPlayers)
                {
                    CheckSCPPlayer(player);
                }
                
                // 检查SCP-079玩家
                foreach (var player in scp079Players)
                {
                    CheckSCP079Player(player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 检查SCP玩家失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查单个SCP玩家
        /// </summary>
        private static void CheckSCPPlayer(Player player)
        {
            try
            {
                Logger.Info($"🔍 检查SCP玩家: {player.Nickname}");
                Logger.Info($"   角色: {player.Role}");
                Logger.Info($"   是否SCP: {player.IsSCP}");
                Logger.Info($"   是否存活: {player.IsAlive}");
                Logger.Info($"   血量: {player.Health}/{player.MaxHealth}");
                
                // 检查区域
                var zone = ZoneDetector.GetPlayerZone(player);
                var zoneName = ZoneDetector.GetZoneName(zone);
                Logger.Info($"   区域: {zoneName} ({zone})");
                
                // 尝试手动更新显示
                try
                {
                    RightBottomDisplayManager.Instance.UpdateSCPTeammateInfo(player);
                    Logger.Info($"   ✅ 手动更新SCP队友显示成功");
                }
                catch (Exception ex)
                {
                    Logger.Error($"   ❌ 手动更新SCP队友显示失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 检查SCP玩家 {player?.Nickname} 失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查SCP-079玩家
        /// </summary>
        private static void CheckSCP079Player(Player player)
        {
            try
            {
                Logger.Info($"🤖 检查SCP-079玩家: {player.Nickname}");
                Logger.Info($"   角色: {player.Role}");
                Logger.Info($"   是否存活: {player.IsAlive}");
                
                // 尝试手动更新显示
                try
                {
                    RightBottomDisplayManager.Instance.UpdateSCP079Info(player);
                    Logger.Info($"   ✅ 手动更新SCP-079显示成功");
                }
                catch (Exception ex)
                {
                    Logger.Error($"   ❌ 手动更新SCP-079显示失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 检查SCP-079玩家 {player?.Nickname} 失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查更新协程
        /// </summary>
        private static void CheckUpdateCoroutine()
        {
            Logger.Info("--- 检查更新协程 ---");
            
            try
            {
                // 这里无法直接检查协程状态，但可以通过其他方式验证
                Logger.Info("📝 更新协程应该每秒运行一次");
                Logger.Info("📝 如果SCP显示没有更新，可能是协程问题");
                Logger.Info("📝 建议检查服务器日志中的协程错误信息");
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 检查更新协程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制更新所有SCP显示
        /// </summary>
        public static void ForceUpdateAllSCPDisplays()
        {
            Logger.Info("🔄 强制更新所有SCP显示...");
            
            try
            {
                var manager = RightBottomDisplayManager.Instance;
                if (manager == null)
                {
                    Logger.Error("❌ RightBottomDisplayManager 未初始化");
                    return;
                }
                
                int updatedCount = 0;
                
                foreach (var player in XHelper.PlayerList.Where(p => p != null && p.IsAlive))
                {
                    try
                    {
                        if (player.Role == RoleTypeId.Scp079)
                        {
                            manager.UpdateSCP079Info(player);
                            updatedCount++;
                            Logger.Debug($"✅ 更新SCP-079显示: {player.Nickname}");
                        }
                        else if (player.IsSCP)
                        {
                            manager.UpdateSCPTeammateInfo(player);
                            updatedCount++;
                            Logger.Debug($"✅ 更新SCP队友显示: {player.Nickname}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"❌ 更新玩家 {player.Nickname} 显示失败: {ex.Message}");
                    }
                }
                
                Logger.Info($"🔄 强制更新完成，更新了 {updatedCount} 个SCP玩家的显示");
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 强制更新所有SCP显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试SCP队友显示内容
        /// </summary>
        public static void TestSCPTeammateDisplay()
        {
            Logger.Info("🧪 测试SCP队友显示内容...");
            
            try
            {
                var scpPlayers = XHelper.PlayerList.Where(p => p != null && p.IsSCP).ToList();
                
                if (scpPlayers.Count == 0)
                {
                    Logger.Warn("⚠️ 没有SCP玩家可供测试");
                    return;
                }
                
                var display = new SCPTeammateDisplay();
                
                foreach (var player in scpPlayers)
                {
                    if (player.Role != RoleTypeId.Scp079) // SCP-079不显示队友信息
                    {
                        try
                        {
                            display.ShowSCPTeammateInfo(player);
                            Logger.Info($"✅ 测试玩家 {player.Nickname} 的SCP队友显示成功");
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"❌ 测试玩家 {player.Nickname} 的SCP队友显示失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 测试SCP队友显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理所有SCP显示
        /// </summary>
        public static void CleanupAllSCPDisplays()
        {
            Logger.Info("🧹 清理所有SCP显示...");
            
            try
            {
                var manager = RightBottomDisplayManager.Instance;
                if (manager == null)
                {
                    Logger.Error("❌ RightBottomDisplayManager 未初始化");
                    return;
                }
                
                int cleanedCount = 0;
                
                foreach (var player in XHelper.PlayerList.Where(p => p != null))
                {
                    try
                    {
                        manager.CleanupPlayerData(player);
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"❌ 清理玩家 {player.Nickname} 显示数据失败: {ex.Message}");
                    }
                }
                
                Logger.Info($"🧹 清理完成，清理了 {cleanedCount} 个玩家的显示数据");
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 清理所有SCP显示失败: {ex.Message}");
            }
        }
    }
}
