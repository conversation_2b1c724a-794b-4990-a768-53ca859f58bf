﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.CompressionLevel">
      <summary>Specifica i valori che indicano se un'operazione di compressione privilegia le dimensioni di compressione o la velocità.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Fastest">
      <summary>L'operazione di compressione deve essere completata il più rapidamente possibile, anche se il file risultante non è compresso in modo ottimale.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.NoCompression">
      <summary>Non deve essere eseguita alcuna compressione del file.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Optimal">
      <summary>L'operazione di compressione deve essere compressa in modo ottimale, anche se l'operazione richiede più tempo per il completamento.</summary>
    </member>
    <member name="T:System.IO.Compression.CompressionMode">
      <summary> Specifica se comprimere o decomprimere il flusso sottostante.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Compress">
      <summary>Comprime il flusso sottostante.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Decompress">
      <summary>Decomprime il flusso sottostante.</summary>
    </member>
    <member name="T:System.IO.Compression.DeflateStream">
      <summary>Fornisce metodi e proprietà per la compressione e la decompressione dei flussi mediante l'algoritmo Deflate.</summary>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.DeflateStream" /> usando il livello di compressione del flusso specificato.</summary>
      <param name="stream">Flusso da comprimere.</param>
      <param name="compressionLevel">Uno dei valori di enumerazione che indica se evidenziare l'efficacia di velocità o compressione quando si comprime il flusso.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il flusso non supporta le operazioni di scrittura come la compressione.(La proprietà di <see cref="P:System.IO.Stream.CanWrite" /> sull'oggetto di flusso è false).</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.DeflateStream" /> usando il livello di compressione del flusso specificato. Facoltativamente mantiene aperto il flusso.</summary>
      <param name="stream">Flusso da comprimere.</param>
      <param name="compressionLevel">Uno dei valori di enumerazione che indica se evidenziare l'efficacia di velocità o compressione quando si comprime il flusso.</param>
      <param name="leaveOpen">true per mantenere l'oggetto flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.DeflateStream" />; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il flusso non supporta le operazioni di scrittura come la compressione.(La proprietà di <see cref="P:System.IO.Stream.CanWrite" /> sull'oggetto di flusso è false).</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.DeflateStream" /> usando la modalità di compressione del flusso specificata.</summary>
      <param name="stream">Flusso da comprimere o decomprimere.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se comprimere o decomprimere il flusso.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.IO.Compression.CompressionMode" /> valido.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Compress" />  e <see cref="P:System.IO.Stream.CanWrite" /> è false.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  e <see cref="P:System.IO.Stream.CanRead" /> è false.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.DeflateStream" /> usando la modalità di compressione del flusso specificata. Facoltativamente mantiene aperto il flusso.</summary>
      <param name="stream">Flusso da comprimere o decomprimere.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se comprimere o decomprimere il flusso.</param>
      <param name="leaveOpen">true per mantenere il flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.DeflateStream" />; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.IO.Compression.CompressionMode" /> valido.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Compress" />  e <see cref="P:System.IO.Stream.CanWrite" /> è false.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  e <see cref="P:System.IO.Stream.CanRead" /> è false.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.BaseStream">
      <summary>Ottiene un riferimento al flusso sottostante.</summary>
      <returns>Oggetto flusso che rappresenta il flusso sottostante.</returns>
      <exception cref="T:System.ObjectDisposedException">Il flusso sottostante è chiuso.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanRead">
      <summary>Ottiene un valore che indica se il flusso supporta la lettura durante la decompressione di un file.</summary>
      <returns>true se il valore di <see cref="T:System.IO.Compression.CompressionMode" /> è Decompress e il flusso sottostante è aperto e supporta la lettura; in caso contrario, false.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanSeek">
      <summary>Ottiene un valore che indica se il flusso supporta la ricerca.</summary>
      <returns>false in tutti i casi.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanWrite">
      <summary>Ottiene un valore che indica se il flusso supporta la scrittura.</summary>
      <returns>true se il valore di <see cref="T:System.IO.Compression.CompressionMode" /> è Compress e il flusso sottostante non è chiuso e supporta la scrittura; in caso contrario, false.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.IO.Compression.DeflateStream" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Flush">
      <summary>L'implementazione corrente di questo metodo non presenta alcuna funzionalità.</summary>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Length">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Position">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge un numero di byte decompressi nella matrice di byte specificata.</summary>
      <returns>Numero di byte letti nella matrice di byte.</returns>
      <param name="array">Matrice per archiviare i byte decompressi.</param>
      <param name="offset">Offset dei byte in <paramref name="array" /> in corrispondenza del quale cui verranno inseriti i byte letti.</param>
      <param name="count">Numero massimo di byte decompressi da leggere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Al momento della creazione dell'oggetto, il valore di <see cref="T:System.IO.Compression.CompressionMode" /> era Compress.-oppure- Il flusso sottostante non supporta la lettura.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> è minore di zero.-oppure-La lunghezza di <paramref name="array" /> meno il punto iniziale dell'indice è minore di <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">Il formato dei dati non è valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Questa operazione non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <param name="offset">Posizione all'interno del flusso.</param>
      <param name="origin">Uno dei valori di <see cref="T:System.IO.SeekOrigin" />.</param>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.SetLength(System.Int64)">
      <summary>Questa operazione non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">Lunghezza del flusso.</param>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Scrive i byte compressi nel flusso sottostante dalla matrice di byte specificata.</summary>
      <param name="array">Buffer contenente i dati da comprimere.</param>
      <param name="offset">Offset dei byte in <paramref name="array" /> da cui verranno letti i byte.</param>
      <param name="count">Numero massimo di byte da scrivere.</param>
    </member>
    <member name="T:System.IO.Compression.GZipStream">
      <summary>Fornisce i metodi e le proprietà usate per comprimere e decomprimere i flussi.</summary>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.GZipStream" /> usando il livello di compressione del flusso specificato.</summary>
      <param name="stream">Flusso in cui scrivere i dati compressi.</param>
      <param name="compressionLevel">Uno dei valori di enumerazione che indica se evidenziare l'efficacia di velocità o compressione quando si comprime il flusso.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il flusso non supporta le operazioni di scrittura come la compressione.(La proprietà di <see cref="P:System.IO.Stream.CanWrite" /> sull'oggetto di flusso è false).</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.GZipStream" /> usando il livello di compressione del flusso specificato. Facoltativamente mantiene aperto il flusso.</summary>
      <param name="stream">Flusso in cui scrivere i dati compressi.</param>
      <param name="compressionLevel">Uno dei valori di enumerazione che indica se evidenziare l'efficacia di velocità o compressione quando si comprime il flusso.</param>
      <param name="leaveOpen">true per mantenere l'oggetto flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.GZipStream" />; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">Il flusso non supporta le operazioni di scrittura come la compressione.(La proprietà di <see cref="P:System.IO.Stream.CanWrite" /> sull'oggetto di flusso è false).</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.GZipStream" /> usando la modalità di compressione del flusso specificata.</summary>
      <param name="stream">Flusso in cui vengono scritti i dati compressi e decompressi.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se comprimere o decomprimere il flusso.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore dell'enumerazione <see cref="T:System.IO.Compression.CompressionMode" /> valido.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Compress" />  e <see cref="P:System.IO.Stream.CanWrite" /> è false.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  e <see cref="P:System.IO.Stream.CanRead" /> è false.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.GZipStream" /> usando la modalità di compressione del flusso specificata. Facoltativamente mantiene aperto il flusso.</summary>
      <param name="stream">Flusso in cui vengono scritti i dati compressi e decompressi.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se comprimere o decomprimere il flusso.</param>
      <param name="leaveOpen">true per mantenere il flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.GZipStream" />; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> non è un valore di <see cref="T:System.IO.Compression.CompressionMode" /> valido.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Compress" />  e <see cref="P:System.IO.Stream.CanWrite" /> è false.-oppure-<see cref="T:System.IO.Compression.CompressionMode" /> è <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  e <see cref="P:System.IO.Stream.CanRead" /> è false.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.BaseStream">
      <summary>Ottiene un riferimento al flusso sottostante.</summary>
      <returns>Oggetto flusso che rappresenta il flusso sottostante.</returns>
      <exception cref="T:System.ObjectDisposedException">Il flusso sottostante è chiuso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanRead">
      <summary>Ottiene un valore che indica se il flusso supporta la lettura durante la decompressione di un file.</summary>
      <returns>true se il valore di <see cref="T:System.IO.Compression.CompressionMode" /> è Decompress, e il flusso sottostante non è chiuso e supporta la lettura; in caso contrario, false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanSeek">
      <summary>Ottiene un valore che indica se il flusso supporta la ricerca.</summary>
      <returns>false in tutti i casi.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanWrite">
      <summary>Ottiene un valore che indica se il flusso supporta la scrittura.</summary>
      <returns>true se il valore di <see cref="T:System.IO.Compression.CompressionMode" /> è Compress e il flusso sottostante non è chiuso e supporta la scrittura; in caso contrario, false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.IO.Compression.GZipStream" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Flush">
      <summary>L'implementazione corrente di questo metodo non presenta alcuna funzionalità.</summary>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Length">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Position">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Legge un numero di byte decompressi nella matrice di byte specificata.</summary>
      <returns>Numero di byte decompressi nella matrice di byte.Se è stata raggiunta la fine del flusso, viene restituito zero o il numero di byte letto.</returns>
      <param name="array">Matrice usata per archiviare i byte decompressi.</param>
      <param name="offset">Offset dei byte in <paramref name="array" /> in corrispondenza del quale cui verranno inseriti i byte letti.</param>
      <param name="count">Numero massimo di byte decompressi da leggere.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> è null.</exception>
      <exception cref="T:System.InvalidOperationException">Al momento della creazione dell'oggetto, il valore di <see cref="T:System.IO.Compression.CompressionMode" /> era Compress.-oppure-Il flusso sottostante non supporta la lettura.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> è minore di zero.-oppure-La lunghezza di <paramref name="array" /> meno il punto iniziale dell'indice è minore di <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">Il formato dei dati non è valido.</exception>
      <exception cref="T:System.ObjectDisposedException">Il flusso è chiuso.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Valore long.</returns>
      <param name="offset">Posizione all'interno del flusso.</param>
      <param name="origin">Uno dei valori di <see cref="T:System.IO.SeekOrigin" />.</param>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.SetLength(System.Int64)">
      <summary>Questa proprietà non è supportata e genera sempre un'eccezione <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">Lunghezza del flusso.</param>
      <exception cref="T:System.NotSupportedException">Questa proprietà non è supportata su questo flusso.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Scrive i byte compressi nel flusso sottostante dalla matrice di byte specificata.</summary>
      <param name="array">Buffer contenente i dati da comprimere.</param>
      <param name="offset">Offset dei byte in <paramref name="array" /> da cui verranno letti i byte.</param>
      <param name="count">Numero massimo di byte da scrivere.</param>
      <exception cref="T:System.ObjectDisposedException">Non è possibile eseguire l'operazione di scrittura perché il flusso è chiuso.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipArchive">
      <summary>Rappresenta un pacchetto di file compressi nel formato di archiviazione ZIP.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.ZipArchive" /> dal flusso specificato.</summary>
      <param name="stream">Flusso che contiene l'archivio da leggere.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed or does not support reading.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream are not in the zip archive format.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.ZipArchive" /> dal flusso specificato e con la modalità specificata.</summary>
      <param name="stream">Flusso di input o output.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se l'archivio ZIP viene usato per leggere, creare o aggiornare le voci.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.ZipArchive" /> sulla base del flusso specificato per la modalità specificata e, facoltativamente, mantiene aperto il flusso.</summary>
      <param name="stream">Flusso di input o output.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se l'archivio ZIP viene usato per leggere, creare o aggiornare le voci.</param>
      <param name="leaveOpen">true per mantenere il flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.ZipArchive" />; in caso contrario, false.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.IO.Compression.ZipArchive" /> sulla base del flusso specificato per la modalità specificata. Usa la codifica specificata per i nomi delle voci e, facoltativamente, mantiene aperto il flusso.</summary>
      <param name="stream">Flusso di input o output.</param>
      <param name="mode">Uno dei valori di enumerazione che indica se l'archivio ZIP viene usato per leggere, creare o aggiornare le voci.</param>
      <param name="leaveOpen">true per mantenere il flusso aperto dopo avere eliminato l'oggetto <see cref="T:System.IO.Compression.ZipArchive" />; in caso contrario, false.</param>
      <param name="entryNameEncoding">La codifica da usate durante la lettura o la scrittura dei nomi delle voci in questo archivio.Specificare un valore per il parametro solo quando una codifica è obbligatoria per l'interoperabilità con gli strumenti e le librerie dell'archivio ZIP che non supportano la codifica UTF-8 per i nomi di voce.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String)">
      <summary>Crea una voce vuota con il percorso e il nome della voce specificati nell'archivio ZIP.</summary>
      <returns>Voce vuota nell'archivio ZIP.</returns>
      <param name="entryName">Percorso, relativo alla radice dell'archivio, che specifica il nome della voce da creare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String,System.IO.Compression.CompressionLevel)">
      <summary>Crea una voce vuota con il livello di compressione e il nome voce specificati nell'archivio ZIP.</summary>
      <returns>Voce vuota nell'archivio ZIP.</returns>
      <param name="entryName">Percorso, relativo alla radice dell'archivio, che specifica il nome della voce da creare.</param>
      <param name="compressionLevel">Uno dei valori di enumerazione che indica se privilegiare la velocità o l'efficacia di compressione quando si crea la voce.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose">
      <summary>Rilascia le risorse usate dall'istanza corrente della classe <see cref="T:System.IO.Compression.ZipArchive" />.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose(System.Boolean)">
      <summary>Viene chiamato dai metodi <see cref="M:System.IO.Compression.ZipArchive.Dispose" /> e <see cref="M:System.Object.Finalize" /> per rilasciare le risorse non gestite usate dall'istanza corrente della classe <see cref="T:System.IO.Compression.ZipArchive" /> e, facoltativamente, completa la scrittura dell'archivio e rilascia le risorse gestite.</summary>
      <param name="disposing">true per completare la scrittura dell'archivio e rilasciare le risorse gestite e non gestite; false per rilasciare solo le risorse non gestite.</param>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Entries">
      <summary>Ottiene la raccolta di voci attualmente presenti nell'archivio ZIP.</summary>
      <returns>La raccolta di voci attualmente presenti nell'archivio ZIP.</returns>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.GetEntry(System.String)">
      <summary>Recupera un wrapper per la voce specificata nell'archivio ZIP.</summary>
      <returns>Wrapper per la voce specificata nell'archivio; null se la voce non esiste nell'archivio.</returns>
      <param name="entryName">Percorso, relativo alla radice dell'archivio, che identifica la voce da recuperare.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Mode">
      <summary>Ottiene un valore che descrive il tipo di azione che l'archivio ZIP può eseguire sulle voci.</summary>
      <returns>Uno dei valori di enumerazione che descrive il tipo di azione (leggere, creare o aggiornare) che l'archivio ZIP può eseguire sulle voci.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveEntry">
      <summary>Rappresenta un file compresso in un archivio ZIP.</summary>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Archive">
      <summary>Ottiene l'archivio ZIP cui appartiene la voce.</summary>
      <returns>Archivio ZIP cui appartiene la voce o null se la voce è stata eliminata.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.CompressedLength">
      <summary>Ottiene la dimensione compressa della voce nell'archivio ZIP.</summary>
      <returns>La dimensione compressa della voce nell'archivio ZIP.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Delete">
      <summary>Elimina la voce dall'archivio ZIP.</summary>
      <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.FullName">
      <summary>Ottiene il percorso relativo della voce nell'archivio ZIP.</summary>
      <returns>Percorso relativo della voce nell'archivio ZIP.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.LastWriteTime">
      <summary>Ottiene o imposta l'ultima volta in cui la voce nell'archivio ZIP è stata modificata.</summary>
      <returns>L'ultima volta in cui la voce nell'archivio ZIP è stata modificata.</returns>
      <exception cref="T:System.NotSupportedException">The attempt to set this property failed, because the zip archive for the entry is in <see cref="F:System.IO.Compression.ZipArchiveMode.Read" /> mode.</exception>
      <exception cref="T:System.IO.IOException">The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />.- or -The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and the entry has been opened.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt was made to set this property to a value that is either earlier than 1980 January 1 0:00:00 (midnight) or later than 2107 December 31 23:59:58 (one second before midnight).</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Length">
      <summary>Ottiene la dimensione non compressa della voce nell'archivio ZIP.</summary>
      <returns>Dimensione non compressa della voce nell'archivio ZIP.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Name">
      <summary>Ottiene il nome file della voce nell'archivio ZIP.</summary>
      <returns>Nome file della voce nell'archivio ZIP.</returns>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Open">
      <summary>Apre la voce dall'archivio ZIP.</summary>
      <returns>Flusso che rappresenta il contenuto della voce.</returns>
      <exception cref="T:System.IO.IOException">The entry is already currently open for writing.-or-The entry has been deleted from the archive.-or-The archive for this entry was opened with the <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> mode, and this entry has already been written to. </exception>
      <exception cref="T:System.IO.InvalidDataException">The entry is either missing from the archive or is corrupt and cannot be read. -or-The entry has been compressed by using a compression method that is not supported.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.ToString">
      <summary>Recupera il percorso relativo della voce nell'archivio ZIP.</summary>
      <returns>Percorso relativo della voce, ovvero il valore archiviato nella proprietà <see cref="P:System.IO.Compression.ZipArchiveEntry.FullName" />.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveMode">
      <summary>Specifica i valori per l'interazione con le voci dell'archivio ZIP.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Create">
      <summary>È consentita solo la creazione di nuove voci dell'archivio.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Read">
      <summary>È consentita solo la lettura delle voci dell'archivio.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Update">
      <summary>Sono consentite sia operazioni in lettura che in scrittura per le voci dell'archivio.</summary>
    </member>
  </members>
</doc>