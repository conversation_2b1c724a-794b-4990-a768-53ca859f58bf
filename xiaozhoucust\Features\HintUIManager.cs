using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Utilities;
using HintServiceMeow.Core.Enum;
using MEC;
using Respawning;
using Respawning.Waves;
using PlayerRoles;

namespace xiaozhoucust.Features
{
    public class HintUIManager : IDisposable
    {
        private readonly Dictionary<Player, PlayerHintData> _playerHints = new Dictionary<Player, PlayerHintData>();
        private CoroutineHandle _updateCoroutine;
        private bool _isRoundActive = false;

        public void OnRoundStarted()
        {
            if (!Plugin.Instance.Config.EnableHintUI)
            {
                Logger.Info("HintUI功能已禁用");
                return;
            }

            _isRoundActive = true;
            _updateCoroutine = Timing.RunCoroutine(UpdateHintsCoroutine());
            Logger.Info("HintUI功能已启用");
        }

        public void OnRoundEnded()
        {
            _isRoundActive = false;
            if (_updateCoroutine.IsValid)
            {
                Timing.KillCoroutines(_updateCoroutine);
            }

            // 清理所有玩家的提示
            foreach (var kvp in _playerHints)
            {
                kvp.Value.Dispose();
            }
            _playerHints.Clear();

            Logger.Debug("HintUI协程已停止", Plugin.Instance.Config.EnableDebugLog);
        }

        public void OnPlayerJoined(Player player)
        {
            if (!Plugin.Instance.Config.EnableHintUI)
                return;

            try
            {
                var hintData = new PlayerHintData(player);
                _playerHints[player] = hintData;
                Logger.Debug($"为玩家 {player.Nickname} 初始化HintUI", Plugin.Instance.Config.EnableDebugLog);
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家 {player.Nickname} 初始化HintUI时出错: {ex.Message}");
            }
        }

        private IEnumerator<float> UpdateHintsCoroutine()
        {
            while (_isRoundActive)
            {
                try
                {
                    UpdateAllPlayerHints();
                }
                catch (Exception ex)
                {
                    Logger.Error($"更新HintUI时出错: {ex.Message}");
                }

                yield return Timing.WaitForSeconds(1f);
            }
        }

        private void UpdateAllPlayerHints()
        {
            var playersToRemove = new List<Player>();

            foreach (var kvp in _playerHints.ToList())
            {
                var player = kvp.Key;
                var hintData = kvp.Value;

                if (player == null || !player.IsConnected)
                {
                    playersToRemove.Add(player);
                    continue;
                }

                try
                {
                    hintData.UpdateHints();
                }
                catch (Exception ex)
                {
                    Logger.Error($"更新玩家 {player.Nickname} 的HintUI时出错: {ex.Message}");
                }
            }

            // 清理断开连接的玩家
            foreach (var player in playersToRemove)
            {
                if (_playerHints.TryGetValue(player, out var hintData))
                {
                    hintData.Dispose();
                    _playerHints.Remove(player);
                }
            }
        }

        public void Dispose()
        {
            _isRoundActive = false;
            if (_updateCoroutine.IsValid)
            {
                Timing.KillCoroutines(_updateCoroutine);
            }

            foreach (var kvp in _playerHints)
            {
                kvp.Value.Dispose();
            }
            _playerHints.Clear();
        }
    }

    public class PlayerHintData : IDisposable
    {
        private readonly Player _player;
        private readonly PlayerDisplay _playerDisplay;
        private DynamicHint _respawnTimerHint;
        private Hint _groupNumberHint;
        private Hint _serverNameHint;

        public PlayerHintData(Player player)
        {
            _player = player;
            _playerDisplay = PlayerDisplay.Get(player);
            InitializeHints();
        }

        private void InitializeHints()
        {
            var config = Plugin.Instance.Config;

            // 重生计算器（动态提示）
            if (config.ShowRespawnTimer)
            {
                _respawnTimerHint = new DynamicHint
                {
                    AutoText = _ => GetRespawnTimerText(),
                    FontSize = config.RespawnTimerFontSize,
                    SyncSpeed = HintSyncSpeed.Fast,
                    TargetY = 105
                };
                _playerDisplay.AddHint(_respawnTimerHint);
            }

            // 群号显示（固定位置）
            if (config.ShowServerInfo && !string.IsNullOrEmpty(config.GroupNumber))
            {
                _groupNumberHint = new Hint
                {
                    Text = $"QQ群: {config.GroupNumber}",
                    FontSize = config.GroupNumberFontSize,
                    YCoordinate = config.GroupNumberYPosition,
                    Alignment = HintAlignment.Right
                };
                _playerDisplay.AddHint(_groupNumberHint);
            }

            // 服务器名称显示（固定位置）
            if (config.ShowServerInfo && !string.IsNullOrEmpty(config.ServerName))
            {
                _serverNameHint = new Hint
                {
                    Text = config.ServerName,
                    FontSize = config.ServerNameFontSize,
                    YCoordinate = config.ServerNameYPosition,
                    Alignment = HintAlignment.Right
                };
                _playerDisplay.AddHint(_serverNameHint);
            }
        }

        public void UpdateHints()
        {
            // 重生计算器会自动更新，这里可以添加其他需要手动更新的逻辑
        }

        private string GetRespawnTimerText()
        {
            if (!_player.IsDead && _player.Role != RoleTypeId.Spectator)
                return string.Empty;

            try
            {
                var sb = new StringBuilder();
                sb.Append("<align=center>");

                // 获取重生时间信息
                var ntfTime = GetNtfRespawnTime();
                var chaosTime = GetChaosRespawnTime();

                // MTF重生时间
                sb.Append($"<color=#0096FF>MTF: {FormatTime(ntfTime)}</color>");
                sb.Append("  ");
                // 混沌重生时间
                sb.Append($"<color=#008F1E>CI: {FormatTime(chaosTime)}</color>");

                sb.Append("</align>");
                return sb.ToString();
            }
            catch (Exception ex)
            {
                Logger.Error($"获取重生计时器文本时出错: {ex.Message}");
                return string.Empty;
            }
        }

        private TimeSpan GetNtfRespawnTime()
        {
            try
            {
                // 获取MTF重生波次
                var ntfWave = WaveManager.Waves.FirstOrDefault(w => w.TargetFaction == Faction.FoundationStaff);
                if (ntfWave != null && ntfWave.Timer != null)
                {
                    var timeLeft = ntfWave.Timer.TimeLeft;
                    return timeLeft > 0 ? TimeSpan.FromSeconds(timeLeft + 18) : TimeSpan.Zero;
                }
                return TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取MTF重生时间时出错: {ex.Message}", Plugin.Instance.Config.EnableDebugLog);
                return TimeSpan.Zero;
            }
        }

        private TimeSpan GetChaosRespawnTime()
        {
            try
            {
                // 获取混沌重生波次
                var chaosWave = WaveManager.Waves.FirstOrDefault(w => w.TargetFaction == Faction.FoundationEnemy);
                if (chaosWave != null && chaosWave.Timer != null)
                {
                    var timeLeft = chaosWave.Timer.TimeLeft;
                    return timeLeft > 0 ? TimeSpan.FromSeconds(timeLeft + 13) : TimeSpan.Zero;
                }
                return TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取混沌重生时间时出错: {ex.Message}", Plugin.Instance.Config.EnableDebugLog);
                return TimeSpan.Zero;
            }
        }

        private string FormatTime(TimeSpan time)
        {
            return $"{time.Minutes:D1}:{time.Seconds:D2}";
        }

        public void Dispose()
        {
            _respawnTimerHint = null;
            _groupNumberHint = null;
            _serverNameHint = null;
        }
    }
}
