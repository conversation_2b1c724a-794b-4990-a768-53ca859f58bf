using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Manager;
using BlackRoseServer.API;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using MEC;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// 顶部UI显示管理器(实际上应该是底下)
    /// </summary>
    public class TopUIDisplay
    {
        private static TopUIDisplay _instance;
        public static TopUIDisplay Instance => _instance ??= new TopUIDisplay();

        private readonly ConcurrentDictionary<string, Player> _activePlayers = new ConcurrentDictionary<string, Player>();
        private CoroutineHandle _updateCoroutine;

        /// <summary>
        /// 显示顶部UI信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void ShowTopUI(Player player)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserId))
                {
                    return;
                }

                // 构建顶部UI文本
                string topUIText = BuildTopUIText(player);

                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = topUIText,
                    XCoordinate = 0f, // 居中显示
                    YCoordinate = 1050, // 顶部位置
                    FontSize = 20, // 适中的字体大小
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center, // 居中对齐
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = BlackRoseServer.Manager.HintPriority.High,
                    CustomId = $"top_ui_{player.UserId}", // 顶部UI的独立ID
                    AllowOverlap = true // 允许重叠
                };

                // 显示顶部UI
                HintManager.Instance.ShowHint(player, config);

                // 添加到活跃玩家列表
                _activePlayers.TryAdd(player.UserId, player);

                // 启动定时更新协程（如果还没有启动）
                if (!_updateCoroutine.IsRunning)
                {
                    _updateCoroutine = Timing.RunCoroutine(UpdateTopUICoroutine());
                }


            }
            catch (Exception ex)
            {
                Logger.Error($"显示顶部UI失败 - 玩家: {player?.Nickname ?? "null"}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除顶部UI显示
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void RemoveTopUI(Player player)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserId))
                {
                    return;
                }

                var customId = $"top_ui_{player.UserId}";
                HintManager.Instance.RemoveHintByCustomId(player, customId);

                // 从活跃玩家列表中移除
                _activePlayers.TryRemove(player.UserId, out _);


            }
            catch (Exception ex)
            {
                Logger.Error($"移除顶部UI失败 - 玩家: {player?.Nickname ?? "null"}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 构建顶部UI显示文本
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>顶部UI文本</returns>
        private string BuildTopUIText(Player player)
        {
            try
            {
                // 获取玩家显示UID（BASE58编码）
                var displayUID = player.GetDisplayUID();

                // 获取时间问候语
                var greeting = GetTimeGreeting();

                // 获取玩家名称
                var playerName = player.Nickname ?? "Unknown";

                // 获取插件版本
                var pluginVersion = GetPluginVersion();

                // 获取等级显示文本
                string levelDisplay;
                if (player.DoNotTrack)
                {
                    levelDisplay = "<color=#FF6B6B>DNT模式</color>";
                }
                else
                {
                    var level = player.GetLVL();
                    levelDisplay = $"<color=#FFD700>Lv.{level}</color>";
                }

                // 构建顶部UI文本：等级/DNT | UID | 问候语 | 玩家名称 | 插件版本
                return $"{levelDisplay} | <color=#87CEEB>{displayUID}</color> | <color=#00FF00>{greeting}</color> | <color=#FFFF00>{playerName}</color> | <color=#FF6600>MhPL v{pluginVersion}</color>";
            }
            catch (Exception ex)
            {
                Logger.Error($"构建顶部UI文本失败: {ex.Message}");
                return "<color=#FF0000>UI加载失败</color>";
            }
        }

        /// <summary>
        /// 根据当前时间获取问候语
        /// </summary>
        /// <returns>时间问候语</returns>
        private string GetTimeGreeting()
        {
            var currentHour = DateTime.Now.Hour;

            return currentHour switch
            {
                >= 6 and < 12 => "上午好",
                >= 12 and < 14 => "中午好",
                >= 14 and < 18 => "下午好",
                _ => "晚上好"
            };
        }

        /// <summary>
        /// 获取玩家UID（使用UserId哈希值作为简单替代）
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>玩家UID</returns>
        private int GetPlayerUID(Player player)
        {
            try
            {
                if (string.IsNullOrEmpty(player.UserId))
                    return 0;

                // 使用UserId的哈希值作为UID（确保为正数）
                var hash = player.UserId.GetHashCode();
                return Math.Abs(hash) % 999999 + 100000; // 确保是6位数的正数
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家UID失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取插件版本
        /// </summary>
        /// <returns>插件版本字符串</returns>
        private string GetPluginVersion()
        {
            try
            {
                // 从Plugin类获取版本信息
                var pluginType = typeof(Plugin);
                var versionProperty = pluginType.GetProperty("Version");
                if (versionProperty != null)
                {
                    var version = versionProperty.GetValue(null) as System.Version;
                    return version?.ToString() ?? "1.0.0";
                }
                return "1.0.0";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取插件版本失败: {ex.Message}");
                return "Unknown";
            }
        }

        /// <summary>
        /// 清理所有顶部UI显示
        /// </summary>
        public void Cleanup()
        {
            try
            {
                Logger.Debug("开始清理顶部UI显示系统...");

                // 停止更新协程
                if (_updateCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_updateCoroutine);
                    Logger.Debug("顶部UI更新协程已停止");
                }

                // 清理所有活跃玩家的UI条
                foreach (var kvp in _activePlayers.ToArray())
                {
                    try
                    {
                        var player = kvp.Value;
                        if (player != null)
                        {
                            RemoveTopUI(player);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"清理单个玩家顶部UI失败: {ex.Message}");
                    }
                }

                // 清空活跃玩家列表
                _activePlayers.Clear();

                Logger.Debug("顶部UI显示系统清理完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理顶部UI显示系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 静态清理方法
        /// </summary>
        public static void DisposeInstance()
        {
            try
            {
                Instance.Cleanup();
            }
            catch (Exception ex)
            {
                Logger.Error($"释放TopUIDisplay实例失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新顶部UI显示（用于时间更新）
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void UpdateTopUI(Player player)
        {
            // 直接重新显示即可，会覆盖旧的显示
            ShowTopUI(player);
        }

        /// <summary>
        /// 定时更新协程，每秒更新一次时间
        /// </summary>
        private IEnumerator<float> UpdateTopUICoroutine()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(1f); // 每秒更新一次

                try
                {
                    // 清理无效的玩家
                    var playersToRemove = new List<string>();
                    foreach (var kvp in _activePlayers)
                    {
                        var player = kvp.Value;
                        if (player == null || !player.IsReady || string.IsNullOrEmpty(player.UserId))
                        {
                            playersToRemove.Add(kvp.Key);
                        }
                    }

                    // 移除无效玩家
                    foreach (var userId in playersToRemove)
                    {
                        _activePlayers.TryRemove(userId, out _);
                    }

                    // 如果没有活跃玩家，停止协程
                    if (_activePlayers.Count == 0)
                    {
                        break;
                    }

                    // 为所有活跃玩家更新顶部UI
                    foreach (var kvp in _activePlayers)
                    {
                        var player = kvp.Value;
                    {
                        try
                        {
                            if (player != null && player.IsReady && !string.IsNullOrEmpty(player.UserId))
                            {
                                // 构建新的顶部UI文本
                                string topUIText = BuildTopUIText(player);

                                var config = new HintConfig
                                {
                                    Type = HintType.Custom,
                                    Text = topUIText,
                                    XCoordinate = 0f,
                                    YCoordinate = 1050,
                                    FontSize = 20,
                                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                                    LifecycleType = HintLifecycleType.Persistent,
                                    Priority = BlackRoseServer.Manager.HintPriority.High,
                                    CustomId = $"top_ui_{player.UserId}",
                                    AllowOverlap = true
                                };

                                // 更新显示
                                HintManager.Instance.ShowHint(player, config);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"更新玩家顶部UI失败 - 玩家: {player?.Nickname ?? "null"}, 错误: {ex.Message}");
                        }
                    }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"顶部UI更新协程异常: {ex.Message}");
                }
            }
        }
    }
}
