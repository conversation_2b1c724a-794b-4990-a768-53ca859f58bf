E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\BlackRoseServer.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\BlackRoseServer.pdb
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\0Harmony.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Assembly-CSharp-firstpass.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Assembly-CSharp-Publicized.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Caress.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\CommandSystem.Core.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\HintServiceMeow-NWAPI.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\LiteDB.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Mirror-Publicized.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Mirror.Components.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Mono.Security.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\MySql.Data.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\netstandard.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Newtonsoft.Json.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\NorthwoodLib.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\NWAPIPermissionSystem.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\PluginAPI.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Pooling.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\SCPSLAudioApi.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Unity.TextMeshPro.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\Unity.Timeline.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\UnityEngine.AudioModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\UnityEngine.CoreModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\UnityEngine.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\UnityEngine.PhysicsModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\UnityEngine.TextRenderingModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\YamlDotNet.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\System.Buffers.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\System.Memory.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\obj\Debug\BlackRoseServer.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer\BlackRoseServer\obj\Debug\BlackRoseServer.pdb
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\BlackRoseServer.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\BlackRoseServer.pdb
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\0Harmony.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Assembly-CSharp-firstpass.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Assembly-CSharp-Publicized.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Caress.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\CommandSystem.Core.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\HintServiceMeow-NWAPI.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\LiteDB.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Mirror-Publicized.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Mirror.Components.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Mono.Security.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\MySql.Data.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\netstandard.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Newtonsoft.Json.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\NorthwoodLib.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\NWAPIPermissionSystem.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\PluginAPI.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Pooling.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\SCPSLAudioApi.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Unity.TextMeshPro.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\Unity.Timeline.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\UnityEngine.AudioModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\UnityEngine.CoreModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\UnityEngine.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\UnityEngine.PhysicsModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\UnityEngine.TextRenderingModule.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\YamlDotNet.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\System.Buffers.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\System.Memory.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\obj\Debug\BlackRoseServer.dll
E:\Microsoft Visual Studio Project\NWAPI_BlackRoseServer - 副本\BlackRoseServer\obj\Debug\BlackRoseServer.pdb
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.pdb
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll.config
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.pdb
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Assembly-CSharp-firstpass.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Assembly-CSharp_publicized.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\CommandSystem.Core.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\HintServiceMeow.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\LabApi.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Mirror.Components.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Mirror_publicized.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\NorthwoodLib.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Pooling.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\SCPSLAudioApi.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.CoreModule.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\ZstdSharp.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.AnimationModule.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.ParticleSystemModule.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.SharedInternalsModule.dll
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.pdb
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.pdb
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Downloads\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll.config
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.pdb
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\HintServiceMeow.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\SCPSLAudioApi.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\ZstdSharp.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.ParticleSystemModule.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.AnimationModule.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.SharedInternalsModule.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.pdb
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.pdb
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.pdb
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Newtonsoft.Json.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\NVorbis.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.ValueTuple.dll
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\NVorbis.xml
D:\vsrepos\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.ValueTuple.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll.config
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BlackRoseServer.pdb
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\ZstdSharp.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.ParticleSystemModule.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.AnimationModule.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\NVorbis.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\UnityEngine.SharedInternalsModule.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.ValueTuple.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.pdb
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\0Harmony.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.pdb
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Google.Protobuf.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\MySql.Data.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Buffers.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Memory.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\NVorbis.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\System.ValueTuple.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\obj\Debug\BlackRoseServer.pdb
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\LabApi.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\YamlDotNet.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\LabApi.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\YamlDotNet.xml
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\HintServiceMeow-LabAPI.dll
F:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\AudioApi.dll
f:\Documents\GitHub\BlackRoseServer_Source\BlackRoseServer\bin\Debug\Newtonsoft.Json.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\MahuiServer.dll.config
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\MahuiServer.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\MahuiServer.pdb
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\0Harmony.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\AudioApi.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\Google.Protobuf.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\LabApi.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\SCPSLAudioApi.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Buffers.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Memory.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\YamlDotNet.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\ZstdSharp.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\UnityEngine.ParticleSystemModule.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\UnityEngine.AnimationModule.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\NVorbis.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\UnityEngine.SharedInternalsModule.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.ValueTuple.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\0Harmony.pdb
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\0Harmony.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\BouncyCastle.Cryptography.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\Google.Protobuf.pdb
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\Google.Protobuf.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Compression.LZ4.Streams.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\K4os.Hash.xxHash.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\LabApi.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Buffers.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Configuration.ConfigurationManager.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Diagnostics.DiagnosticSource.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.IO.Pipelines.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Memory.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Numerics.Vectors.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.Threading.Tasks.Extensions.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\YamlDotNet.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\NVorbis.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\System.ValueTuple.xml
J:\vsrepos\Mahui_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.AssemblyReference.cache
J:\vsrepos\Mahui_Source\BlackRoseServer\obj\Debug\BlackRoseServer.csproj.CoreCompileInputs.cache
J:\vsrepos\Mahui_Source\BlackRoseServer\obj\Debug\BlackRos.543CC49F.Up2Date
J:\vsrepos\Mahui_Source\BlackRoseServer\obj\Debug\MahuiServer.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\obj\Debug\MahuiServer.pdb
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\LiteDB.dll
J:\vsrepos\Mahui_Source\BlackRoseServer\bin\Debug\LiteDB.xml
