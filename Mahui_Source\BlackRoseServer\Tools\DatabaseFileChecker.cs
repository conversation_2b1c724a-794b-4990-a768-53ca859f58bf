using System;
using System.IO;
using System.Text;
using System.Linq;
using LabApi.Features.Console;
using LiteDB;

namespace BlackRoseServer.Tools
{
    /// <summary>
    /// 数据库文件检查工具
    /// </summary>
    public static class DatabaseFileChecker
    {
        /// <summary>
        /// 检查数据库文件状态
        /// </summary>
        /// <param name="filePath">数据库文件路径</param>
        public static void CheckDatabaseFile(string filePath = null)
        {
            try
            {
                // 使用配置中的路径或指定路径
                var dbPath = filePath ?? Config.Instance?.DatabaseConnectionString ?? @"D:\kldhsh123\MahuiServer\data.db";
                
                Logger.Info("=== 数据库文件检查 ===");
                Logger.Info($"检查文件: {dbPath}");
                
                // 1. 检查文件是否存在
                CheckFileExists(dbPath);
                
                // 2. 检查文件基本信息
                CheckFileInfo(dbPath);
                
                // 3. 检查文件头部信息
                CheckFileHeader(dbPath);
                
                // 4. 尝试使用LiteDB打开
                CheckLiteDBConnection(dbPath);
                
                // 5. 检查数据库内容
                CheckDatabaseContent(dbPath);
                
                Logger.Info("=== 检查完成 ===");
            }
            catch (Exception ex)
            {
                Logger.Error($"数据库文件检查失败: {ex.Message}");
                Logger.Debug($"详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        private static void CheckFileExists(string filePath)
        {
            Logger.Info("--- 文件存在性检查 ---");
            
            if (File.Exists(filePath))
            {
                Logger.Info($"✅ 文件存在: {filePath}");
            }
            else
            {
                Logger.Error($"❌ 文件不存在: {filePath}");
                
                // 检查目录是否存在
                var directory = Path.GetDirectoryName(filePath);
                if (Directory.Exists(directory))
                {
                    Logger.Info($"✅ 目录存在: {directory}");
                    
                    // 列出目录中的文件
                    var files = Directory.GetFiles(directory, "*.db");
                    if (files.Length > 0)
                    {
                        Logger.Info("目录中的.db文件:");
                        foreach (var file in files)
                        {
                            Logger.Info($"  - {Path.GetFileName(file)}");
                        }
                    }
                    else
                    {
                        Logger.Info("目录中没有.db文件");
                    }
                }
                else
                {
                    Logger.Error($"❌ 目录不存在: {directory}");
                }
                return;
            }
        }
        
        /// <summary>
        /// 检查文件基本信息
        /// </summary>
        private static void CheckFileInfo(string filePath)
        {
            Logger.Info("--- 文件信息检查 ---");
            
            try
            {
                var fileInfo = new FileInfo(filePath);
                Logger.Info($"✅ 文件大小: {fileInfo.Length} 字节 ({fileInfo.Length / 1024.0:F2} KB)");
                Logger.Info($"✅ 创建时间: {fileInfo.CreationTime}");
                Logger.Info($"✅ 修改时间: {fileInfo.LastWriteTime}");
                Logger.Info($"✅ 访问时间: {fileInfo.LastAccessTime}");
                Logger.Info($"✅ 只读属性: {fileInfo.IsReadOnly}");
                
                // 检查文件大小
                if (fileInfo.Length == 0)
                {
                    Logger.Warn("⚠️ 文件大小为0，可能是空文件");
                }
                else if (fileInfo.Length < 1024)
                {
                    Logger.Warn("⚠️ 文件大小很小，可能不是有效的数据库文件");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 获取文件信息失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查文件头部信息
        /// </summary>
        private static void CheckFileHeader(string filePath)
        {
            Logger.Info("--- 文件头部检查 ---");
            
            try
            {
                using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    if (fs.Length < 16)
                    {
                        Logger.Warn("⚠️ 文件太小，无法读取头部信息");
                        return;
                    }
                    
                    var header = new byte[16];
                    fs.Read(header, 0, 16);
                    
                    // 显示头部字节
                    var headerHex = BitConverter.ToString(header).Replace("-", " ");
                    Logger.Info($"文件头部 (16字节): {headerHex}");
                    
                    // 尝试解析为文本
                    var headerText = Encoding.ASCII.GetString(header).Replace('\0', '.');
                    Logger.Info($"文件头部 (文本): {headerText}");
                    
                    // 检查是否是LiteDB文件
                    // LiteDB 5.x 的文件头通常以特定字节开始
                    if (header[0] == 0x4C && header[1] == 0x69 && header[2] == 0x74 && header[3] == 0x65) // "Lite"
                    {
                        Logger.Info("✅ 检测到LiteDB文件头标识");
                    }
                    else
                    {
                        Logger.Warn("⚠️ 未检测到标准LiteDB文件头，可能不是LiteDB文件或版本不同");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 读取文件头部失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查LiteDB连接
        /// </summary>
        private static void CheckLiteDBConnection(string filePath)
        {
            Logger.Info("--- LiteDB连接检查 ---");
            
            try
            {
                // 尝试只读模式打开数据库
                var connectionString = $"Filename={filePath};ReadOnly=true";
                using (var db = new LiteDatabase(connectionString))
                {
                    Logger.Info("✅ LiteDB连接成功");
                    
                    // 获取集合列表
                    var collections = db.GetCollectionNames();
                    Logger.Info($"✅ 数据库集合数量: {collections.Count()}");
                    
                    foreach (var collection in collections)
                    {
                        Logger.Info($"  - 集合: {collection}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ LiteDB连接失败: {ex.Message}");
                Logger.Debug($"连接详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 检查数据库内容
        /// </summary>
        private static void CheckDatabaseContent(string filePath)
        {
            Logger.Info("--- 数据库内容检查 ---");
            
            try
            {
                // 尝试只读模式打开数据库
                var connectionString = $"Filename={filePath};ReadOnly=true";
                using (var db = new LiteDatabase(connectionString))
                {
                    // 检查主要集合
                    CheckCollection(db, "players", "玩家数据");
                    CheckCollection(db, "joinedinfo", "进服信息");
                    CheckCollection(db, "badges", "徽章信息");
                    CheckCollection(db, "experience_records", "经验记录");
                    CheckCollection(db, "permission_records", "权限记录");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 数据库内容检查失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查单个集合
        /// </summary>
        private static void CheckCollection(LiteDatabase db, string collectionName, string description)
        {
            try
            {
                var collection = db.GetCollection(collectionName);
                var count = collection.Count();
                Logger.Info($"✅ {description} ({collectionName}): {count} 条记录");
            }
            catch (Exception ex)
            {
                Logger.Warn($"⚠️ 检查集合 {collectionName} 失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 创建测试数据库文件
        /// </summary>
        public static void CreateTestDatabase(string filePath = null)
        {
            try
            {
                var testPath = filePath ?? Path.Combine(Environment.CurrentDirectory, "test_database.db");
                
                Logger.Info($"创建测试数据库: {testPath}");
                
                using (var db = new LiteDatabase(testPath))
                {
                    var testCollection = db.GetCollection("test");
                    var testDoc = new BsonDocument
                    {
                        ["Id"] = 1,
                        ["Name"] = "Test",
                        ["Time"] = DateTime.Now
                    };
                    testCollection.Insert(testDoc);

                    Logger.Info("✅ 测试数据库创建成功");
                    Logger.Info($"文件路径: {testPath}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"❌ 创建测试数据库失败: {ex.Message}");
            }
        }
    }
}
