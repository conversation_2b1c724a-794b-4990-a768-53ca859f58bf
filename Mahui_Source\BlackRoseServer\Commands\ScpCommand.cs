﻿using CommandSystem;
using LabApi.Features.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utils.NonAllocLINQ;

namespace BlackRoseServer.Commands
{
    [CommandHandler(typeof(ClientCommandHandler))]
    public class ScpCommand : ICommand
    {
        public string Command => "scp";

        public string[] Aliases => [];

        public string Description => "申请替补SCP";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            Player pSender = Player.Get(sender);

            if (pSender is not null && !Plugin.PlayerDataService.DisconnectSCPs.IsEmpty)
            {
                foreach (var value in Plugin.PlayerDataService.WaitingSCPs)
                {
                    if (value.Value.Contains(pSender))
                    {
                        response = "🔄 你已经在SCP补位队列中了，请耐心等待分配";
                        return false;
                    }
                }

                foreach (var value in Plugin.PlayerDataService.WaitingSCPs)
                {
                    value.Value.Add(pSender);
                }

                int queuePosition = Plugin.PlayerDataService.WaitingSCPs.Values.FirstOrDefault()?.Count ?? 0;
                response = $"✅ 成功加入SCP补位队列！当前排队人数: {queuePosition}";
                return true;
            }
            else
            {
                response = "❌ 当前没有可补位的SCP，或者补位条件不满足";
                return false;
            }
        }
    }
}
