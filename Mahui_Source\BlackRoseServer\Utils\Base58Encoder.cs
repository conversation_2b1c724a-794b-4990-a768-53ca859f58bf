using System;
using System.Numerics;
using System.Text;

namespace BlackRoseServer.Utils
{
    /// <summary>
    /// BASE58编码工具类
    /// </summary>
    public static class Base58Encoder
    {
        private const string Base58Alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
        
        /// <summary>
        /// 将整数编码为BASE58字符串
        /// </summary>
        /// <param name="number">要编码的数字</param>
        /// <returns>BASE58编码的字符串</returns>
        public static string Encode(int number)
        {
            if (number <= 0)
                return "1"; // 默认返回"1"作为最小值

            var result = new StringBuilder();
            var num = (BigInteger)number;

            while (num > 0)
            {
                var remainder = (int)(num % 58);
                result.Insert(0, Base58Alphabet[remainder]);
                num /= 58;
            }

            return result.ToString();
        }

        /// <summary>
        /// 将BASE58字符串解码为整数
        /// </summary>
        /// <param name="encoded">BASE58编码的字符串</param>
        /// <returns>解码后的数字</returns>
        public static int Decode(string encoded)
        {
            if (string.IsNullOrEmpty(encoded))
                return 0;

            BigInteger result = 0;
            BigInteger multiplier = 1;

            for (int i = encoded.Length - 1; i >= 0; i--)
            {
                var character = encoded[i];
                var index = Base58Alphabet.IndexOf(character);
                
                if (index == -1)
                    throw new ArgumentException($"Invalid BASE58 character: {character}");

                result += index * multiplier;
                multiplier *= 58;
            }

            return (int)result;
        }

        /// <summary>
        /// 生成带前缀的用户ID
        /// </summary>
        /// <param name="number">数字ID</param>
        /// <param name="prefix">前缀（默认为"U"）</param>
        /// <returns>格式化的用户ID</returns>
        public static string GenerateUserID(int number, string prefix = "U")
        {
            return $"{prefix}{Encode(number)}";
        }

        /// <summary>
        /// 从用户ID中提取数字
        /// </summary>
        /// <param name="userID">用户ID</param>
        /// <param name="prefix">前缀（默认为"U"）</param>
        /// <returns>提取的数字</returns>
        public static int ExtractNumber(string userID, string prefix = "U")
        {
            if (string.IsNullOrEmpty(userID) || !userID.StartsWith(prefix))
                return 0;

            var encoded = userID.Substring(prefix.Length);
            return Decode(encoded);
        }
    }
}
