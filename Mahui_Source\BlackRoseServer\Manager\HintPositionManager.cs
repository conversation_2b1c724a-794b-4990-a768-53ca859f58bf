using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint位置管理器
    /// </summary>
    public class HintPositionManager
    {
        /// <summary>
        /// 层级信息
        /// </summary>
        public class LayerInfo
        {
            public int YCoordinate { get; set; }
            public List<string> ActiveHints { get; set; } = new List<string>();
            public bool AllowOverlap { get; set; } = false;
            public int MaxHints { get; set; } = 1;
        }

        private readonly ConcurrentDictionary<Player, Dictionary<int, LayerInfo>> _playerLayers;
        private readonly object _syncLock = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public HintPositionManager()
        {
            _playerLayers = new ConcurrentDictionary<Player, Dictionary<int, LayerInfo>>();
        }

        /// <summary>
        /// 检查位置是否可用
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="config">Hint配置</param>
        /// <returns>是否可用</returns>
        public bool IsPositionAvailable(Player player, HintConfig config)
        {
            if (player == null || config == null)
                return false;

            try
            {
                lock (_syncLock)
                {
                    if (!_playerLayers.TryGetValue(player, out var playerLayers))
                    {
                        return true; // 玩家没有任何Hint，位置可用
                    }

                    if (!playerLayers.TryGetValue(config.YCoordinate, out var layerInfo))
                    {
                        return true; // 该层级没有Hint，位置可用
                    }

                    // 特殊处理：SCP信息和Y=200-950范围允许重叠（包含SCP-079专用显示）
                    if ((config.YCoordinate == HintLayers.SCPInfo || (config.YCoordinate >= 200 && config.YCoordinate <= 950)) &&
                        config.CustomId != null &&
                        (config.CustomId.Contains("scp_individual") || config.CustomId.Contains("scp_0492_count") || config.CustomId.Contains("scp_teammate") || config.CustomId.Contains("scp079_info") || config.CustomId.Contains("scp079_teammate")))
                    {
                        return true; // SCP信息总是允许显示
                    }

                    // 特殊处理：Y=900位置允许左对齐和右对齐共存
                    if (config.YCoordinate == 900)
                    {
                        return true; // Y=900位置总是允许显示（左右对齐可以共存）
                    }

                    // 特殊处理：Y=1000-1200位置（经验显示区域）允许重叠
                    if (config.YCoordinate >= 1000 && config.YCoordinate <= 1200)
                    {
                        return true; // 经验显示区域总是允许显示
                    }

                    // 如果允许重叠，检查是否超过最大数量
                    if (config.AllowOverlap)
                    {
                        return layerInfo.ActiveHints.Count < layerInfo.MaxHints;
                    }

                    // 不允许重叠，检查是否有活跃的Hint
                    return layerInfo.ActiveHints.Count == 0;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"检查位置可用性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 占用位置
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="config">Hint配置</param>
        /// <param name="hintId">Hint ID</param>
        /// <returns>是否成功占用</returns>
        public bool OccupyPosition(Player player, HintConfig config, string hintId)
        {
            if (player == null || config == null || string.IsNullOrEmpty(hintId))
                return false;

            try
            {
                lock (_syncLock)
                {
                    if (!_playerLayers.ContainsKey(player))
                    {
                        _playerLayers[player] = new Dictionary<int, LayerInfo>();
                    }

                    var playerLayers = _playerLayers[player];

                    if (!playerLayers.ContainsKey(config.YCoordinate))
                    {
                        var maxHints = GetMaxHintsForLayer(config.YCoordinate);
                        playerLayers[config.YCoordinate] = new LayerInfo
                        {
                            YCoordinate = config.YCoordinate,
                            AllowOverlap = config.AllowOverlap,
                            MaxHints = maxHints
                        };
                    }

                    var layerInfo = playerLayers[config.YCoordinate];

                    // 特殊处理：SCP信息总是允许占用位置（包含SCP-079专用显示范围200-950）
                    bool isSCPInfo = (config.YCoordinate == HintLayers.SCPInfo || (config.YCoordinate >= 200 && config.YCoordinate <= 950)) &&
                                     config.CustomId != null &&
                                     (config.CustomId.Contains("scp_individual") || config.CustomId.Contains("scp_0492_count") || config.CustomId.Contains("scp_teammate") || config.CustomId.Contains("scp079_info") || config.CustomId.Contains("scp079_teammate"));

                    // 特殊处理：Y=900位置允许左对齐和右对齐共存
                    bool isY900 = config.YCoordinate == 900;

                    // 特殊处理：Y=1000-1200位置（经验显示区域）允许重叠
                    bool isExperienceArea = config.YCoordinate >= 1000 && config.YCoordinate <= 1200;

                    // 检查是否可以添加
                    if (!isSCPInfo && !isY900 && !isExperienceArea && !config.AllowOverlap && layerInfo.ActiveHints.Count > 0)
                    {
                        return false;
                    }

                    if (!isSCPInfo && !isY900 && !isExperienceArea && config.AllowOverlap && layerInfo.ActiveHints.Count >= layerInfo.MaxHints)
                    {
                        return false;
                    }

                    // 添加到活跃列表
                    layerInfo.ActiveHints.Add(hintId);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"占用位置失败: {ex.Message}");
                Logger.Debug($"OccupyPosition详细错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 释放位置
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="yCoordinate">Y坐标</param>
        /// <param name="hintId">Hint ID</param>
        /// <returns>是否成功释放</returns>
        public bool ReleasePosition(Player player, int yCoordinate, string hintId)
        {
            if (player == null || string.IsNullOrEmpty(hintId))
                return false;

            try
            {
                lock (_syncLock)
                {
                    if (!_playerLayers.TryGetValue(player, out var playerLayers))
                    {
                        return false;
                    }

                    if (!playerLayers.TryGetValue(yCoordinate, out var layerInfo))
                    {
                        return false;
                    }

                    bool removed = layerInfo.ActiveHints.Remove(hintId);
                    
                    // 如果该层级没有活跃Hint了，移除层级信息
                    if (layerInfo.ActiveHints.Count == 0)
                    {
                        playerLayers.Remove(yCoordinate);
                    }

                    // 如果玩家没有任何层级了，移除玩家信息
                    if (playerLayers.Count == 0)
                    {
                        _playerLayers.TryRemove(player, out _);
                    }

                    if (removed)
                    {
                        // Logger.Debug($"位置已释放 - 玩家: {player.Nickname}, Y坐标: {yCoordinate}, Hint ID: {hintId}");
                    }

                    return removed;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"释放位置失败: {ex.Message}");
                Logger.Debug($"ReleasePosition详细错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 释放玩家的所有位置
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>释放的位置数量</returns>
        public int ReleasePlayerPositions(Player player)
        {
            if (player == null)
                return 0;

            try
            {
                lock (_syncLock)
                {
                    if (_playerLayers.TryRemove(player, out var playerLayers))
                    {
                        int totalHints = playerLayers.Values.Sum(layer => layer.ActiveHints.Count);
                        // Logger.Debug($"已释放玩家 {player.Nickname} 的所有位置，共 {totalHints} 个Hint");
                        return totalHints;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"释放玩家位置失败: {ex.Message}");
                Logger.Debug($"ReleasePlayerPositions详细错误: {ex}");
            }

            return 0;
        }

        /// <summary>
        /// 获取玩家在指定层级的活跃Hint数量
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="yCoordinate">Y坐标</param>
        /// <returns>活跃Hint数量</returns>
        public int GetLayerHintCount(Player player, int yCoordinate)
        {
            if (player == null)
                return 0;

            try
            {
                lock (_syncLock)
                {
                    if (_playerLayers.TryGetValue(player, out var playerLayers) &&
                        playerLayers.TryGetValue(yCoordinate, out var layerInfo))
                    {
                        return layerInfo.ActiveHints.Count;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取层级Hint数量失败: {ex.Message}");
            }

            return 0;
        }

        /// <summary>
        /// 获取玩家的所有活跃层级
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>活跃层级列表</returns>
        public List<int> GetPlayerActiveLayers(Player player)
        {
            if (player == null)
                return new List<int>();

            try
            {
                lock (_syncLock)
                {
                    if (_playerLayers.TryGetValue(player, out var playerLayers))
                    {
                        return playerLayers.Keys.ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家活跃层级失败: {ex.Message}");
            }

            return new List<int>();
        }

        /// <summary>
        /// 检查位置冲突
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="config">新的Hint配置</param>
        /// <returns>冲突的Hint ID列表</returns>
        public List<string> CheckPositionConflicts(Player player, HintConfig config)
        {
            var conflicts = new List<string>();

            if (player == null || config == null)
                return conflicts;

            try
            {
                lock (_syncLock)
                {
                    if (_playerLayers.TryGetValue(player, out var playerLayers))
                    {
                        // 检查相同Y坐标的冲突
                        if (playerLayers.TryGetValue(config.YCoordinate, out var layerInfo))
                        {
                            if (!config.AllowOverlap)
                            {
                                conflicts.AddRange(layerInfo.ActiveHints);
                            }
                        }

                        // 检查相邻位置的冲突（可选，根据需要实现）
                        // 这里可以添加更复杂的冲突检测逻辑
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"检查位置冲突失败: {ex.Message}");
            }

            return conflicts;
        }

        /// <summary>
        /// 获取层级的最大Hint数量
        /// </summary>
        /// <param name="yCoordinate">Y坐标</param>
        /// <returns>最大Hint数量</returns>
        private int GetMaxHintsForLayer(int yCoordinate)
        {
            // 根据不同层级设置不同的最大数量
            switch (yCoordinate)
            {
                case HintLayers.Critical:
                    return 1; // 紧急信息只允许一个
                case HintLayers.SCPInfo:
                    return 10; // SCP信息允许多个（支持多个SCP玩家）
                // SCP阵营显示位置范围（55像素间隔，向上增长，最多8个项目）
                case 920: // 第1个SCP (基础位置，Y=900+20)
                case 865: // 第2个SCP (向上55像素)
                case 810: // 第3个SCP (向上110像素)
                case 755: // 第4个SCP (向上165像素)
                case 700: // 第5个SCP (向上220像素)
                case 645: // 第6个SCP (向上275像素)
                case 590: // 第7个SCP (向上330像素)
                case 535: // 第8个SCP (向上385像素)
                    return 10; // SCP阵营显示位置允许多个
                case HintLayers.LevelUp:
                    return 1; // 重要显示只允许一个
                case HintLayers.Interaction: // 值为800，与SCP第3个位置冲突，所以合并处理
                    return 10; // 交互信息和SCP第3个位置都允许多个
                case HintLayers.Notification:
                    return 2; // 通知允许少量重叠
                case HintLayers.ExperienceGain:
                    return 10; // 经验增加显示允许多个重叠
                case HintLayers.ExperienceBar:
                    return 10; // 经验进度条显示允许多个重叠
                default:
                    // SCP-079专用显示范围（200-300）
                    if (yCoordinate >= 200 && yCoordinate <= 300)
                    {
                        return 10; // SCP-079专用显示允许多个
                    }
                    // 经验显示范围（1000-1200）
                    if (yCoordinate >= 1000 && yCoordinate <= 1200)
                    {
                        return 10; // 经验显示区域允许多个
                    }
                    return 1; // 默认只允许一个
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetStatistics()
        {
            try
            {
                lock (_syncLock)
                {
                    int totalPlayers = _playerLayers.Count;
                    int totalLayers = _playerLayers.Values.Sum(layers => layers.Count);
                    int totalHints = _playerLayers.Values.Sum(layers => layers.Values.Sum(layer => layer.ActiveHints.Count));

                    return $"位置管理统计 - 玩家: {totalPlayers}, 活跃层级: {totalLayers}, 总Hint数: {totalHints}";
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取统计信息失败: {ex.Message}");
                return "统计信息获取失败";
            }
        }
    }
}
