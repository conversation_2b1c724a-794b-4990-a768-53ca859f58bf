using System;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Features.Extensions;
using MEC;
using xiaozhoucust.Utils;

namespace xiaozhoucust.Features
{
    public class ScpHealthManager : IDisposable
    {
        public void OnPlayerSpawning(PlayerSpawningEventArgs ev)
        {
            if (!Plugin.Instance.Config.EnableScpHealthAdjustment)
                return;

            if (!ev.Role.RoleTypeId.IsScp())
                return;

            // 延迟设置血量，确保角色完全生成
            Timing.CallDelayed(0.5f, () => SetScpHealth(ev.Player, ev.Role.RoleTypeId));
        }

        private void SetScpHealth(Player player, PlayerRoles.RoleTypeId roleType)
        {
            try
            {
                if (player == null || !player.IsConnected || !player.IsAlive)
                    return;

                var config = Plugin.Instance.Config;
                
                if (config.ScpHealthSettings.TryGetValue(roleType, out float customHealth))
                {
                    // 设置最大血量
                    player.MaxHealth = customHealth;
                    // 设置当前血量
                    player.Health = customHealth;

                    Logger.Info($"已为SCP {roleType} ({player.Nickname}) 设置血量: {customHealth}");
                    Logger.Debug($"玩家 {player.Nickname} 的血量已调整为 {customHealth}", config.EnableDebugLog);
                }
                else
                {
                    Logger.Debug($"未找到SCP {roleType} 的血量配置", config.EnableDebugLog);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家 {player?.Nickname} 设置SCP血量时出错: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // 清理资源（如果需要）
        }
    }
}
