﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.5.1" targetFramework="net48" />
  <package id="Google.Protobuf" version="3.30.0" targetFramework="net48" />
  <package id="HintServiceMeow" version="5.4.1" targetFramework="net48" />
  <package id="K4os.Compression.LZ4" version="1.3.8" targetFramework="net48" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.8" targetFramework="net48" />
  <package id="K4os.Hash.xxHash" version="1.0.8" targetFramework="net48" />
  <package id="Lib.Harmony" version="2.3.6" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net48" />
  <package id="MySql.Data" version="9.4.0" targetFramework="net48" />
  <package id="Northwood.LabAPI" version="1.0.2" targetFramework="net48" />
  <package id="NVorbis" version="0.10.5" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="8.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="5.0.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="YamlDotNet" version="11.0.1" targetFramework="net48" />
  <package id="ZstdSharp.Port" version="0.8.5" targetFramework="net48" />
</packages>