using System.ComponentModel;
using System.Collections.Generic;

namespace xiaozhoucust
{
    /// <summary>
    /// 小周自定义插件配置文件
    /// </summary>
    public class Config
    {
        [Description("是否启用插件")]
        public bool IsEnabled { get; set; } = true;

        [Description("是否启用调试模式")]
        public bool Debug { get; set; } = false;

        // ===== 无限子弹功能配置 =====
        
        [Description("是否启用无限子弹功能")]
        public bool EnableInfiniteAmmo { get; set; } = true;

        [Description("无限子弹刷新间隔（秒）")]
        public float InfiniteAmmoRefreshInterval { get; set; } = 3.0f;

        [Description("9x19mm弹药数量")]
        public int Ammo9x19Count { get; set; } = 180;

        [Description("12号霰弹数量")]
        public int Ammo12gaugeCount { get; set; } = 18;

        [Description(".44马格南弹数量")]
        public int Ammo44calCount { get; set; } = 18;

        [Description("7.62x39mm弹药数量")]
        public int Ammo762x39Count { get; set; } = 180;

        [Description("5.56x45mm弹药数量")]
        public int Ammo556x45Count { get; set; } = 180;

        // ===== 重生计时器功能配置 =====
        
        [Description("是否启用重生计时器")]
        public bool EnableRespawnTimer { get; set; } = true;

        [Description("计时器显示Y坐标")]
        public int RespawnTimerYCoordinate { get; set; } = 105;

        [Description("计时器字体大小")]
        public int RespawnTimerFontSize { get; set; } = 35;

        [Description("计时器之间的间距")]
        public int TimerSpacing { get; set; } = 16;

        [Description("九尾狐重生计时器颜色")]
        public string NtfSpawnColor { get; set; } = "#4169E1";

        [Description("混沌重生计时器颜色")]
        public string ChaosSpawnColor { get; set; } = "#DC143C";

        // ===== SCP血量调整功能配置 =====
        
        [Description("是否启用SCP血量调整")]
        public bool EnableScpHealthAdjustment { get; set; } = true;

        [Description("SCP-173血量")]
        public float Scp173Health { get; set; } = 5000f;

        [Description("SCP-106血量")]
        public float Scp106Health { get; set; } = 5000f;

        [Description("SCP-049血量")]
        public float Scp049Health { get; set; } = 5000f;

        [Description("SCP-049-2血量")]
        public float Scp0492Health { get; set; } = 5000f;

        [Description("SCP-096血量")]
        public float Scp096Health { get; set; } = 5000f;

        [Description("SCP-939血量")]
        public float Scp939Health { get; set; } = 5000f;

        [Description("SCP-079血量（如果适用）")]
        public float Scp079Health { get; set; } = 5000f;

        // ===== 服务器信息显示功能配置 =====
        
        [Description("是否启用服务器信息显示")]
        public bool EnableServerInfoDisplay { get; set; } = true;

        [Description("服务器名称")]
        public string ServerName { get; set; } = "小周的服务器";

        [Description("QQ群号")]
        public string QQGroupNumber { get; set; } = "123456789";

        [Description("服务器信息显示Y坐标")]
        public int ServerInfoYCoordinate { get; set; } = 950;

        [Description("服务器信息字体大小")]
        public int ServerInfoFontSize { get; set; } = 18;

        [Description("服务器信息显示颜色")]
        public string ServerInfoColor { get; set; } = "#00BFFF";

        // ===== HintUI通用配置 =====
        
        [Description("Hint更新速度")]
        public string HintSyncSpeed { get; set; } = "Fast";

        [Description("是否启用Hint位置冲突检测")]
        public bool EnableHintPositionConflictDetection { get; set; } = true;

        [Description("Hint显示对齐方式")]
        public string HintAlignment { get; set; } = "Center";

        // ===== 高级配置 =====
        
        [Description("插件优先级")]
        public int PluginPriority { get; set; } = 100;

        [Description("最大同时显示的Hint数量")]
        public int MaxConcurrentHints { get; set; } = 10;

        [Description("Hint清理间隔（秒）")]
        public float HintCleanupInterval { get; set; } = 30.0f;
    }
}
