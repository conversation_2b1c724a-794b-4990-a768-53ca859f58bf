   .winmd.dll.exe    4J:\vsrepos\xiaozhoucust\xiaozhoucust\packages.configJ:\vsrepos\0Harmony.dll;J:\vsrepos\xiaozhoucust\packages\HintServiceMeow-LabAPI.dllLJ:\vsrepos\xiaozhoucust\packages\Northwood.LabAPI.1.1.0\lib\net48\LabApi.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\Microsoft.CSharp.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\mscorlib.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Net.Http.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\System.Xml.Linq.dllKJ:\vsrepos\xiaozhoucust\packages\YamlDotNet.11.0.1\lib\net45\YamlDotNet.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.8.1,AssemblyFoldersEx}
{RawFileName}/J:\vsrepos\xiaozhoucust\xiaozhoucust\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.8.1,AssemblyFoldersEx}XJ:\vsrepos\xiaozhoucust\xiaozhoucust\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8.1\Facades\.NETFramework,Version=v4.8.1.NET Framework 4.8.1v4.8.1msil
v4.0.30319         