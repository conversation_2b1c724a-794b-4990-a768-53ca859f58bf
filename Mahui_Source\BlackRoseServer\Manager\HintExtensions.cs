using System;
using System.Collections.Generic;
using LabApi.Features.Wrappers;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint管理器扩展方法
    /// </summary>
    public static class HintExtensions
    {


        /// <summary>
        /// 显示升级信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="levelBefore">升级前等级</param>
        /// <param name="levelAfter">升级后等级</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowLevelUp(this Player player, int levelBefore, int levelAfter)
        {
            return HintManager.Instance.ShowLevelUp(player, levelBefore, levelAfter);
        }

        /// <summary>
        /// 显示保安下班提示
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowSecurityOffDuty(this Player player)
        {
            return HintManager.Instance.ShowSecurityOffDuty(player);
        }

        /// <summary>
        /// 显示通知
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">通知消息</param>
        /// <param name="color">颜色（可选）</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowNotification(this Player player, string message, string color = "#FFFFFF", float? duration = null)
        {
            return HintManager.Instance.ShowNotification(player, message, color, duration);
        }

        /// <summary>
        /// 显示警告
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">警告消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowWarning(this Player player, string message, float? duration = null)
        {
            return HintManager.Instance.ShowWarning(player, message, duration);
        }

        /// <summary>
        /// 显示错误
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">错误消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowError(this Player player, string message, float? duration = null)
        {
            return HintManager.Instance.ShowError(player, message, duration);
        }

        /// <summary>
        /// 显示成功信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">成功消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowSuccess(this Player player, string message, float? duration = null)
        {
            return HintManager.Instance.ShowSuccess(player, message, duration);
        }

        /// <summary>
        /// 显示SCP-914启动信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">启动信息</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowSCP914Info(this Player player, string message)
        {
            return HintManager.Instance.ShowHint(player, HintType.SCP914Info, message, 5f);
        }

        /// <summary>
        /// 显示电梯交互信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="message">交互信息</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowElevatorInteraction(this Player player, string message)
        {
            return HintManager.Instance.ShowHint(player, HintType.ElevatorInteraction, message, 3f);
        }

        /// <summary>
        /// 显示自定义Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="config">Hint配置</param>
        /// <returns>Hint的唯一ID</returns>
        public static string ShowCustomHint(this Player player, HintConfig config)
        {
            return HintManager.Instance.ShowHint(player, config);
        }

        /// <summary>
        /// 移除玩家的所有Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>移除的Hint数量</returns>
        public static int RemoveAllHints(this Player player)
        {
            return HintManager.Instance.RemovePlayerHints(player);
        }

        /// <summary>
        /// 移除玩家指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>移除的Hint数量</returns>
        public static int RemoveHintsByType(this Player player, HintType hintType)
        {
            return HintManager.Instance.RemoveHintsByType(player, hintType);
        }

        /// <summary>
        /// 检查玩家是否有指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>是否存在</returns>
        public static bool HasHintType(this Player player, HintType hintType)
        {
            return HintManager.Instance.HasHintType(player, hintType);
        }

        /// <summary>
        /// 获取玩家的活跃Hint数量
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>活跃Hint数量</returns>
        public static int GetHintCount(this Player player)
        {
            return HintManager.Instance.GetPlayerHintCount(player);
        }

        /// <summary>
        /// 批量显示通知给多个玩家
        /// </summary>
        /// <param name="players">玩家列表</param>
        /// <param name="message">通知消息</param>
        /// <param name="color">颜色（可选）</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>显示的Hint ID列表</returns>
        public static List<string> ShowNotificationToAll(this IEnumerable<Player> players, string message, string color = "#FFFFFF", float? duration = null)
        {
            var hintIds = new List<string>();
            foreach (var player in players)
            {
                if (player != null)
                {
                    string hintId = player.ShowNotification(message, color, duration);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            return hintIds;
        }

        /// <summary>
        /// 批量显示警告给多个玩家
        /// </summary>
        /// <param name="players">玩家列表</param>
        /// <param name="message">警告消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>显示的Hint ID列表</returns>
        public static List<string> ShowWarningToAll(this IEnumerable<Player> players, string message, float? duration = null)
        {
            var hintIds = new List<string>();
            foreach (var player in players)
            {
                if (player != null)
                {
                    string hintId = player.ShowWarning(message, duration);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            return hintIds;
        }

        /// <summary>
        /// 批量显示成功信息给多个玩家
        /// </summary>
        /// <param name="players">玩家列表</param>
        /// <param name="message">成功消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>显示的Hint ID列表</returns>
        public static List<string> ShowSuccessToAll(this IEnumerable<Player> players, string message, float? duration = null)
        {
            var hintIds = new List<string>();
            foreach (var player in players)
            {
                if (player != null)
                {
                    string hintId = player.ShowSuccess(message, duration);
                    if (!string.IsNullOrEmpty(hintId))
                    {
                        hintIds.Add(hintId);
                    }
                }
            }
            return hintIds;
        }

        /// <summary>
        /// 创建快速通知配置
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="yCoordinate">Y坐标（可选）</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <param name="color">颜色（可选）</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateQuickNotification(string message, int? yCoordinate = null, float? duration = null, string color = "#FFFFFF")
        {
            var config = HintTemplate.GetTemplate(HintType.Notification);
            config.Text = $"<color={color}>{message}</color>";
            
            if (yCoordinate.HasValue)
            {
                config.YCoordinate = yCoordinate.Value;
            }
            
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
            }

            return config;
        }

        /// <summary>
        /// 创建自定义位置的Hint配置
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="yCoordinate">Y坐标</param>
        /// <param name="fontSize">字体大小（可选）</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <param name="allowOverlap">是否允许重叠（可选）</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateCustomPositionHint(string message, int yCoordinate, int? fontSize = null, float? duration = null, bool? allowOverlap = null)
        {
            var config = HintTemplate.GetTemplate(HintType.Custom);
            config.Text = message;
            config.YCoordinate = yCoordinate;
            
            if (fontSize.HasValue)
            {
                config.FontSize = fontSize.Value;
            }
            
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
                config.LifecycleType = HintLifecycleType.Temporary;
            }
            
            if (allowOverlap.HasValue)
            {
                config.AllowOverlap = allowOverlap.Value;
            }

            return config;
        }
    }
}
