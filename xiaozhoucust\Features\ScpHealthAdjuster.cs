using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using MEC;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// SCP血量调整功能实现
    /// 基于Mahui_Source项目的XHelper.healthDict实现
    /// </summary>
    public class ScpHealthAdjuster : IDisposable
    {
        private readonly Config _config;
        private readonly Dictionary<PlayerRoles.RoleTypeId, float> _scpHealthDict;

        public ScpHealthAdjuster(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _scpHealthDict = InitializeHealthDictionary();
        }

        /// <summary>
        /// 初始化SCP血量字典
        /// </summary>
        private Dictionary<PlayerRoles.RoleTypeId, float> InitializeHealthDictionary()
        {
            return new Dictionary<PlayerRoles.RoleTypeId, float>
            {
                [PlayerRoles.RoleTypeId.Scp173] = _config.Scp173Health,
                [PlayerRoles.RoleTypeId.Scp106] = _config.Scp106Health,
                [PlayerRoles.RoleTypeId.Scp049] = _config.Scp049Health,
                [PlayerRoles.RoleTypeId.Scp0492] = _config.Scp0492Health,
                [PlayerRoles.RoleTypeId.Scp096] = _config.Scp096Health,
                [PlayerRoles.RoleTypeId.Scp939] = _config.Scp939Health,
                [PlayerRoles.RoleTypeId.Scp3114] = _config.Scp3114Health
            };
        }

        /// <summary>
        /// 调整SCP血量
        /// </summary>
        public void AdjustScpHealth(Player player, PlayerRoles.RoleTypeId role)
        {
            try
            {
                if (player == null)
                {
                    Logger.Warn("尝试调整空玩家的SCP血量");
                    return;
                }

                if (!_config.EnableScpHealthAdjustment)
                {
                    if (_config.Debug)
                    {
                        Logger.Debug("SCP血量调整功能已禁用");
                    }
                    return;
                }

                if (!IsSCPRole(role))
                {
                    if (_config.Debug)
                    {
                        Logger.Debug($"角色 {role} 不是SCP，跳过血量调整");
                    }
                    return;
                }

                if (!_scpHealthDict.TryGetValue(role, out float targetHealth))
                {
                    Logger.Warn($"未找到角色 {role} 的血量配置");
                    return;
                }

                // 延迟0.5秒后设置血量，确保角色完全生成
                Timing.CallDelayed(0.5f, () =>
                {
                    try
                    {
                        if (player == null || !player.IsAlive || player.Role != role)
                        {
                            if (_config.Debug)
                            {
                                Logger.Debug($"玩家状态已变更，跳过血量调整");
                            }
                            return;
                        }

                        // 设置玩家血量
                        player.Health = targetHealth;

                        Logger.Info($"已将玩家 {player.Nickname} 的 {GetScpName(role)} 血量调整为 {targetHealth}");

                        if (_config.Debug)
                        {
                            Logger.Debug($"SCP血量调整详情 - 玩家: {player.Nickname}, 角色: {role}, 血量: {targetHealth}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"延迟设置SCP血量失败: {ex.Message}");
                        if (_config.Debug)
                        {
                            Logger.Debug($"详细错误信息: {ex}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"调整SCP血量失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 批量调整所有SCP玩家的血量
        /// </summary>
        public void AdjustAllScpHealth()
        {
            try
            {
                if (!_config.EnableScpHealthAdjustment)
                {
                    return;
                }

                var scpPlayers = Player.List?.Where(p => p != null && p.IsAlive && IsSCPRole(p.Role))?.ToList();

                if (scpPlayers != null && scpPlayers.Count > 0)
                {
                    Logger.Info($"开始批量调整 {scpPlayers.Count} 个SCP的血量");

                    foreach (var player in scpPlayers)
                    {
                        if (player != null)
                        {
                            AdjustScpHealth(player, player.Role);
                        }
                    }
                }
                else
                {
                    Logger.Debug("当前没有SCP玩家需要调整血量");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"批量调整SCP血量失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 获取指定SCP角色的配置血量
        /// </summary>
        public float GetScpHealth(PlayerRoles.RoleTypeId role)
        {
            return _scpHealthDict.TryGetValue(role, out float health) ? health : 0f;
        }

        /// <summary>
        /// 设置指定SCP角色的血量配置
        /// </summary>
        public void SetScpHealth(PlayerRoles.RoleTypeId role, float health)
        {
            if (IsSCPRole(role) && health > 0)
            {
                _scpHealthDict[role] = health;
                Logger.Info($"已更新 {GetScpName(role)} 的血量配置为 {health}");
            }
        }

        /// <summary>
        /// 获取所有SCP血量配置
        /// </summary>
        public Dictionary<PlayerRoles.RoleTypeId, float> GetAllScpHealthConfigs()
        {
            return new Dictionary<PlayerRoles.RoleTypeId, float>(_scpHealthDict);
        }

        /// <summary>
        /// 重新加载血量配置
        /// </summary>
        public void ReloadHealthConfigs()
        {
            try
            {
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp173] = _config.Scp173Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp106] = _config.Scp106Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp049] = _config.Scp049Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp0492] = _config.Scp0492Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp096] = _config.Scp096Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp939] = _config.Scp939Health;
                _scpHealthDict[PlayerRoles.RoleTypeId.Scp3114] = _config.Scp3114Health;

                Logger.Info("SCP血量配置已重新加载");
            }
            catch (Exception ex)
            {
                Logger.Error($"重新加载SCP血量配置失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 检查角色是否为SCP
        /// </summary>
        private bool IsSCPRole(PlayerRoles.RoleTypeId role)
        {
            return role switch
            {
                PlayerRoles.RoleTypeId.Scp173 => true,
                PlayerRoles.RoleTypeId.Scp106 => true,
                PlayerRoles.RoleTypeId.Scp049 => true,
                PlayerRoles.RoleTypeId.Scp0492 => true,
                PlayerRoles.RoleTypeId.Scp096 => true,
                PlayerRoles.RoleTypeId.Scp939 => true,
                PlayerRoles.RoleTypeId.Scp3114 => true,
                _ => false
            };
        }

        /// <summary>
        /// 获取SCP的中文名称
        /// </summary>
        private string GetScpName(PlayerRoles.RoleTypeId role)
        {
            return role switch
            {
                PlayerRoles.RoleTypeId.Scp173 => "SCP-173",
                PlayerRoles.RoleTypeId.Scp106 => "SCP-106",
                PlayerRoles.RoleTypeId.Scp049 => "SCP-049",
                PlayerRoles.RoleTypeId.Scp0492 => "SCP-049-2",
                PlayerRoles.RoleTypeId.Scp096 => "SCP-096",
                PlayerRoles.RoleTypeId.Scp939 => "SCP-939",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// 获取功能状态信息
        /// </summary>
        public string GetStatusInfo()
        {
            var status = $"SCP血量调整功能状态:\n";
            status += $"- 功能启用: {_config.EnableScpHealthAdjustment}\n";
            status += $"- 配置的SCP数量: {_scpHealthDict.Count}\n";
            
            foreach (var kvp in _scpHealthDict)
            {
                status += $"- {GetScpName(kvp.Key)}: {kvp.Value} HP\n";
            }

            return status;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _scpHealthDict?.Clear();
        }
    }
}
