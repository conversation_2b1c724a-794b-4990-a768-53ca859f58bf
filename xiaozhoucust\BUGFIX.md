# 服务器卡死问题修复说明

## 问题描述
服务器在回合开始后立即卡死，进入空闲模式，无法正常运行。

## 问题原因分析
1. **协程死循环**：协程在while(true)循环开始时立即执行逻辑，可能导致阻塞
2. **空引用异常**：Player.List在回合开始时可能为null，导致异常
3. **回合开始时立即初始化**：在回合刚开始时立即启动所有功能可能导致阻塞

## 修复措施

### 1. 协程结构优化
**修复前**：
```csharp
while (true)
{
    // 立即执行逻辑
    if (Round.IsRoundEnded) yield break;
    // 执行业务逻辑
    yield return Timing.WaitForSeconds(interval);
}
```

**修复后**：
```csharp
while (true)
{
    // 先等待，再执行逻辑
    yield return Timing.WaitForSeconds(interval);
    if (Round.IsRoundEnded) yield break;
    // 执行业务逻辑
}
```

### 2. 空引用保护
**修复前**：
```csharp
var players = Player.List.Where(p => p != null).ToList();
```

**修复后**：
```csharp
var players = Player.List?.Where(p => p != null)?.ToList();
if (players != null && players.Count > 0)
{
    // 处理玩家列表
}
```

### 3. 延迟初始化
**修复前**：
```csharp
public void OnRoundStarting(RoundStartingEventArgs ev)
{
    // 立即启动所有功能
    StartAllFeatures();
}
```

**修复后**：
```csharp
public void OnRoundStarting(RoundStartingEventArgs ev)
{
    // 延迟3秒后启动功能
    Timing.CallDelayed(3f, () => {
        StartAllFeatures();
    });
}
```

## 修复的文件列表

### InfiniteAmmo.cs
- ✅ 协程结构优化：先等待再执行逻辑
- ✅ 添加空引用保护
- ✅ 增强异常处理

### RespawnTimer.cs
- ✅ 协程结构优化
- ✅ 添加空引用保护
- ✅ 移除多余的类型转换

### ScpHealthAdjuster.cs
- ✅ 添加空引用保护
- ✅ 增强玩家列表检查

### ServerInfoDisplay.cs
- ✅ 添加空引用保护
- ✅ 增强玩家列表检查

### EventHandlers.cs
- ✅ 添加MEC引用
- ✅ 实现延迟初始化（3秒延迟）
- ✅ 增强异常处理

## 预期效果
1. **消除卡死问题**：协程不会在启动时立即阻塞主线程
2. **提高稳定性**：空引用保护避免异常崩溃
3. **平滑启动**：延迟初始化确保回合正常开始

## 测试建议
1. 启动服务器，观察回合是否能正常开始
2. 检查日志中是否有"延迟初始化功能"的消息
3. 确认各功能在延迟后正常启动
4. 测试多轮游戏的稳定性

## 注意事项
- 功能启动会有3秒延迟，这是正常的
- 如果仍有问题，可以增加延迟时间到5秒
- 建议在测试环境中先验证修复效果

## 最新修复 (v1.0.2)

### 问题分析
经过进一步分析，发现卡死问题出现在功能模块初始化完成后，具体表现为：
- 功能启动日志正常输出
- 在"所有功能模块初始化完成"后立即卡死
- 服务器进入空闲模式

### 根本原因
1. **协程立即启动导致阻塞**：Timing.RunCoroutine()在启动时可能阻塞主线程
2. **RespawnWaves访问问题**：访问RespawnWaves可能在回合刚开始时不稳定
3. **HintServiceMeow初始化阻塞**：大量玩家同时添加Hint可能导致阻塞

### 最终修复方案

#### 1. 全面延迟启动策略
- **回合开始延迟**：3秒后才开始初始化功能
- **协程启动延迟**：各功能再延迟2-5秒启动协程
- **Hint添加延迟**：延迟5秒后才为玩家添加显示

#### 2. 具体修复内容

**InfiniteAmmo.cs**：
```csharp
// 立即标记为启动，但延迟2秒启动协程
_isRunning = true;
Timing.CallDelayed(2f, () => {
    _infiniteAmmoCoroutine = Timing.RunCoroutine(InfiniteAmmoCoroutine());
});
```

**RespawnTimer.cs**：
```csharp
// 暂时禁用RespawnWaves访问，延迟3秒启动
_isRunning = true;
Timing.CallDelayed(3f, () => {
    // 延迟启动所有功能
});
```

**ServerInfoDisplay.cs**：
```csharp
// 立即标记启动，延迟5秒添加玩家显示
_isRunning = true;
Timing.CallDelayed(5f, () => {
    // 为玩家添加服务器信息显示
});
```

#### 3. 启动时间线
```
回合开始 -> 3秒延迟 -> 功能初始化 -> 2-5秒延迟 -> 协程启动
0s       3s         6s           8-11s
```

## 弹药数量修复 (v1.0.3)

### 问题描述
无限子弹功能使用`ushort.MaxValue`(65535)作为弹药数量，导致：
- 游戏内显示异常
- 弹药数量溢出
- 用户体验不佳

### 修复内容
1. **合理的弹药数量**：
   - 9x19mm帕拉贝鲁姆弹：180发
   - 12号霰弹：18发
   - .44马格南弹：18发
   - 7.62x39mm弹：180发
   - 5.56x45mm弹：180发

2. **可配置的弹药数量**：
   - 在PluginConfig.cs中添加了各种弹药的配置选项
   - 用户可以自定义每种弹药的数量
   - 使用ushort类型确保数值范围合理

3. **代码修复**：
```csharp
// 修复前
player.SetAmmo(ItemType.Ammo9x19, ushort.MaxValue);

// 修复后
player.SetAmmo(ItemType.Ammo9x19, _config.Ammo9x19Count);
```

### 配置示例
```yaml
# 弹药数量配置
ammo9x19_count: 180        # 9x19mm帕拉贝鲁姆弹
ammo12gauge_count: 18      # 12号霰弹
ammo44cal_count: 18        # .44马格南弹
ammo762x39_count: 180      # 7.62x39mm弹
ammo556x45_count: 180      # 5.56x45mm弹
```

## 版本信息
- 修复版本：v1.0.3
- 修复日期：2025-07-25
- 修复内容：
  1. 彻底解决服务器卡死问题，采用分层延迟启动策略
  2. 修复弹药数量溢出问题，使用合理数值并支持配置
