﻿using MEC;
using PlayerRoles.Spectating;
using PlayerRoles;
using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using NorthwoodLib.Pools;
using HintServiceMeow.Core.Utilities;
using HintServiceMeow.Core.Models.Hints;
using BlackRoseServer.Helper.Chat;
using UnityEngine;
using LabApi.Features.Wrappers;

namespace BlackRoseServer.Helper.Players
{
    public sealed class SpectatorHelper
    {
        private static readonly Lazy<SpectatorHelper> _lazyInstance = new(() => new SpectatorHelper());
        public static SpectatorHelper Instance => _lazyInstance.Value;

        public delegate void SpectatorChangedEventHandler(Player target, Player spectator, bool isSpectating);
        public event SpectatorChangedEventHandler OnSpectatorChanged;

        public delegate void SpectatorCountChangedEventHandler(Player target, int previousCount, int currentCount);
        public event SpectatorCountChangedEventHandler OnSpectatorCountChanged;

        private CoroutineHandle _spectatorListCoroutine;
        private readonly ConcurrentDictionary<Player, Hint> _messageSlots = new ConcurrentDictionary<Player, Hint>();
        private readonly ConcurrentDictionary<Player, HashSet<Player>> _spectatorCache = new ConcurrentDictionary<Player, HashSet<Player>>();
        private readonly ConcurrentDictionary<Player, SpectatorStatistics> _spectatorStats = new ConcurrentDictionary<Player, SpectatorStatistics>();
        private readonly ConcurrentDictionary<Player, long> _spectatingStartTimes = new ConcurrentDictionary<Player, long>();
        private readonly Stopwatch _performanceStopwatch = new Stopwatch();
        private float _lastUpdateTime;
        private int _updateCount;
        private float _averageUpdateTime;
        private readonly Queue<float> _recentUpdateTimes = new Queue<float>();
        private readonly int _maxRecentUpdateSamples = 10;
        private readonly object _updateLock = new object();

        public bool IsRunning => _spectatorListCoroutine.IsRunning;
        public float AverageUpdateTime => _averageUpdateTime;
        public int ActivePlayerCount => _messageSlots.Count;
        public int TotalSpectatorRelationships => _spectatorCache.Sum(x => x.Value?.Count ?? 0);
        private SpectatorHelper()
        {

        }

        public void Initialize()
        {
            if (!_spectatorListCoroutine.IsRunning)
            {
                _spectatorListCoroutine = Timing.RunCoroutine(SpectatorListCoroutine());
            }
        }
        public void InitForPlayer(Player player)
        {
            if (player == null) return;

            Initialize();

            var hint = new Hint
            {
                Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                YCoordinate = 20,
                FontSize = 20,
                LineHeight = 5,
                Text = string.Empty
            };

            _messageSlots.TryAdd(player, hint);
            _spectatorCache.TryAdd(player, new HashSet<Player>());
            _spectatorStats.TryAdd(player, new SpectatorStatistics());

            PlayerDisplay.Get(player.ReferenceHub).AddHint(hint);
        }

        public void RemovePlayer(Player player)
        {
            if (player == null) return;

            if (_messageSlots.TryRemove(player, out var hint))
            {
                    PlayerDisplay.Get(player.ReferenceHub)?.RemoveHint(hint);
            }

            _spectatorCache.TryRemove(player, out _);
            _spectatorStats.TryRemove(player, out _);
        }

        public HashSet<Player> GetSpectatorsFor(Player player)
        {
            if (player == null || !_spectatorCache.TryGetValue(player, out var spectators))
            {
                return new HashSet<Player>();
            }

            return new HashSet<Player>(spectators);
        }

        public int GetSpectatorCount(Player player)
        {
            if (player == null || !_spectatorCache.TryGetValue(player, out var spectators))
            {
                return 0;
            }

            return spectators.Count;
        }

        public SpectatorStatistics GetSpectatorStatistics(Player player)
        {
            if (player == null || !_spectatorStats.TryGetValue(player, out var stats))
            {
                return new SpectatorStatistics();
            }

            return stats;
        }

        public void Shutdown()
        {
            foreach (var slot in _messageSlots)
            {
                    PlayerDisplay.Get(slot.Key.ReferenceHub)?.RemoveHint(slot.Value);
            }

            _messageSlots.Clear();
            _spectatorCache.Clear();
            _spectatorStats.Clear();
            _spectatingStartTimes.Clear();
        }

        private IEnumerator<float> SpectatorListCoroutine()
        {
            while (true)
            {
                _performanceStopwatch.Restart();

                    if (Round.IsRoundEnded)
                    {
                        yield break;
                    }

                    UpdateSpectatorRelationships();
                    UpdateSpectatorHints();

                    _performanceStopwatch.Stop();
                    RecordUpdatePerformance((float)_performanceStopwatch.Elapsed.TotalMilliseconds);

                yield return Timing.WaitForSeconds(1f);
            }
        }

        private void UpdateSpectatorRelationships()
        {
            foreach (var cache in _spectatorCache)
            {
                cache.Value.Clear();
            }

            var deadPlayers = XHelper.PlayerList.Where(x => x.Team == Team.Dead).ToList();

            foreach (var deadPlayer in deadPlayers)
            {
                    if (deadPlayer.IsGlobalModerator || deadPlayer.IsOverwatchEnabled || deadPlayer.IsNorthwoodStaff)
                    {
                        continue;
                    }

                    var spectatorRole = (SpectatorRole)deadPlayer.ReferenceHub.roleManager.CurrentRole;
                    var spectatedNetId = spectatorRole.SyncedSpectatedNetId;
                    var targetPlayer = _messageSlots.Keys.FirstOrDefault(p => p.NetworkId == spectatedNetId);
                    if (targetPlayer != null)
                    {
                        if (_spectatorCache.TryGetValue(targetPlayer, out var spectators))
                        {
                            bool wasSpectating = spectators.Contains(deadPlayer);
                            spectators.Add(deadPlayer);

                            if (!wasSpectating)
                            {
                                _spectatingStartTimes[deadPlayer] = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                                OnSpectatorChanged?.Invoke(targetPlayer, deadPlayer, true);
                            }

                            if (_spectatorStats.TryGetValue(targetPlayer, out var stats))
                            {
                                stats.TotalSpectators++;
                                if (!stats.UniqueSpectators.Contains(deadPlayer.UserId))
                                {
                                    stats.UniqueSpectators.Add(deadPlayer.UserId);
                                }
                            }
                        }
                    }
            }

            foreach (var player in _spectatorCache.Keys)
            {
                var currentSpectators = new HashSet<Player>(_spectatorCache[player]);
                var previousSpectators = GetPreviousSpectators(player);

                foreach (var previousSpectator in previousSpectators)
                {
                    if (!currentSpectators.Contains(previousSpectator))
                    {
                        if (_spectatingStartTimes.TryGetValue(previousSpectator, out var startTime))
                        {
                            long endTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            long duration = endTime - startTime;

                            if (_spectatorStats.TryGetValue(player, out var stats))
                            {
                                stats.TotalSpectatingTime += duration;
                                stats.SpectatingSessionCount++;
                            }

                            _spectatingStartTimes.TryRemove(previousSpectator, out _);
                        }

                        OnSpectatorChanged?.Invoke(player, previousSpectator, false);
                    }
                }

                int previousCount = previousSpectators.Count;
                int currentCount = currentSpectators.Count;

                if (previousCount != currentCount)
                {
                    OnSpectatorCountChanged?.Invoke(player, previousCount, currentCount);
                }
            }
        }

        private HashSet<Player> GetPreviousSpectators(Player player)
        {
            if (_spectatorCache.TryGetValue(player, out var spectators))
            {
                return new HashSet<Player>(spectators);
            }
            return [];
        }

        private void UpdateSpectatorHints()
        {
            foreach (var messageSlot in _messageSlots)
            {
                var player = messageSlot.Key;
                var hint = messageSlot.Value;

                if (!player.IsAlive)
                {
                    hint.Text = string.Empty;
                    continue;
                }

                if (_spectatorCache.TryGetValue(player, out var spectators))
                {
                    int count = spectators.Count;

                    if (count > 0)
                    {
                        StringBuilder hintBuilder = StringBuilderPool.Shared.Rent();

                        hintBuilder.Append($"<b>{count}人正在观战你</b>");

                        if (count <= 3)
                        {
                            hintBuilder.Append("\n<size=15>");
                            foreach (var spectator in spectators.Take(3))
                            {
                                hintBuilder.Append($"{spectator.Nickname} ");
                            }
                            hintBuilder.Append("</size>");
                        }
                        else
                        {
                            hintBuilder.Append("\n<size=15>");
                            foreach (var spectator in spectators.Take(2))
                            {
                                hintBuilder.Append($"{spectator.Nickname} ");
                            }
                            hintBuilder.Append($"等{count}人</size>");
                        }

                        hint.Text = StringBuilderPool.Shared.ToStringReturn(hintBuilder);
                    }
                    else
                    {
                        hint.Text = string.Empty;
                    }
                }
            }
        }

        private void RecordUpdatePerformance(float updateTimeMs)
        {
            lock (_updateLock)
            {
                _lastUpdateTime = updateTimeMs;
                _updateCount++;

                _recentUpdateTimes.Enqueue(updateTimeMs);
                while (_recentUpdateTimes.Count > _maxRecentUpdateSamples)
                {
                    _recentUpdateTimes.Dequeue();
                }

                if (_recentUpdateTimes.Count > 0)
                {
                    _averageUpdateTime = _recentUpdateTimes.Average();
                }
            }
        }
    }

    public class SpectatorStatistics
    {
        public int TotalSpectators { get; set; }
        public HashSet<string> UniqueSpectators { get; } = new HashSet<string>();
        public long TotalSpectatingTime { get; set; }
        public int SpectatingSessionCount { get; set; }

        public float AverageSpectatingTime => SpectatingSessionCount > 0 ? (float)TotalSpectatingTime / SpectatingSessionCount : 0;
        public int UniqueSpectatorCount => UniqueSpectators.Count;
    }
}
