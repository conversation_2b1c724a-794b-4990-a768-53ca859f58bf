﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>preview</LangVersion>
        <PlatformTarget>x64</PlatformTarget>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <Platforms>AnyCPU</Platforms>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <CopyLocalLockFileAssemblies>false</CopyLocalLockFileAssemblies>
        <SignAssembly>false</SignAssembly>
        <DebugType>none</DebugType>
        <Configurations>EXILED-HSM;EXILED-RUEI;LabAPI-RUEI;LabAPI-HSM</Configurations>
        <AssemblyName>Timers-$(Configuration)</AssemblyName>
        <OutputPath>bin\</OutputPath>
    </PropertyGroup>
    <PropertyGroup>
        <DefineConstants Condition="$(Configuration.Contains('EXILED'))">$(DefineConstants);EXILED</DefineConstants>
        <DefineConstants Condition="$(Configuration.Contains('LabAPI'))">$(DefineConstants);LabAPI</DefineConstants>
        <DefineConstants Condition="$(Configuration.Contains('RUEI'))">$(DefineConstants);RUEI</DefineConstants>
        <DefineConstants Condition="$(Configuration.Contains('HSM'))">$(DefineConstants);HSM</DefineConstants>
    </PropertyGroup>
    <ItemGroup>
        <Reference Include="Assembly-CSharp-Publicized" HintPath="$(EXILED_REFERENCES)\Assembly-CSharp-Publicized.dll" Private="false" />
        <Reference Include="Assembly-CSharp-firstpass" HintPath="$(EXILED_REFERENCES)\Assembly-CSharp-firstpass.dll" Private="false" />
        <Reference Include="Pooling" HintPath="$(EXILED_REFERENCES)\Pooling.dll" Private="false" />
        <Reference Include="Mirror" HintPath="$(EXILED_REFERENCES)\Mirror.dll" Private="false" />
        <Reference Include="UnityEngine" HintPath="$(EXILED_REFERENCES)\UnityEngine.dll" Private="false" />
        <Reference Include="UnityEngine.CoreModule" HintPath="$(EXILED_REFERENCES)\UnityEngine.CoreModule.dll" Private="false" />
        <Reference Include="UnityEngine.PhysicsModule" HintPath="$(EXILED_REFERENCES)\UnityEngine.PhysicsModule.dll" Private="false" />
        <Reference Include="NorthwoodLib" HintPath="$(EXILED_REFERENCES)\NorthwoodLib.dll" Private="false" />
        <Reference Include="HintServiceMeow-Exiled" Condition="$(Configuration.Contains('EXILED-HSM'))" HintPath="$(EXILED_REFERENCES)\HintServiceMeow-Exiled.dll" Private="false" />
        <Reference Include="HintServiceMeow-LabAPI" Condition="$(Configuration.Contains('LabAPI-HSM'))" HintPath="$(EXILED_REFERENCES)\HintServiceMeow-LabAPI.dll" Private="false" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Condition="$(DefineConstants.Contains('EXILED'))" Include="ExMod.Exiled" Version="9.6.0" />
        <PackageReference Condition="$(DefineConstants.Contains('RUEI'))" Include="RueI" Version="2.1.0" />
        <PackageReference Include="Northwood.LabAPI" Version="1.0.2" />
    </ItemGroup>
</Project>