using System;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 服务器信息显示功能实现
    /// 只在观察者模式显示纯文本服务器信息
    /// </summary>
    public class ServerInfoDisplay : IDisposable
    {
        private readonly Config _config;
        public DynamicHint _serverInfoDisplay;
        private bool _isRunning = false;

        public ServerInfoDisplay(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 启动服务器信息显示
        /// 只在观察者模式显示纯文本
        /// </summary>
        public void StartDisplay()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("服务器信息显示已在运行中");
                    return;
                }

                if (!_config.EnableServerInfoDisplay)
                {
                    Logger.Info("服务器信息显示功能已在配置中禁用");
                    return;
                }

                // 创建DynamicHint，只显示纯文本
                _serverInfoDisplay = new DynamicHint()
                {
                    AutoText = arg => GetServerInfoText(Player.Get(arg.PlayerDisplay.ReferenceHub)),
                    TargetY = _config.ServerInfoYCoordinate,
                    FontSize = _config.ServerInfoFontSize,
                    SyncSpeed = HintSyncSpeed.Fast
                };

                _isRunning = true;

            }
            catch (Exception ex)
            {
                Logger.Error($"启动服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 获取服务器信息文本
        /// 只在观察者模式显示纯文本
        /// </summary>
        private string GetServerInfoText(Player player)
        {
            try
            {
                // 只为观察者显示服务器信息
                if (player == null || !player.IsReady || 
                    (player.Role != PlayerRoles.RoleTypeId.Spectator && player.Role != PlayerRoles.RoleTypeId.Overwatch))
                {
                    return string.Empty;
                }

                // 返回美化的文本，左对齐，白色服务器名称，浅粉色QQ群
                return $"<align=left><color=#FFFFFF><b>{_config.ServerName}</b></color>\n<color=#FFB6C1>QQ群: {_config.QQGroupNumber}</color></align>";
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取服务器信息文本失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 停止服务器信息显示
        /// </summary>
        public void StopDisplay()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                _isRunning = false;
            }
            catch (Exception ex)
            {
                Logger.Error($"停止服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopDisplay();
            }
            catch (Exception ex)
            {
                Logger.Error($"释放服务器信息显示资源失败: {ex.Message}");
            }
        }
    }
}
