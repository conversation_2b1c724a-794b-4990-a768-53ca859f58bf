using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.Core.Extension;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 服务器信息显示功能实现
    /// 在右下角显示群号和服务器名称
    /// </summary>
    public class ServerInfoDisplay : IDisposable
    {
        private readonly Config _config;
        private readonly Dictionary<Player, Hint> _playerDisplays;
        private bool _isRunning = false;

        public ServerInfoDisplay(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _playerDisplays = new Dictionary<Player, Hint>();
        }

        /// <summary>
        /// 启动服务器信息显示
        /// </summary>
        public void StartDisplay()
        {
            try
            {
                if (_isRunning)
                {
                    Logger.Warn("服务器信息显示已在运行中");
                    return;
                }

                if (!_config.EnableServerInfoDisplay)
                {
                    Logger.Info("服务器信息显示功能已在配置中禁用");
                    return;
                }

                // 为所有在线玩家添加服务器信息显示
                var onlinePlayers = Player.List?.Where(p => p != null && p.IsReady)?.ToList();

                if (onlinePlayers != null)
                {
                    foreach (var player in onlinePlayers)
                    {
                        if (player != null)
                        {
                            AddPlayer(player);
                        }
                    }
                }

                _isRunning = true;
                Logger.Info($"服务器信息显示已启动，为 {onlinePlayers.Count} 个玩家显示信息");
            }
            catch (Exception ex)
            {
                Logger.Error($"启动服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 停止服务器信息显示
        /// </summary>
        public void StopDisplay()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                // 清理所有玩家的显示
                foreach (var kvp in _playerDisplays.ToList())
                {
                    RemovePlayer(kvp.Key);
                }

                _isRunning = false;
                Logger.Info("服务器信息显示已停止");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 为玩家添加服务器信息显示
        /// </summary>
        public void AddPlayer(Player player)
        {
            try
            {
                if (player == null || !player.IsReady || _playerDisplays.ContainsKey(player))
                    return;

                if (!_config.EnableServerInfoDisplay)
                    return;

                var serverInfoHint = new Hint
                {
                    Alignment = ParseHintAlignment(_config.HintAlignment),
                    YCoordinate = _config.ServerInfoYCoordinate,
                    FontSize = _config.ServerInfoFontSize,
                    Text = GenerateServerInfoText(),
                    SyncSpeed = ParseHintSyncSpeed(_config.HintSyncSpeed)
                };

                player.AddHint(serverInfoHint);
                _playerDisplays[player] = serverInfoHint;

                if (_config.Debug)
                {
                    Logger.Debug($"为玩家 {player.Nickname} 添加服务器信息显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"为玩家添加服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 移除玩家的服务器信息显示
        /// </summary>
        public void RemovePlayer(Player player)
        {
            try
            {
                if (player == null || !_playerDisplays.ContainsKey(player))
                    return;

                var hint = _playerDisplays[player];
                player.RemoveHint(hint);
                _playerDisplays.Remove(player);

                if (_config.Debug)
                {
                    Logger.Debug($"移除玩家 {player.Nickname} 的服务器信息显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除玩家服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 更新所有玩家的服务器信息显示
        /// </summary>
        public void UpdateAllDisplays()
        {
            try
            {
                if (!_isRunning || !_config.EnableServerInfoDisplay)
                    return;

                var newText = GenerateServerInfoText();

                foreach (var kvp in _playerDisplays.ToList())
                {
                    try
                    {
                        var player = kvp.Key;
                        var hint = kvp.Value;

                        if (player == null || !player.IsReady)
                        {
                            RemovePlayer(player);
                            continue;
                        }

                        // 更新显示文本
                        hint.Text = newText;
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"更新玩家服务器信息显示失败: {ex.Message}");
                    }
                }

                if (_config.Debug)
                {
                    Logger.Debug($"已更新 {_playerDisplays.Count} 个玩家的服务器信息显示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"批量更新服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 生成服务器信息文本
        /// </summary>
        private string GenerateServerInfoText()
        {
            try
            {
                var serverName = string.IsNullOrWhiteSpace(_config.ServerName) ? "未设置服务器名称" : _config.ServerName;
                var qqGroup = string.IsNullOrWhiteSpace(_config.QQGroupNumber) ? "未设置QQ群" : _config.QQGroupNumber;

                return $"<align=right>" +
                       $"<color={_config.ServerInfoColor}>" +
                       $"<size={_config.ServerInfoFontSize}>" +
                       $"🌟 {serverName}\n" +
                       $"📱 QQ群: {qqGroup}" +
                       $"</size>" +
                       $"</color>" +
                       $"</align>";
            }
            catch (Exception ex)
            {
                Logger.Error($"生成服务器信息文本失败: {ex.Message}");
                return "<align=right><color=#00BFFF>服务器信息加载失败</color></align>";
            }
        }

        /// <summary>
        /// 获取当前显示的玩家数量
        /// </summary>
        public int GetDisplayingPlayerCount()
        {
            return _playerDisplays.Count;
        }

        /// <summary>
        /// 获取功能状态信息
        /// </summary>
        public string GetStatusInfo()
        {
            var status = $"服务器信息显示功能状态:\n";
            status += $"- 功能启用: {_config.EnableServerInfoDisplay}\n";
            status += $"- 运行状态: {_isRunning}\n";
            status += $"- 显示玩家数: {_playerDisplays.Count}\n";
            status += $"- 服务器名称: {_config.ServerName}\n";
            status += $"- QQ群号: {_config.QQGroupNumber}\n";
            status += $"- 显示位置: Y={_config.ServerInfoYCoordinate}\n";
            status += $"- 字体大小: {_config.ServerInfoFontSize}\n";
            status += $"- 显示颜色: {_config.ServerInfoColor}";

            return status;
        }

        /// <summary>
        /// 解析Hint对齐方式
        /// </summary>
        private HintAlignment ParseHintAlignment(string alignment)
        {
            return alignment?.ToLower() switch
            {
                "left" => HintAlignment.Left,
                "center" => HintAlignment.Center,
                "right" => HintAlignment.Right,
                _ => HintAlignment.Center
            };
        }

        /// <summary>
        /// 解析Hint同步速度
        /// </summary>
        private HintSyncSpeed ParseHintSyncSpeed(string speed)
        {
            return speed?.ToLower() switch
            {
                "slow" => HintSyncSpeed.Slow,
                "normal" => HintSyncSpeed.Normal,
                "fast" => HintSyncSpeed.Fast,
                _ => HintSyncSpeed.Fast
            };
        }

        /// <summary>
        /// 检查功能是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopDisplay();
        }
    }
}
