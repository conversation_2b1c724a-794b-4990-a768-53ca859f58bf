using System;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;

namespace xiaozhoucust.Features
{
    /// <summary>
    /// 服务器信息显示功能实现
    /// 只在观察者模式显示纯文本服务器信息
    /// </summary>
    public class ServerInfoDisplay : IDisposable
    {
        private readonly Config _config;
        public DynamicHint _serverInfoDisplay;
        private bool _isRunning = false;

        public ServerInfoDisplay(Config config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
        }

        /// <summary>
        /// 启动服务器信息显示
        /// 只在观察者模式显示纯文本
        /// </summary>
        public void StartDisplay()
        {
            // 完全禁用独立的服务器信息显示，因为已经集成到重生计时器中
            return;
        }

        /// <summary>
        /// 获取服务器信息文本
        /// 只在观察者模式显示纯文本
        /// </summary>
        private string GetServerInfoText(Player player)
        {
            // 完全禁用，返回空字符串
            return string.Empty;
        }

        /// <summary>
        /// 停止服务器信息显示
        /// </summary>
        public void StopDisplay()
        {
            try
            {
                if (!_isRunning)
                {
                    return;
                }

                _isRunning = false;
            }
            catch (Exception ex)
            {
                Logger.Error($"停止服务器信息显示失败: {ex.Message}");
                if (_config.Debug)
                {
                    Logger.Debug($"详细错误信息: {ex}");
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopDisplay();
            }
            catch (Exception ex)
            {
                Logger.Error($"释放服务器信息显示资源失败: {ex.Message}");
            }
        }
    }
}
