using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using HintServiceMeow.Core.Extension;

namespace xiaozhoucust.Utils
{
    /// <summary>
    /// Hint辅助工具类
    /// 提供统一的Hint管理和操作方法
    /// </summary>
    public static class HintHelper
    {
        /// <summary>
        /// 为玩家显示临时提示
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="text">提示文本</param>
        /// <param name="duration">显示时长（秒）</param>
        /// <param name="yCoordinate">Y坐标位置</param>
        /// <param name="fontSize">字体大小</param>
        /// <param name="color">文本颜色</param>
        public static void ShowTemporaryHint(Player player, string text, float duration = 5f, 
            int yCoordinate = 400, int fontSize = 20, string color = "#FFFFFF")
        {
            try
            {
                if (player == null || !player.IsReady || string.IsNullOrWhiteSpace(text))
                    return;

                var hint = new Hint
                {
                    Alignment = HintAlignment.Center,
                    YCoordinate = yCoordinate,
                    FontSize = fontSize,
                    Text = $"<color={color}>{text}</color>",
                    SyncSpeed = HintSyncSpeed.Fast
                };

                player.AddHint(hint);

                // 指定时间后自动移除
                Timing.CallDelayed(duration, () =>
                {
                    try
                    {
                        player.RemoveHint(hint);
                    }
                    catch (Exception ex)
                    {
                        Logger.Debug($"移除临时提示失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"显示临时提示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为多个玩家显示临时提示
        /// </summary>
        /// <param name="players">目标玩家列表</param>
        /// <param name="text">提示文本</param>
        /// <param name="duration">显示时长（秒）</param>
        /// <param name="yCoordinate">Y坐标位置</param>
        /// <param name="fontSize">字体大小</param>
        /// <param name="color">文本颜色</param>
        public static void ShowTemporaryHintToMultiple(IEnumerable<Player> players, string text, 
            float duration = 5f, int yCoordinate = 400, int fontSize = 20, string color = "#FFFFFF")
        {
            try
            {
                if (players == null || string.IsNullOrWhiteSpace(text))
                    return;

                foreach (var player in players.Where(p => p != null && p.IsReady))
                {
                    ShowTemporaryHint(player, text, duration, yCoordinate, fontSize, color);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"批量显示临时提示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为玩家显示持久化提示
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="text">提示文本</param>
        /// <param name="yCoordinate">Y坐标位置</param>
        /// <param name="fontSize">字体大小</param>
        /// <param name="alignment">对齐方式</param>
        /// <param name="color">文本颜色</param>
        /// <returns>创建的Hint对象</returns>
        public static Hint ShowPersistentHint(Player player, string text, int yCoordinate = 400, 
            int fontSize = 20, HintAlignment alignment = HintAlignment.Center, string color = "#FFFFFF")
        {
            try
            {
                if (player == null || !player.IsReady || string.IsNullOrWhiteSpace(text))
                    return null;

                var hint = new Hint
                {
                    Alignment = alignment,
                    YCoordinate = yCoordinate,
                    FontSize = fontSize,
                    Text = $"<color={color}>{text}</color>",
                    SyncSpeed = HintSyncSpeed.Fast
                };

                player.AddHint(hint);
                return hint;
            }
            catch (Exception ex)
            {
                Logger.Error($"显示持久化提示失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 为玩家显示动态提示
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="textProvider">文本提供函数</param>
        /// <param name="targetY">目标Y坐标</param>
        /// <param name="fontSize">字体大小</param>
        /// <param name="syncSpeed">同步速度</param>
        /// <returns>创建的DynamicHint对象</returns>
        public static DynamicHint ShowDynamicHint(Player player, Func<string> textProvider, 
            int targetY = 400, int fontSize = 20, HintSyncSpeed syncSpeed = HintSyncSpeed.Fast)
        {
            try
            {
                if (player == null || !player.IsReady || textProvider == null)
                    return null;

                var dynamicHint = new DynamicHint
                {
                    AutoText = _ => textProvider(),
                    TargetY = targetY,
                    FontSize = fontSize,
                    SyncSpeed = syncSpeed
                };

                player.AddHint(dynamicHint);
                return dynamicHint;
            }
            catch (Exception ex)
            {
                Logger.Error($"显示动态提示失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 安全移除玩家的Hint
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="hint">要移除的Hint</param>
        public static void SafeRemoveHint(Player player, Hint hint)
        {
            try
            {
                if (player == null || hint == null)
                    return;

                player.RemoveHint(hint);
            }
            catch (Exception ex)
            {
                Logger.Debug($"安全移除Hint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全移除玩家的DynamicHint
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="hint">要移除的DynamicHint</param>
        public static void SafeRemoveHint(Player player, DynamicHint hint)
        {
            try
            {
                if (player == null || hint == null)
                    return;

                player.RemoveHint(hint);
            }
            catch (Exception ex)
            {
                Logger.Debug($"安全移除DynamicHint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理玩家的所有Hint
        /// </summary>
        /// <param name="player">目标玩家</param>
        public static void ClearAllHints(Player player)
        {
            try
            {
                if (player == null)
                    return;

                // 这里需要根据HintServiceMeow的实际API来实现
                // 由于没有直接的清理所有Hint的方法，这里提供一个基础框架
                Logger.Debug($"清理玩家 {player.Nickname} 的所有Hint");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理玩家Hint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化颜色文本
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="color">颜色代码</param>
        /// <returns>格式化后的文本</returns>
        public static string FormatColorText(string text, string color)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (string.IsNullOrWhiteSpace(color))
                return text;

            return $"<color={color}>{text}</color>";
        }

        /// <summary>
        /// 格式化大小文本
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="size">字体大小</param>
        /// <returns>格式化后的文本</returns>
        public static string FormatSizeText(string text, int size)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (size <= 0)
                return text;

            return $"<size={size}>{text}</size>";
        }

        /// <summary>
        /// 格式化对齐文本
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="alignment">对齐方式</param>
        /// <returns>格式化后的文本</returns>
        public static string FormatAlignText(string text, string alignment)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (string.IsNullOrWhiteSpace(alignment))
                return text;

            return $"<align={alignment}>{text}</align>";
        }

        /// <summary>
        /// 创建复合格式化文本
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="color">颜色代码</param>
        /// <param name="size">字体大小</param>
        /// <param name="alignment">对齐方式</param>
        /// <param name="bold">是否加粗</param>
        /// <returns>格式化后的文本</returns>
        public static string CreateFormattedText(string text, string color = null, int? size = null, 
            string alignment = null, bool bold = false)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            var formattedText = text;

            if (bold)
                formattedText = $"<b>{formattedText}</b>";

            if (!string.IsNullOrWhiteSpace(color))
                formattedText = FormatColorText(formattedText, color);

            if (size.HasValue && size.Value > 0)
                formattedText = FormatSizeText(formattedText, size.Value);

            if (!string.IsNullOrWhiteSpace(alignment))
                formattedText = FormatAlignText(formattedText, alignment);

            return formattedText;
        }

        /// <summary>
        /// 解析HintAlignment枚举
        /// </summary>
        /// <param name="alignment">对齐方式字符串</param>
        /// <returns>HintAlignment枚举值</returns>
        public static HintAlignment ParseAlignment(string alignment)
        {
            return alignment?.ToLower() switch
            {
                "left" => HintAlignment.Left,
                "center" => HintAlignment.Center,
                "right" => HintAlignment.Right,
                _ => HintAlignment.Center
            };
        }

        /// <summary>
        /// 解析HintSyncSpeed枚举
        /// </summary>
        /// <param name="speed">同步速度字符串</param>
        /// <returns>HintSyncSpeed枚举值</returns>
        public static HintSyncSpeed ParseSyncSpeed(string speed)
        {
            return speed?.ToLower() switch
            {
                "slow" => HintSyncSpeed.Slow,
                "normal" => HintSyncSpeed.Normal,
                "fast" => HintSyncSpeed.Fast,
                _ => HintSyncSpeed.Fast
            };
        }
    }
}
