﻿using BlackRoseServer.API.Features.Pool;
using HintServiceMeow.Core.Utilities;
using HintServiceMeow.Core.Extension;
using LabApi.Features.Wrappers;
using MEC;
using PlayerRoles;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Helper.Chat
{
    public class ChatMessage
    {
        public enum MessageType
        {
            AdminPrivateChat,
            BroadcastChat,
            TeamChat,
        }

        public enum Priority
        {
            Low = 0,
            Normal = 1,
            High = 2,
            Critical = 3
        }

        public ChatMessage(Player sender, MessageType type, string message, Priority priority = Priority.Normal)
        {
            TimeSent = DateTime.Now;
            Type = type;
            Message = message;
            SenderName = sender.DisplayName;
            SenderTeam = sender.Team;
            SenderRole = sender.Role;
            MessagePriority = priority;
            MessageId = Guid.NewGuid().ToString();
            ProcessedContent = AnalyzeMessageContent(message);
        }

        private string AnalyzeMessageContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            bool containsUrl = content.Contains("http://") || content.Contains("https://");
            HasUrl = containsUrl;

            bool containsEmoji = content.Any(c => char.IsSymbol(c) || char.IsSurrogate(c));
            HasEmoji = containsEmoji;

            if (content.Length < 10)
                ContentLengthType = "短消息";
            else if (content.Length < 50)
                ContentLengthType = "中等消息";
            else
                ContentLengthType = "长消息";

            return content;
        }

        public DateTime TimeSent { get; }
        public MessageType Type { get; }
        public string Message { get; }
        public string SenderName { get; }
        public Team SenderTeam { get; }
        public RoleTypeId SenderRole { get; }
        public Priority MessagePriority { get; }
        public string MessageId { get; }
        public string ProcessedContent { get; }
        public bool HasUrl { get; private set; }
        public bool HasEmoji { get; private set; }
        public string ContentLengthType { get; private set; }
        public bool ConsoleSent { get; set; } = false;
        public bool IsExpired => (DateTime.Now - TimeSent).TotalSeconds > ChatHelper.Instance.MessageExpirationTime;
    }

    public class ChatHelper
    {
        private static readonly Lazy<ChatHelper> _instance = new(() => new ChatHelper());
        public static ChatHelper Instance => _instance.Value;

        private CoroutineHandle _messageCoroutine;
        private CoroutineHandle _cleanupCoroutine;

        private readonly ConcurrentQueue<ChatMessage> _messageQueue = new();
        private readonly ConcurrentDictionary<string, ChatMessage> _messageCache = new();
        private readonly ConcurrentDictionary<Player, HintServiceMeow.Core.Models.Hints.Hint> _messageSlots = new();
        private readonly ConcurrentDictionary<Player, HashSet<string>> _playerMessageHistory = new();
        private readonly ConcurrentDictionary<Player, DateTime> _lastMessageTime = new();
        private readonly ConcurrentDictionary<string, int> _messageTypeStats = new();
        private readonly ConcurrentDictionary<Team, int> _teamMessageStats = new();
        private readonly object _statsLock = new();
        private readonly Stopwatch _performanceMonitor = new();
        private long _totalProcessingTime = 0;
        private int _processedMessageCount = 0;

        public int MessageExpirationTime { get; set; } = 10;
        public int MaxCachedMessages { get; set; } = 100;
        public int MaxMessagesPerPlayer { get; set; } = 20;
        public int MessageRateLimitSeconds { get; set; } = 1;
        public bool EnableMessageFiltering { get; set; } = true;
        public float AverageProcessingTimeMs => _processedMessageCount > 0 ? (float)_totalProcessingTime / _processedMessageCount : 0;
        public int ActivePlayerCount => _messageSlots.Count;
        public int CachedMessageCount => _messageCache.Count;
        public IReadOnlyDictionary<string, int> MessageTypeStatistics => _messageTypeStats;
        public IReadOnlyDictionary<Team, int> TeamMessageStatistics => _teamMessageStats;

        private readonly ConcurrentDictionary<ChatMessage.MessageType, string> _messageTypeName = new();
        private readonly ConcurrentDictionary<RoleTypeId, string> _chatSystemRoleTranslation = new();

        private ChatHelper()
        {
            InitializeMessageTypeNames();
            InitializeRoleTranslations();
        }

        private void InitializeMessageTypeNames()
        {
            _messageTypeName[ChatMessage.MessageType.AdminPrivateChat] = "管理私聊";
            _messageTypeName[ChatMessage.MessageType.BroadcastChat] = "全体";
            _messageTypeName[ChatMessage.MessageType.TeamChat] = "队友消息";
        }

        private void InitializeRoleTranslations()
        {
            _chatSystemRoleTranslation[RoleTypeId.ClassD] = "D级人员";
            _chatSystemRoleTranslation[RoleTypeId.FacilityGuard] = "保安";
            _chatSystemRoleTranslation[RoleTypeId.ChaosConscript] = "混沌征召兵";
            _chatSystemRoleTranslation[RoleTypeId.ChaosMarauder] = "混沌掠夺者";
            _chatSystemRoleTranslation[RoleTypeId.ChaosRepressor] = "混沌压制者";
            _chatSystemRoleTranslation[RoleTypeId.ChaosRifleman] = "混沌步枪兵";
            _chatSystemRoleTranslation[RoleTypeId.NtfCaptain] = "九尾狐指挥官";
            _chatSystemRoleTranslation[RoleTypeId.NtfPrivate] = "九尾狐列兵";
            _chatSystemRoleTranslation[RoleTypeId.NtfSergeant] = "九尾狐中士";
            _chatSystemRoleTranslation[RoleTypeId.NtfSpecialist] = "九尾狐收容专家";
            _chatSystemRoleTranslation[RoleTypeId.Scientist] = "科学家";
            _chatSystemRoleTranslation[RoleTypeId.Tutorial] = "教程角色";
            _chatSystemRoleTranslation[RoleTypeId.Scp096] = "SCP-096";
            _chatSystemRoleTranslation[RoleTypeId.Scp049] = "SCP-049";
            _chatSystemRoleTranslation[RoleTypeId.Scp173] = "SCP-173";
            _chatSystemRoleTranslation[RoleTypeId.Scp939] = "SCP-939";
            _chatSystemRoleTranslation[RoleTypeId.Scp079] = "SCP-079";
            _chatSystemRoleTranslation[RoleTypeId.Scp0492] = "SCP-049-2";
            _chatSystemRoleTranslation[RoleTypeId.Scp106] = "SCP-106";
            _chatSystemRoleTranslation[RoleTypeId.Scp3114] = "SCP-3114";
            _chatSystemRoleTranslation[RoleTypeId.Spectator] = "观察者";
            _chatSystemRoleTranslation[RoleTypeId.Overwatch] = "监管";
            _chatSystemRoleTranslation[RoleTypeId.Filmmaker] = "导演";
        }

        public void InitForPlayer(Player player)
        {
            if (player == null) return;

            StartCoroutines();

            if (_messageSlots.ContainsKey(player))
            {
                return;
            }

            var hint = new HintServiceMeow.Core.Models.Hints.Hint
            {
                Alignment = HintServiceMeow.Core.Enum.HintAlignment.Left,
                YCoordinate = 280, // 调整位置避免重叠
                FontSize = 22, // 稍微减小字体
                LineHeight = 8 // 增加行间距
            };

            _messageSlots[player] = hint;
            _playerMessageHistory[player] = new HashSet<string>();
            _lastMessageTime[player] = DateTime.MinValue;

            player.AddHint(hint);
        }

        public void SendMessage(Player sender, ChatMessage.MessageType type, string message)
        {
            SendMessage(new ChatMessage(sender, type, message));
        }

        public void SendMessage(Player sender, ChatMessage.MessageType type, string message, ChatMessage.Priority priority)
        {
            SendMessage(new ChatMessage(sender, type, message, priority));
        }

        public void SendMessage(ChatMessage message)
        {
            if (message == null) return;

            if (!CanSendMessage(message))
                return;

            if (EnableMessageFiltering && ShouldFilterMessage(message))
                return;

            _messageQueue.Enqueue(message);
            _messageCache[message.MessageId] = message;

            UpdatePlayerMessageHistory(message);

            UpdateMessageStatistics(message);

            // 发送控制台副本（只发送一次）
            SendConsoleMessageToAllRecipients(message);

            CleanupExpiredMessages();
        }

        public void RemovePlayerMessages(Player player)
        {
            if (player == null) return;
            _messageSlots.TryRemove(player, out _);
            _playerMessageHistory.TryRemove(player, out _);
            _lastMessageTime.TryRemove(player, out _);
        }

        public void ClearAllMessages()
        {
            _messageCache.Clear();
            while (_messageQueue.TryDequeue(out _)) { }

            foreach (var slot in _messageSlots)
            {
                slot.Value.Text = string.Empty;
            }
        }

        public List<ChatMessage> GetPlayerMessages(Player player, int count = 10)
        {
            if (player == null || !_playerMessageHistory.TryGetValue(player, out var messageIds))
                return new List<ChatMessage>();

            return messageIds
                .Select(id => _messageCache.TryGetValue(id, out var msg) ? msg : null)
                .Where(msg => msg != null && !msg.IsExpired)
                .OrderByDescending(msg => msg.TimeSent)
                .Take(count)
                .ToList();
        }

        public Dictionary<ChatMessage.MessageType, string> GetMessageTypeNames()
        {
            return _messageTypeName.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        public Dictionary<RoleTypeId, string> GetRoleTranslations()
        {
            return _chatSystemRoleTranslation.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        private void StartCoroutines()
        {
            if (!_messageCoroutine.IsRunning)
                _messageCoroutine = Timing.RunCoroutine(MessageCoroutineMethod());

            if (!_cleanupCoroutine.IsRunning)
                _cleanupCoroutine = Timing.RunCoroutine(CleanupCoroutineMethod());
        }

        private bool CanSendMessage(ChatMessage message)
        {
            var senderName = message.SenderName;
            var matchingPlayers = Player.List.Where(p => p.DisplayName == senderName).ToList();
            if (!matchingPlayers.Any())
                return false;

            var sender = matchingPlayers.First();

            if (_lastMessageTime.TryGetValue(sender, out var lastTime))
            {
                if ((DateTime.Now - lastTime).TotalSeconds < MessageRateLimitSeconds)
                    return false;
            }

            _lastMessageTime[sender] = DateTime.Now;
            return true;
        }

        private bool ShouldFilterMessage(ChatMessage message)
        {
            if (string.IsNullOrWhiteSpace(message.Message))
                return true;

            if (message.Message.Length > 200)
                return true;

            return false;
        }

        private void UpdatePlayerMessageHistory(ChatMessage message)
        {
            var senderName = message.SenderName;
            var matchingPlayers = Player.List.Where(p => p.DisplayName == senderName).ToList();
            if (!matchingPlayers.Any())
                return;

            var sender = matchingPlayers.First();

            if (_playerMessageHistory.TryGetValue(sender, out var history))
            {
                if (history.Count >= MaxMessagesPerPlayer)
                {
                    var oldestMessage = history.FirstOrDefault();
                    if (oldestMessage != null)
                        history.Remove(oldestMessage);
                }

                history.Add(message.MessageId);
            }
        }

        private void UpdateMessageStatistics(ChatMessage message)
        {
            lock (_statsLock)
            {
                var typeKey = message.Type.ToString();
                _messageTypeStats.AddOrUpdate(typeKey, 1, (_, count) => count + 1);

                _teamMessageStats.AddOrUpdate(message.SenderTeam, 1, (_, count) => count + 1);
            }
        }

        private void CleanupExpiredMessages()
        {
            if (_messageCache.Count > MaxCachedMessages)
            {
                var expiredMessages = _messageCache.Values
                    .OrderBy(m => m.TimeSent)
                    .Take(_messageCache.Count - MaxCachedMessages)
                    .ToList();

                foreach (var msg in expiredMessages)
                {
                    _messageCache.TryRemove(msg.MessageId, out _);
                }
            }
        }

        private bool HaveAccess(Player player, ChatMessage message)
        {
            if (message.IsExpired)
                return false;

            return message.Type switch
            {
                ChatMessage.MessageType.AdminPrivateChat => player.RemoteAdminAccess,
                ChatMessage.MessageType.BroadcastChat => true,
                ChatMessage.MessageType.TeamChat => player.Team == message.SenderTeam,
                _ => false,
            };
        }

        private IEnumerator<float> MessageCoroutineMethod()
        {
            while (true)
            {
                _performanceMonitor.Restart();

                var sb = StringBuilderPool.Pool.Get();

                foreach (var messageSlot in _messageSlots)
                {
                    var player = messageSlot.Key;
                    var hint = messageSlot.Value;

                    var visibleMessages = _messageCache.Values
                        .Where(msg => HaveAccess(player, msg))
                        .OrderByDescending(msg => msg.MessagePriority)
                        .ThenByDescending(msg => msg.TimeSent)
                        .ToList();

                    if (!visibleMessages.Any())
                    {
                        hint.Text = string.Empty;
                        continue;
                    }

                    sb.AppendLine("<color=#87CEEB><size=20>📢 消息中心</size></color>");

                    foreach (var message in visibleMessages)
                    {
                        string messageStr = FormatMessage(message);
                        sb.AppendLine($"  {messageStr}");
                    }

                    hint.Text = $"<b>{sb}</b>";

                    sb.Clear();
                }

                StringBuilderPool.Pool.Return(sb);

                _performanceMonitor.Stop();
                _totalProcessingTime += _performanceMonitor.ElapsedMilliseconds;
                _processedMessageCount++;

                yield return Timing.WaitForSeconds(0.5f);
            }
        }

        private IEnumerator<float> CleanupCoroutineMethod()
        {
            while (true)
            {
                var expiredMessages = _messageCache.Values.Where(m => m.IsExpired).ToList();
                foreach (var msg in expiredMessages)
                {
                    _messageCache.TryRemove(msg.MessageId, out _);
                }

                yield return Timing.WaitForSeconds(5f);
            }
        }

        private string FormatMessage(ChatMessage message)
        {
            string teamColor = GetTeamColor(message.SenderTeam);
            string messageTypeColor = message.Type == ChatMessage.MessageType.AdminPrivateChat ? "#FF6B6B" : teamColor;
            string priorityIndicator = GetPriorityIndicator(message.MessagePriority);
            string countDown = (MessageExpirationTime - (int)(DateTime.Now - message.TimeSent).TotalSeconds).ToString();

            // 保持noparse标签的完整性，不在其周围添加额外的HTML标签
            string formattedMessage = message.Message;

            // 美化消息格式
            string roleText = GetRoleTranslation(message.SenderRole);
            string typeText = GetMessageTypeName(message.Type);

            return $"{priorityIndicator}<color=#CCCCCC>[{countDown}s]</color> <color={teamColor}>{roleText}</color> <color={messageTypeColor}>[{typeText}]</color> <color=#FFFFFF>{message.SenderName}</color>: <color=#F0F8FF>{formattedMessage}</color>";
        }

        private string GetTeamColor(Team team)
        {
            return team switch
            {
                Team.SCPs => "red",
                Team.ChaosInsurgency => "green",
                Team.Scientists => "yellow",
                Team.ClassD => "orange",
                Team.Dead => "white",
                Team.FoundationForces => "#4EFAFF",
                _ => "white"
            };
        }

        private string GetPriorityIndicator(ChatMessage.Priority priority)
        {
            return priority switch
            {
                ChatMessage.Priority.Critical => "<color=red>!</color>",
                ChatMessage.Priority.High => "<color=yellow>*</color>",
                _ => ""
            };
        }

        private string GetMessageTypeName(ChatMessage.MessageType type)
        {
            return _messageTypeName.TryGetValue(type, out var name) ? name : type.ToString();
        }

        private string GetRoleTranslation(RoleTypeId role)
        {
            return _chatSystemRoleTranslation.TryGetValue(role, out var translation) ? translation : role.ToString();
        }

        /// <summary>
        /// 向所有有权限接收消息的玩家控制台发送消息副本
        /// </summary>
        /// <param name="message">聊天消息</param>
        private void SendConsoleMessageToAllRecipients(ChatMessage message)
        {
            try
            {
                if (message == null) return;

                string consoleMessage = FormatConsoleMessage(message);

                // 向所有有权限接收此消息的玩家发送控制台副本
                foreach (var player in Player.List.Where(p => p != null && HaveAccess(p, message)))
                {
                    player.SendConsoleMessage(consoleMessage, "cyan");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"发送控制台消息副本失败 - 消息: {message?.Message ?? "Unknown"}, 错误: {ex.Message}");
                Logger.Debug($"SendConsoleMessageToAllRecipients详细错误: {ex}");
            }
        }

        /// <summary>
        /// 格式化控制台消息
        /// </summary>
        /// <param name="message">聊天消息</param>
        /// <returns>格式化后的控制台消息</returns>
        private string FormatConsoleMessage(ChatMessage message)
        {
            string messageTypeName = _messageTypeName.TryGetValue(message.Type, out var typeName) ? typeName : "未知";

            // 控制台消息移除HTML标签，但保留noparse内容
            string cleanMessage = message.Message;
            if (cleanMessage.Contains("<noparse>") && cleanMessage.Contains("</noparse>"))
            {
                // 提取noparse标签内的内容
                var startIndex = cleanMessage.IndexOf("<noparse>") + 9;
                var endIndex = cleanMessage.IndexOf("</noparse>");
                if (startIndex < endIndex)
                {
                    cleanMessage = cleanMessage.Substring(startIndex, endIndex - startIndex);
                }
            }

            return $"[{messageTypeName}] {message.SenderName}: {cleanMessage}";
        }

        public void Reset()
        {
            ClearAllMessages();
            _messageSlots.Clear();
            _playerMessageHistory.Clear();
            _lastMessageTime.Clear();
            _messageTypeStats.Clear();
            _teamMessageStats.Clear();
        }
    }
}
