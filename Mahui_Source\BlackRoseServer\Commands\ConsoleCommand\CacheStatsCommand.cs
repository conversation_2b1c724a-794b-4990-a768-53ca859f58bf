using System;
using CommandSystem;
using LabApi.Features.Console;

namespace BlackRoseServer.Commands.ConsoleCommand
{
    /// <summary>
    /// 缓存统计命令
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class CacheStatsCommand : ICommand
    {
        public string Command => "playercachestats";
        public string[] Aliases => new[] { "pcs", "pcstats" };
        public string Description => "显示玩家数据缓存统计信息";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                var stats = BlackRoseServer.API.PlayerDataCache.Instance.GetCacheStats();
                response = $"玩家数据缓存统计:\n{stats}";
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取缓存统计失败: {ex.Message}");
                response = $"获取缓存统计失败: {ex.Message}";
                return false;
            }
        }
    }

    /// <summary>
    /// 强制刷新缓存命令
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class FlushCacheCommand : ICommand
    {
        public string Command => "flushplayercache";
        public string[] Aliases => new[] { "fpc", "flushpc" };
        public string Description => "强制刷新玩家数据缓存到数据库";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                BlackRoseServer.API.PlayerDataCache.Instance.ForceFlush();
                response = "玩家数据缓存已强制刷新到数据库";
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"强制刷新缓存失败: {ex.Message}");
                response = $"强制刷新缓存失败: {ex.Message}";
                return false;
            }
        }
    }

    /// <summary>
    /// 清空缓存命令
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class ClearCacheCommand : ICommand
    {
        public string Command => "clearplayercache";
        public string[] Aliases => new[] { "cpc", "clearpc" };
        public string Description => "清空所有玩家数据缓存（不写入数据库）";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                var stats = BlackRoseServer.API.PlayerDataCache.Instance.GetCacheStats();
                BlackRoseServer.API.PlayerDataCache.Instance.ClearCache();
                response = $"玩家数据缓存已清空\n清空前统计:\n{stats}";
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"清空缓存失败: {ex.Message}");
                response = $"清空缓存失败: {ex.Message}";
                return false;
            }
        }
    }

    /// <summary>
    /// 清空所有显示系统缓存命令
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class ClearAllCacheCommand : ICommand
    {
        public string Command => "clearallcache";
        public string[] Aliases => new[] { "cac", "clearall" };
        public string Description => "清空所有系统缓存（包括显示系统、权限系统等）";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                var result = new System.Text.StringBuilder();
                result.AppendLine("开始清空所有系统缓存...");

                // 清空玩家数据缓存
                try
                {
                    var stats = BlackRoseServer.API.PlayerDataCache.Instance.GetCacheStats();
                    BlackRoseServer.API.PlayerDataCache.Instance.ClearCache();
                    result.AppendLine($"✓ 玩家数据缓存已清空");
                    result.AppendLine($"  清空前统计: {stats.Split('\n')[0]}"); // 只显示第一行统计
                }
                catch (Exception ex)
                {
                    result.AppendLine($"✗ 清空玩家数据缓存失败: {ex.Message}");
                }

                // 清空显示系统
                try
                {
                    BlackRoseServer.Display.TopUIDisplay.DisposeInstance();
                    result.AppendLine("✓ 顶部UI显示系统已清理");
                }
                catch (Exception ex)
                {
                    result.AppendLine($"✗ 清理顶部UI显示系统失败: {ex.Message}");
                }

                try
                {
                    BlackRoseServer.Display.RightBottomDisplayManager.DisposeInstance();
                    result.AppendLine("✓ 右下角显示系统已清理");
                }
                catch (Exception ex)
                {
                    result.AppendLine($"✗ 清理右下角显示系统失败: {ex.Message}");
                }

                // 清空Hint管理器
                try
                {
                    BlackRoseServer.Manager.HintManager.Instance.ForceCleanupAllPositions();
                    result.AppendLine("✓ Hint管理器已清理");
                }
                catch (Exception ex)
                {
                    result.AppendLine($"✗ 清理Hint管理器失败: {ex.Message}");
                }

                // 清空出生保护
                try
                {
                    BlackRoseServer.Manager.SpawnProtectionManager.Instance.ClearAllProtections();
                    result.AppendLine("✓ 出生保护系统已清理");
                }
                catch (Exception ex)
                {
                    result.AppendLine($"✗ 清理出生保护系统失败: {ex.Message}");
                }

                result.AppendLine("所有系统缓存清理完成！");
                response = result.ToString();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"清空所有缓存失败: {ex.Message}");
                response = $"清空所有缓存失败: {ex.Message}";
                return false;
            }
        }
    }
}
