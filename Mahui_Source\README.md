# Mahui_Source
马辉插件

# 项目待办
## 远程管理系统

### 数据库表结构设计
1.AC表 (AC_Requests)
2.游戏内报告表 (Game_Reports)
3.AC返回表 (AC_Responses)
4.命令表 (Server_Commands)



### 机器人工作流程
轮询检查（5秒间隔）
扫描AC_Requests和Game_Reports表中is_completed=0的新记录
对未处理记录：
推送到指定频道
在内存和文件缓存中记录已推送UID

### 插件工作流程
AC响应处理
轮询AC_Responses表的新记录
将管理员回复发送至AC专属频道
当有玩家使用AC命令或者游戏内报告时拉入AC频道，在6分钟内或者回合结束前有效
关联的玩家在有效期内可见回复

命令执行
轮询Server_Commands表中is_completed=0的记录
执行命令后：
更新is_completed=1
记录execution_time
支持批量命令处理

## 封禁同步数据库
新增py程序每隔2分钟将封禁文件数据同步至数据库中对应的表